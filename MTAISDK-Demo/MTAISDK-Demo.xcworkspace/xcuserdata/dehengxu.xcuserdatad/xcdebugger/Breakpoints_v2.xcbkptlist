<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "5293E87F-6C45-4E8B-AFE7-0DABACCB8041"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "54C576FF-0C53-4DB7-BF00-C3F282F02368"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MTAISDK-Demo/ViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "163"
            endingLineNumber = "163">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "788FA23A-CDBC-4796-BF3E-80E3D4C26EB2"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MTAISDK-Demo/ViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "161"
            endingLineNumber = "161">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C1DEE49F-806D-47E9-B7CF-21B48D77F2B0"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MTAISDK-Demo/objc/MTAIEngineHelper.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "194"
            endingLineNumber = "194"
            landmarkName = "RecognizeGeneralObjectsInImage(image)"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
