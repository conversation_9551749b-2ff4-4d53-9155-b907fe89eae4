//
//  MTAIEngineHelper.h
//  MTAISDK-Demo
//
//  Created by DehengXu on 2025/7/28.
//

#ifndef MTAIEngineHelper_h
#define MTAIEngineHelper_h

#import <MTAiInterface/MTFaceModule/MTAiFace.h>
#import <MTAiInterface/MTFaceModule/MTAiFaceOption.h>
#import <MTAiInterface/MT3DFaceModule/MTAi3DFace.h>
#import <MTAiInterface/MTAiEngineFrame.h>
#import <MTAiInterface/MeituAiEngine.h>
#import <MTAiInterface/MTAiEngineOption.h>
#import <MTAiInterface/MTImageRecognitionModule/MTAiImageRecognition.h>
#import <MTAiInterface/MTImageRecognitionModule/MTAiImageRecognitionOption.h>
#import <MTAiInterface/MTImageRecognitionModule/MTAiImageRecognitionResult.h>

@interface MTAIEngineDelegator : NSObject <MTAiErrorCallbackDetegate>
@property MeituAiEngine *engine;

- (id)init;
- (MTAiEngineOption*)engineOption;

@end

NSDictionary* RecognizeGeneralObjectsInImage(UIImage *image);

void setupAIEngineObjc(void);
NSString* MTAiEyelidToString(MTAiEyelid eyelid);
CGRect GetImageFace(UIImage *image, MTAiEngineOption* option);
// 物体识别相关函数
NSArray<MTAiImageRecognition*>* RecognizeObjectsInImage(UIImage *image);
NSString* GetObjectCategoryName(int category, MTAiImageRecognitionLabelLevelType labelLevel, BOOL inEnglish);

// 物体识别测试函数
void TestObjectRecognition(void);

#endif /* MTAIEngineHelper_h */
