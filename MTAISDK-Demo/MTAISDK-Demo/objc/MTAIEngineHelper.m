//
//  MTAIEngineHelper.m
//  MTAISDK-Demo
//
//  Created by DehengXu on 2025/7/28.
//

#import <Foundation/Foundation.h>
#import "MTAIEngineHelper.h"

//#import <libmtaiinterface/MeituAiEngine.h>
//#import "Pods_MTAISDK_Demo/Pods-MTAISDK-Demo-umbrella.h"
//#import <mtlabrecord/basic_define.h>
//#import <mtlabrecord/record_header.h>

@implementation MTAIEngineDelegator

- (id)init {
    self = [super init];
    if (self) {
        self.engine = [[MeituAiEngine alloc] initWithMode:MTDetectorMode_IMAGE];
        [self.engine SetErrorCallbackWithObj:self];
        
        NSString *modelDir = [[NSString alloc] initWithFormat:@"%@/MTAiModel", [[NSBundle mainBundle] resourcePath]];
        [self.engine setModel:modelDir];

        MTAiEngineOption *option = self.engineOption;
        BOOL moduleStatus = [self.engine registerModule:option];
        NSLog(@"register module status: %d", moduleStatus);

    }
    return self;
}

- (void)AiErrorCallbackWithType:(MTAI_ERROR_TYPE)type AndWhat:(NSString *)what {
    NSLog(@"type: %ld, what: %@", type, what);
}

- (MTAiEngineOption*)engineOption {
    // 创建option对象
    MTAiEngineOption *option = [[MTAiEngineOption alloc] init];

    // 设置faceOption中的检测开关
    option.faceOption.mode = MT_FACE_MODULE_IMAGE_FD_FA;
    unsigned long long enableFlag = 0;
    enableFlag |= MT_FACE_ENABLE_FACE |
                  MT_FACE_ENABLE_REFINE_EYE |
                  MT_FACE_ENABLE_REFINE_MOUTH |
                  MT_FACE_ENABLE_POSEESTIMATION |
                  MT_FACE_ENABLE_VISIBILITY;
    option.faceOption.option = enableFlag;
    
    // 设置图像识别模块配置
    option.imageRecognitionOption = [[MTAiImageRecognitionOption alloc] init];
    option.imageRecognitionOption.option = MT_IMAGE_RECOGNITION_ENABLE_RECOGNITION;
    option.imageRecognitionOption.labelLevel = MT_IMAGE_RCOGNITION_MODULE_THIRD_LEVEL;
    option.imageRecognitionOption.mode = MT_IMAGE_RECOGNITION_SCENE_BASE;
    option.imageRecognitionOption.deviceType = MT_IMAGE_RECOGNITION_DEVICE_TYPE_HIGH;

    // ---------- begin set coreml-------------
    //使用本地模型（用智枢时，配置无效），且设置coreml
    option.faceOption.useCoreML= @{
        @(MT_FACE_ENABLE_FACE): @(YES),
        @(MT_FACE_ENABLE_FR) : @(YES),
        @(MT_FACE_ENABLE_PARSING) : @(YES),
        @(MT_FACE_ENABLE_FD_CONTROL) : @(YES)
    };

    return option;
}

- (MTAiEngineOption*)faceOption {
    // 创建option对象
    MTAiEngineOption *option = [[MTAiEngineOption alloc] init];

    // 设置faceOption中的检测开关
    option.faceOption.mode = MT_FACE_MODULE_IMAGE_FD_FA;
    unsigned long long enableFlag = 0;
    enableFlag |= MT_FACE_ENABLE_FACE |
                  MT_FACE_ENABLE_REFINE_EYE |
                  MT_FACE_ENABLE_REFINE_MOUTH |
                  MT_FACE_ENABLE_POSEESTIMATION |
                  MT_FACE_ENABLE_VISIBILITY;
    option.faceOption.option = enableFlag;

    //使用本地模型（用智枢时，配置无效），且设置coreml
    option.faceOption.useCoreML= @{
        @(MT_FACE_ENABLE_FACE): @(YES),
        @(MT_FACE_ENABLE_FR) : @(YES),
        @(MT_FACE_ENABLE_PARSING) : @(YES),
        @(MT_FACE_ENABLE_FD_CONTROL) : @(YES)
    };
    return option;
}

- (MTAiEngineOption*)recognitionOption {
    // 创建option对象
    MTAiEngineOption *option = [[MTAiEngineOption alloc] init];

    // 设置图像识别模块配置
    option.imageRecognitionOption = [[MTAiImageRecognitionOption alloc] init];
    option.imageRecognitionOption.option = MT_IMAGE_RECOGNITION_ENABLE_RECOGNITION;
    option.imageRecognitionOption.labelLevel = MT_IMAGE_RCOGNITION_MODULE_THIRD_LEVEL;
    option.imageRecognitionOption.mode = MT_IMAGE_RECOGNITION_SCENE_BASE;
    option.imageRecognitionOption.deviceType = MT_IMAGE_RECOGNITION_DEVICE_TYPE_HIGH;

    //使用本地模型（用智枢时，配置无效），且设置coreml
    option.faceOption.useCoreML= @{
        @(MT_FACE_ENABLE_FACE): @(YES),
        @(MT_FACE_ENABLE_FR) : @(YES),
        @(MT_FACE_ENABLE_PARSING) : @(YES),
        @(MT_FACE_ENABLE_FD_CONTROL) : @(YES)
    };
    return option;
}

@end

static MeituAiEngine *_engine;

NSDictionary* RecognizeGeneralObjectsInImage(UIImage *image) {
    MTAIEngineDelegator *delegator = [MTAIEngineDelegator new];
    NSMutableDictionary *result = [@{
        @"face": [@{
            @"face_size": @0,
            @"face_rectangle": [@[] mutableCopy]
        } mutableCopy],
        @"image_recognition_result": [@{
            @"first": @0,
            @"second": @0,
            @"third": @0
        } mutableCopy]
    } mutableCopy];
    
    if (delegator.engine == nil || image == nil) {
        NSLog(@"AI引擎未初始化或图片为空");
        return result;
    }
    
    // 获取图片的像素数据
    CGImageRef imageRef = image.CGImage;
    NSUInteger width = CGImageGetWidth(imageRef);
    NSUInteger height = CGImageGetHeight(imageRef);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    NSUInteger bytesPerPixel = 4;
    NSUInteger bytesPerRow = bytesPerPixel * width;
    NSUInteger bitsPerComponent = 8;
    UInt8 *rawData = (UInt8 *)calloc(height * width * bytesPerPixel, sizeof(UInt8));
    CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                              bitsPerComponent, bytesPerRow, colorSpace,
                                              kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
    CGColorSpaceRelease(colorSpace);
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), imageRef);
    CGContextRelease(context);
    
    // 创建AI引擎图像对象
    int orientation = 2;
    int stride = (int)(width * bytesPerPixel);
    MTAiEngineImage *aiImage = [[MTAiEngineImage alloc] initWithRgbaData:rawData width:width height:height orientation:orientation stride:stride];
    MTAiEngineFrame *frame = [[MTAiEngineFrame alloc] initWithImage:aiImage];
    
    // 创建包含人脸检测和图像识别的配置
    MTAiEngineOption *option = delegator.engineOption;
        
    // 执行检测和识别
    MTAiEngineResult *engineResult = [delegator.engine runWithFrame:frame option:option orientation:orientation];
    
    // 处理人脸检测结果
    MTAiFaceResult *faceResult = engineResult.faceResult;
    if (faceResult && faceResult.faces.count > 0) {
        NSMutableArray *faceRectangles = [@[] mutableCopy];
        
        for (MTAiFace *face in faceResult.faces) {
            CGRect faceBounds = face.faceBounds;
            // 计算人脸框体在图片中的占比
            double w_ratio = faceBounds.size.width / width;
            double h_ratio = faceBounds.size.height / height;
            
            NSDictionary *faceRectangle = @{
                @"w_ratio": @(w_ratio),
                @"h_ratio": @(h_ratio)
            };
            [faceRectangles addObject:faceRectangle];
        }
        
        // 更新人脸信息
        NSMutableDictionary *faceInfo = result[@"face"];
        faceInfo[@"face_size"] = @(faceResult.faces.count);
        faceInfo[@"face_rectangle"] = faceRectangles;
    }
    
    // 处理图像识别结果
    MTAiImageRecognitionResult *recognitionResult = engineResult.imageRecognitionResult;
    if (recognitionResult && recognitionResult.thirdLevelRecognitions.count > 0) {
        // 获取置信度最高的识别结果
        MTAiImageRecognition *topRecognition = nil;
        float maxScore = 0.0f;
        topRecognition = recognitionResult.thirdLevelRecognitions.firstObject;
        maxScore = topRecognition.score;
        //recognitionResult.thirdLevelRecognitions 已经排过序，最高分排在第一位。
//        for (MTAiImageRecognition *recognition in recognitionResult.thirdLevelRecognitions) {
//            if (recognition.score > maxScore) {
//                maxScore = recognition.score;
//                topRecognition = recognition;
//            }
//        }
        
        if (topRecognition) {
            // 获取三级标签信息
            // 这里需要根据实际的标签体系来解析 category 到三级标签
            // 暂时使用 category 值作为 third 级别，first 和 second 需要根据实际标签体系计算
            //int category = topRecognition.category;
            
            // 简化的三级标签计算（实际使用时需要根据标签体系调整）
            int first = topRecognition.firstCategory;  // 一级标签
            int second = topRecognition.secondCategory;   // 二级标签
            int third =  topRecognition.category;  // 三级标签
            
            NSMutableDictionary *recognitionInfo = result[@"image_recognition_result"];
            recognitionInfo[@"first"] = @(first);
            recognitionInfo[@"second"] = @(second);
            recognitionInfo[@"third"] = @(third);
        }
    }
    
    // 释放内存
    free(rawData);
    
    // 将 result 转换为 JSON 字符串
    NSString *resultString = @"";
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:result
                                                       options:NSJSONWritingPrettyPrinted
                                                         error:&error];
    if (jsonData && !error) {
        resultString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    } else {
        NSLog(@"JSON 序列化错误: %@", error.localizedDescription);
        resultString = @"{}";
    }
    
    NSLog(@"识别结果 JSON: %@", resultString);
    
    return result;
}

// 将MTAiFaceAction转换为NSString的函数
static NSString* MTAiFaceActionToString(MTAiFaceAction action) {
    NSMutableString *result = [NSMutableString string];
    
    if (action.isEyeBlink) [result appendString:@"眨眼 "];
    if (action.isLeftEyeClose) [result appendString:@"闭左眼 "];
    if (action.isRightEyeClose) [result appendString:@"闭右眼 "];
    if (action.isEyeBrowUp) [result appendString:@"挑眉 "];
    if (action.isMouthOpen) [result appendString:@"张嘴 "];
    if (action.isKiss) [result appendString:@"Kiss "];
    if (action.isNod) [result appendString:@"点头 "];
    if (action.isHeadTurnLeft) [result appendString:@"左转头 "];
    if (action.isHeadTurnRight) [result appendString:@"右转头 "];
    if (action.isHeadRaiseUp) [result appendString:@"抬头 "];
    if (action.isHeadFallDown) [result appendString:@"低头 "];
    
    return [result length] > 0 ? result : @"无动作";
}

void setupAIEngineObjc() {
    // 创建Ai引擎对象
    MTAIEngineDelegator *delegator = [MTAIEngineDelegator new];
//    MeituAiEngine *engine = [[MeituAiEngine alloc] initWithMode:MTDetectorMode_IMAGE];
//    [engine SetErrorCallbackWithObj:delegator];
    
    // 设置模型路径, 也可以通过setSingleModelPath:forKey:接口单独设置某个模型的路径(优先级最高)
    NSString *modelDir = [[NSString alloc] initWithFormat:@"%@/MTAiModel", [[NSBundle mainBundle] resourcePath]];
    [delegator.engine setModel:modelDir];

    // 创建option对象
    MTAiEngineOption *option = [delegator engineOption];
            
            // {
            //     NSString *path = [NSString stringWithFormat:@"%@/FaceDetectModel/mtface_fr.manisc", modelDir];
            //     [delegator.engine setSingleModelPath:path forKey:MTAIENGINE_MODEL_FACE_FR];
            // }
            // {
            //     NSString *path = [NSString stringWithFormat:@"%@/FaceDetectModel/mtface_parsing.manisc", modelDir];
            //     [delegator.engine setSingleModelPath:path forKey:MTAIENGINE_MODEL_FACE_PARSING];
            // }

    //---------------end set coreml ----------


    // 注册option中打开的模块
    BOOL moduleStatus = [delegator.engine registerModule:option];
    NSLog(@"register module status: %d", moduleStatus);
    _engine = delegator.engine;
    
    // 测试物体识别功能
    UIImage *testImage = [UIImage imageNamed:@"output.jpg"];
    if (testImage) {
        NSArray<MTAiImageRecognition*> *recognitions = RecognizeObjectsInImage(testImage);
        NSLog(@"物体识别测试完成，识别到 %lu 个类别", (unsigned long)recognitions.count);
    }

    // 加载图片
    UIImage *image = [UIImage imageNamed:@"output.jpg"];
    CGRect face = GetImageFace(image, option);
    NSLog(@"face: %@", NSStringFromCGRect(face));
}

CGRect GetImageFace(UIImage *image, MTAiEngineOption* option) {
    if (_engine == nil) {
        return CGRectZero;
    }
    // PNG格式的像素格式为RGBA, 每个通道8位, 总共32位
      // 将 jpg 图片转换为 NSData
      NSData *jpgData = UIImageJPEGRepresentation(image, 1.0);
      // 获取图片的像素数据
      CGImageRef imageRef = image.CGImage;
      NSUInteger width = CGImageGetWidth(imageRef);
      NSUInteger height = CGImageGetHeight(imageRef);
      CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
      NSUInteger bytesPerPixel = 4;
      NSUInteger bytesPerRow = bytesPerPixel * width;
      NSUInteger bitsPerComponent = 8;
      UInt8 *rawData = (UInt8 *)calloc(height * width * bytesPerPixel, sizeof(UInt8));
      CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                                bitsPerComponent, bytesPerRow, colorSpace,
                                                kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
      CGColorSpaceRelease(colorSpace);
      CGContextDrawImage(context, CGRectMake(0, 0, width, height), imageRef);
      CGContextRelease(context);

//    NSData *pData = UIImagePNGRepresentation(image);
//    int width = (int)image.size.width;
//    int height = (int)image.size.height;
    int orientation = 1;
    int stride = (int)(width * bytesPerPixel);
    // 检测帧信息
    MTAiEngineImage *aiImage = [[MTAiEngineImage alloc] initWithRgbaData:rawData width:width height:height orientation:orientation stride:stride];
    MTAiEngineFrame *frame = [[MTAiEngineFrame alloc] initWithImage:aiImage];

    // 检测并获取结果, 此处option可以重新声明对象, 也可以复用注册的option
    MTAiEngineResult *result = [_engine runWithFrame:frame option:option];
    MTAiFaceResult *faceResult = result.faceResult;
    MTAiFace *face = faceResult.faces.lastObject;

    
    NSLog(@"faces bounds: %@, action: %@, points: %@", NSStringFromCGRect(face.faceBounds), MTAiFaceActionToString(face.faceAction), face.facePoints);
//    NSDictionary *dict = [engine getCurrentModelsName:option];
    return face.faceBounds;
}

NSString* MTAiEyelidToString(MTAiEyelid eyelid) {
    return [NSString stringWithFormat:@"左眼类型:%d, 右眼类型:%d, 左眼单眼皮分数:%.2f, 左眼双眼皮分数:%.2f, 左眼内双分数:%.2f, 右眼单眼皮分数:%.2f, 右眼双眼皮分数:%.2f, 右眼内双分数:%.2f",
            eyelid.left,
            eyelid.right,
            eyelid.leftSingleScore,
            eyelid.leftDoubleScore,
            eyelid.leftDoubleInsideScore,
            eyelid.rightSingleScore,
            eyelid.rightDoubleScore,
            eyelid.rightDoubleInsideScore];
}

// MARK: - 物体识别功能实现

NSArray<MTAiImageRecognition*>* RecognizeObjectsInImage(UIImage *image) {
    if (_engine == nil || image == nil) {
        NSLog(@"AI引擎未初始化或图片为空");
        return @[];
    }
    
    // 获取图片的像素数据
    CGImageRef imageRef = image.CGImage;
    NSUInteger width = CGImageGetWidth(imageRef);
    NSUInteger height = CGImageGetHeight(imageRef);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    NSUInteger bytesPerPixel = 4;
    NSUInteger bytesPerRow = bytesPerPixel * width;
    NSUInteger bitsPerComponent = 8;
    UInt8 *rawData = (UInt8 *)calloc(height * width * bytesPerPixel, sizeof(UInt8));
    CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                              bitsPerComponent, bytesPerRow, colorSpace,
                                              kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
    CGColorSpaceRelease(colorSpace);
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), imageRef);
    CGContextRelease(context);
    
    // 创建AI引擎图像对象
    int orientation = 1;
    int stride = (int)(width * bytesPerPixel);
    MTAiEngineImage *aiImage = [[MTAiEngineImage alloc] initWithRgbaData:rawData width:width height:height orientation:orientation stride:stride];
    MTAiEngineFrame *frame = [[MTAiEngineFrame alloc] initWithImage:aiImage];
    
    // 创建图像识别配置
    MTAiEngineOption *option = [[MTAiEngineOption alloc] init];
    option.imageRecognitionOption = [[MTAiImageRecognitionOption alloc] init];
    option.imageRecognitionOption.option = MT_IMAGE_RECOGNITION_ENABLE_RECOGNITION;
    option.imageRecognitionOption.labelLevel = MT_IMAGE_RCOGNITION_MODULE_THIRD_LEVEL;
    option.imageRecognitionOption.mode = MT_IMAGE_RECOGNITION_SCENE_BASE;
    option.imageRecognitionOption.deviceType = MT_IMAGE_RECOGNITION_DEVICE_TYPE_HIGH;
    
    // 执行识别
    MTAiEngineResult *result = [_engine runWithFrame:frame option:option];
    MTAiImageRecognitionResult *recognitionResult = result.imageRecognitionResult;
    
    // 释放内存
    free(rawData);
    
    if (recognitionResult && recognitionResult.thirdLevelRecognitions) {
        NSLog(@"识别到 %lu 个物体类别", (unsigned long)recognitionResult.thirdLevelRecognitions.count);
        
        // 按置信度排序
        NSArray *sortedResults = [recognitionResult.thirdLevelRecognitions sortedArrayUsingComparator:^NSComparisonResult(MTAiImageRecognition *obj1, MTAiImageRecognition *obj2) {
            if (obj1.score > obj2.score) {
                return NSOrderedAscending;
            } else if (obj1.score < obj2.score) {
                return NSOrderedDescending;
            }
            return NSOrderedSame;
        }];
        
        // 打印识别结果
        for (MTAiImageRecognition *recognition in sortedResults) {
            NSString *categoryName = GetObjectCategoryName(recognition.category, MT_IMAGE_RCOGNITION_MODULE_THIRD_LEVEL, NO);
            NSLog(@"识别结果: %@ (置信度: %.3f)", categoryName, recognition.score);
        }
        
        return sortedResults;
    } else {
        NSLog(@"未识别到任何物体");
        return @[];
    }
}

NSString* GetObjectCategoryName(int category, MTAiImageRecognitionLabelLevelType labelLevel, BOOL inEnglish) {
    NSString *categoryName = [MTAiImageRecognitionResult GetImageRecognitionLabel:category labelLevel:labelLevel inEnglish:inEnglish];
    return categoryName ? categoryName : [NSString stringWithFormat:@"未知类别(%d)", category];
}

// MARK: - 物体识别测试函数

void TestObjectRecognition(void) {
    NSLog(@"=== 开始物体识别测试 ===");
    
    // 检查AI引擎是否已初始化
    if (_engine == nil) {
        NSLog(@"❌ AI引擎未初始化，请先调用setupAIEngineObjc");
        return;
    }
    
    // 加载测试图片
    UIImage *testImage = [UIImage imageNamed:@"output.jpg"];
    if (!testImage) {
        NSLog(@"❌ 无法加载测试图片 output.jpg");
        return;
    }
    
    NSLog(@"✅ 成功加载测试图片，尺寸: %.0fx%.0f", testImage.size.width, testImage.size.height);
    
    // 执行物体识别
    NSArray<MTAiImageRecognition*> *recognitions = RecognizeObjectsInImage(testImage);
    
    if (recognitions.count == 0) {
        NSLog(@"⚠️ 未识别到任何物体");
    } else {
        NSLog(@"✅ 识别成功！共识别到 %lu 个物体类别:", (unsigned long)recognitions.count);
        
        // 显示前5个识别结果
        NSInteger maxResults = recognitions.count; //MIN(5, recognitions.count);
        for (NSInteger i = 0; i < maxResults; i++) {
            MTAiImageRecognition *recognition = recognitions[i];
            
            // 获取中文和英文类别名称
            NSString *chineseName = GetObjectCategoryName(recognition.category, MT_IMAGE_RCOGNITION_MODULE_THIRD_LEVEL, NO);
            NSString *englishName = GetObjectCategoryName(recognition.category, MT_IMAGE_RCOGNITION_MODULE_THIRD_LEVEL, YES);
            
            NSLog(@"  %ld. %@ (%@) - 置信度: %.3f", 
                  (long)(i + 1), chineseName, englishName, recognition.score);
        }
        
        if (recognitions.count > maxResults) {
            NSLog(@"  ... 还有 %lu 个其他识别结果", (unsigned long)(recognitions.count - maxResults));
        }
    }
    
    NSLog(@"=== 物体识别测试完成 ===");
}
