//
//  ViewController.swift
//  MTAISDK-Demo
//
//  Created by DehengXu on 2025/7/28.
//

import UIKit
import SnapKit

class ViewController: UIViewController {
    
    private var imageView: UIImageView!
    private var faceButton: UIButton!

    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
        setupImageView()
        if let image = UIImage(named: "output.jpg") {
            let result = RecognizeGeneralObjectsInImage(image)
            print("result: \(result)")
        }
    }
    
    func setupImageView() {
        // 创建 UIImageView
        imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        
        // 加载 output.jpg 图片
        if let image = UIImage(named: "output.jpg") {
            imageView.image = image
        }
        
        // 添加到视图
        view.addSubview(imageView)
        
        // 使用 SnapKit 设置约束
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-50)
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        // 创建 Face 按钮
        faceButton = UIButton(type: .system)
        faceButton.setTitle("Face", for: .normal)
        faceButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        faceButton.backgroundColor = UIColor.systemBlue
        faceButton.setTitleColor(.white, for: .normal)
        faceButton.layer.cornerRadius = 8
        
        // 添加按钮到视图
        view.addSubview(faceButton)
        
        // 使用 SnapKit 设置按钮约束
        faceButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(imageView.snp.bottom).offset(30)
            make.width.equalTo(120)
            make.height.equalTo(44)
        }
    }
    
    func setupAIEngine() {
        // 创建Ai引擎对象
        let engine = MeituAiEngine(mode: MTDetectorMode(rawValue: 0))!
        
        // 创建option对象
        let option = MTAiEngineOption()!
        
        // 设置faceOption中的检测开关
        option.faceOption.mode = MTAiFaceModeType.FACE_MODULE_IMAGE_FD_FA //MT_FACE_MODULE_IMAGE_FD_FA
        var enableFlag: UInt64 = 0
        enableFlag |= MTAiFaceOptionType._FACE_ENABLE_FACE.rawValue
        enableFlag |= MTAiFaceOptionType._FACE_ENABLE_REFINE_EYE.rawValue
        enableFlag |= MTAiFaceOptionType._FACE_ENABLE_REFINE_MOUTH.rawValue
        enableFlag |= MTAiFaceOptionType._FACE_ENABLE_POSEESTIMATION.rawValue
        enableFlag |= MTAiFaceOptionType._FACE_ENABLE_VISIBILITY.rawValue
        option.faceOption.option = MTAiFaceOptionType(rawValue: enableFlag)
        
        // ---------- begin set coreml-------------
        // 使用本地模型（用智枢时，配置无效），且设置coreml
        option.faceOption.useCoreML = [
            NSNumber(value: MTAiFaceOptionType._FACE_ENABLE_FACE.rawValue): NSNumber(value: true),
            NSNumber(value: MTAiFaceOptionType._FACE_ENABLE_FR.rawValue): NSNumber(value: true),
            NSNumber(value: MTAiFaceOptionType._FACE_ENABLE_PARSING.rawValue): NSNumber(value: true)
        ]
        
        if false {
            // 设置模型路径, 也可以通过setSingleModelPath:forKey:接口单独设置某个模型的路径(优先级最高)
            let modelDir = "\(Bundle.main.resourcePath!)/MTAiModel"
            engine.setModel(modelDir)
            
            do {
                let path = "\(modelDir)/FaceDetectModel/mtface_fr.manisc"
                let type = engine.setSingleModelPath(path, forKey: MTAIENGINE_MODEL_FACE_FR)!
                print("type: \(type)")
            }
            
            do {
                let path = "\(modelDir)/FaceDetectModel/mtface_parsing.manisc"
                let type = engine.setSingleModelPath(path, forKey: MTAIENGINE_MODEL_FACE_PARSING)!
                print("type: \(type)")
            }
            
            // ---------------end set coreml ----------
            
            // 注册option中打开的模块
            let registerdStatus = engine.registerModule(option)
            print("registerdStatus: \(registerdStatus)")
        }
        
        // 检测帧信息示例（需要实际的图像数据）
        //bundle 加载 output.jpg
        let uiImage = UIImage(named: "output.jpg")!
        let imageData = uiImage.pngData()!
        let pData = imageData.withUnsafeBytes { (bytes: UnsafeRawBufferPointer) -> UnsafePointer<UInt8> in
            return bytes.baseAddress!.assumingMemoryBound(to: UInt8.self)
        }
        //UnsafePointer to UnsafeMutablePointer
        let pDataMutable = UnsafeMutablePointer<UInt8>(mutating: pData)
        
        let width = uiImage.size.width
        let height = uiImage.size.height
        let orientation = 1 //MTAiEngineImageOrientation.up
        let stride = width * 4

        let image = MTAiEngineImage(rgbaData: pDataMutable, width: Int32(width), height: Int32(height), orientation: Int32(1), stride: Int32(stride))
        
//        let image = MTAiEngineImage(rgbaData: pData, width: Int32(width), height: Int32(height), orientation: Int32(orientation), stride: Int32(stride))
        let frame = MTAiEngineFrame(image: image)
        
        // 检测并获取结果, 此处option可以重新声明对象, 也可以复用注册的option
        let result = engine.run(with: frame, option: option)!
        print("result: \(result)")
        print("result: \(result.imageDetectionResult.imageDetections)")
        // 注销人脸模块
        // engine.unregisterModule(kMTAiEngineType_faceModule)
    }

}

