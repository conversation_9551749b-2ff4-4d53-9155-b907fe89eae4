// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		2C49D14E42D09ED7FB96B2DA /* Pods_MTAISDK_DemoTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C28662E0286DE4913F2CBA14 /* Pods_MTAISDK_DemoTests.framework */; };
		8165BC0291BB44587151010F /* Pods_MTAISDK_Demo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AA4EE5A532C5E7563C300579 /* Pods_MTAISDK_Demo.framework */; };
		EA789FD8BFB1D49A9767D4A6 /* Pods_MTAISDK_Demo_MTAISDK_DemoUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E727A834B4E092F8FC6B47EF /* Pods_MTAISDK_Demo_MTAISDK_DemoUITests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		24A798E62E3718C400109F03 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 24A798C72E3718C300109F03 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 24A798CE2E3718C300109F03;
			remoteInfo = "MTAISDK-Demo";
		};
		24A798F02E3718C400109F03 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 24A798C72E3718C300109F03 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 24A798CE2E3718C300109F03;
			remoteInfo = "MTAISDK-Demo";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		24A798CF2E3718C300109F03 /* MTAISDK-Demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "MTAISDK-Demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		24A798E52E3718C400109F03 /* MTAISDK-DemoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "MTAISDK-DemoTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		24A798EF2E3718C400109F03 /* MTAISDK-DemoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "MTAISDK-DemoUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		3B303165BB9FB4BD1B54DA0E /* Pods-MTAISDK-Demo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MTAISDK-Demo.debug.xcconfig"; path = "Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo.debug.xcconfig"; sourceTree = "<group>"; };
		41448F0E063643646C6EC606 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig"; path = "Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig"; sourceTree = "<group>"; };
		615AC7F99B73F27585E0754F /* Pods-MTAISDK-DemoTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MTAISDK-DemoTests.release.xcconfig"; path = "Target Support Files/Pods-MTAISDK-DemoTests/Pods-MTAISDK-DemoTests.release.xcconfig"; sourceTree = "<group>"; };
		8B7F6A6FBF76CBF8BCBBF64C /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig"; path = "Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig"; sourceTree = "<group>"; };
		AA4EE5A532C5E7563C300579 /* Pods_MTAISDK_Demo.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MTAISDK_Demo.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AE684367A623383C09A770EB /* Pods-MTAISDK-DemoTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MTAISDK-DemoTests.debug.xcconfig"; path = "Target Support Files/Pods-MTAISDK-DemoTests/Pods-MTAISDK-DemoTests.debug.xcconfig"; sourceTree = "<group>"; };
		C28662E0286DE4913F2CBA14 /* Pods_MTAISDK_DemoTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MTAISDK_DemoTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E727A834B4E092F8FC6B47EF /* Pods_MTAISDK_Demo_MTAISDK_DemoUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MTAISDK_Demo_MTAISDK_DemoUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F5DCC1F41BB7DF17B8D57C1D /* Pods-MTAISDK-Demo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MTAISDK-Demo.release.xcconfig"; path = "Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		24A798F72E3718C400109F03 /* Exceptions for "MTAISDK-Demo" folder in "MTAISDK-Demo" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 24A798CE2E3718C300109F03 /* MTAISDK-Demo */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		24A798D12E3718C300109F03 /* MTAISDK-Demo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				24A798F72E3718C400109F03 /* Exceptions for "MTAISDK-Demo" folder in "MTAISDK-Demo" target */,
			);
			path = "MTAISDK-Demo";
			sourceTree = "<group>";
		};
		24A798E82E3718C400109F03 /* MTAISDK-DemoTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
			);
			path = "MTAISDK-DemoTests";
			sourceTree = "<group>";
		};
		24A798F22E3718C400109F03 /* MTAISDK-DemoUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
			);
			path = "MTAISDK-DemoUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		24A798CC2E3718C300109F03 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8165BC0291BB44587151010F /* Pods_MTAISDK_Demo.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		24A798E22E3718C400109F03 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2C49D14E42D09ED7FB96B2DA /* Pods_MTAISDK_DemoTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		24A798EC2E3718C400109F03 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EA789FD8BFB1D49A9767D4A6 /* Pods_MTAISDK_Demo_MTAISDK_DemoUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		24A798C62E3718C300109F03 = {
			isa = PBXGroup;
			children = (
				24A798D12E3718C300109F03 /* MTAISDK-Demo */,
				24A798E82E3718C400109F03 /* MTAISDK-DemoTests */,
				24A798F22E3718C400109F03 /* MTAISDK-DemoUITests */,
				24A798D02E3718C300109F03 /* Products */,
				270664417F4F88322DE63B5D /* Pods */,
				A1D92668928FB31F205DD6E0 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		24A798D02E3718C300109F03 /* Products */ = {
			isa = PBXGroup;
			children = (
				24A798CF2E3718C300109F03 /* MTAISDK-Demo.app */,
				24A798E52E3718C400109F03 /* MTAISDK-DemoTests.xctest */,
				24A798EF2E3718C400109F03 /* MTAISDK-DemoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		270664417F4F88322DE63B5D /* Pods */ = {
			isa = PBXGroup;
			children = (
				3B303165BB9FB4BD1B54DA0E /* Pods-MTAISDK-Demo.debug.xcconfig */,
				F5DCC1F41BB7DF17B8D57C1D /* Pods-MTAISDK-Demo.release.xcconfig */,
				8B7F6A6FBF76CBF8BCBBF64C /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig */,
				41448F0E063643646C6EC606 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig */,
				AE684367A623383C09A770EB /* Pods-MTAISDK-DemoTests.debug.xcconfig */,
				615AC7F99B73F27585E0754F /* Pods-MTAISDK-DemoTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		A1D92668928FB31F205DD6E0 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				AA4EE5A532C5E7563C300579 /* Pods_MTAISDK_Demo.framework */,
				E727A834B4E092F8FC6B47EF /* Pods_MTAISDK_Demo_MTAISDK_DemoUITests.framework */,
				C28662E0286DE4913F2CBA14 /* Pods_MTAISDK_DemoTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		24A798CE2E3718C300109F03 /* MTAISDK-Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 24A798F82E3718C400109F03 /* Build configuration list for PBXNativeTarget "MTAISDK-Demo" */;
			buildPhases = (
				F20C44EE889E51CE4BC08A56 /* [CP] Check Pods Manifest.lock */,
				24A798CB2E3718C300109F03 /* Sources */,
				24A798CC2E3718C300109F03 /* Frameworks */,
				24A798CD2E3718C300109F03 /* Resources */,
				6335E829CBFBC0AC164D83DC /* [CP] Embed Pods Frameworks */,
				D4A94CB9D517D64B035A07DD /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				24A798D12E3718C300109F03 /* MTAISDK-Demo */,
			);
			name = "MTAISDK-Demo";
			productName = "MTAISDK-Demo";
			productReference = 24A798CF2E3718C300109F03 /* MTAISDK-Demo.app */;
			productType = "com.apple.product-type.application";
		};
		24A798E42E3718C400109F03 /* MTAISDK-DemoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 24A798FD2E3718C400109F03 /* Build configuration list for PBXNativeTarget "MTAISDK-DemoTests" */;
			buildPhases = (
				BE3FB0C2758E46822CE9C5D9 /* [CP] Check Pods Manifest.lock */,
				24A798E12E3718C400109F03 /* Sources */,
				24A798E22E3718C400109F03 /* Frameworks */,
				24A798E32E3718C400109F03 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				24A798E72E3718C400109F03 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				24A798E82E3718C400109F03 /* MTAISDK-DemoTests */,
			);
			name = "MTAISDK-DemoTests";
			productName = "MTAISDK-DemoTests";
			productReference = 24A798E52E3718C400109F03 /* MTAISDK-DemoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		24A798EE2E3718C400109F03 /* MTAISDK-DemoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 24A799002E3718C400109F03 /* Build configuration list for PBXNativeTarget "MTAISDK-DemoUITests" */;
			buildPhases = (
				E9811595653135FF758C1686 /* [CP] Check Pods Manifest.lock */,
				24A798EB2E3718C400109F03 /* Sources */,
				24A798EC2E3718C400109F03 /* Frameworks */,
				24A798ED2E3718C400109F03 /* Resources */,
				9A572E929235D1245AE7EF22 /* [CP] Embed Pods Frameworks */,
				8C4AFAEF14B3A659AAD7952F /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				24A798F12E3718C400109F03 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				24A798F22E3718C400109F03 /* MTAISDK-DemoUITests */,
			);
			name = "MTAISDK-DemoUITests";
			productName = "MTAISDK-DemoUITests";
			productReference = 24A798EF2E3718C400109F03 /* MTAISDK-DemoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		24A798C72E3718C300109F03 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					24A798CE2E3718C300109F03 = {
						CreatedOnToolsVersion = 16.4;
						LastSwiftMigration = 1640;
					};
					24A798E42E3718C400109F03 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 24A798CE2E3718C300109F03;
					};
					24A798EE2E3718C400109F03 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 24A798CE2E3718C300109F03;
					};
				};
			};
			buildConfigurationList = 24A798CA2E3718C300109F03 /* Build configuration list for PBXProject "MTAISDK-Demo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 24A798C62E3718C300109F03;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 24A798D02E3718C300109F03 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				24A798CE2E3718C300109F03 /* MTAISDK-Demo */,
				24A798E42E3718C400109F03 /* MTAISDK-DemoTests */,
				24A798EE2E3718C400109F03 /* MTAISDK-DemoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		24A798CD2E3718C300109F03 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		24A798E32E3718C400109F03 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		24A798ED2E3718C400109F03 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		6335E829CBFBC0AC164D83DC /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8C4AFAEF14B3A659AAD7952F /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9A572E929235D1245AE7EF22 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		BE3FB0C2758E46822CE9C5D9 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MTAISDK-DemoTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D4A94CB9D517D64B035A07DD /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E9811595653135FF758C1686 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F20C44EE889E51CE4BC08A56 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MTAISDK-Demo-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		24A798CB2E3718C300109F03 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		24A798E12E3718C400109F03 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		24A798EB2E3718C400109F03 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		24A798E72E3718C400109F03 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 24A798CE2E3718C300109F03 /* MTAISDK-Demo */;
			targetProxy = 24A798E62E3718C400109F03 /* PBXContainerItemProxy */;
		};
		24A798F12E3718C400109F03 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 24A798CE2E3718C300109F03 /* MTAISDK-Demo */;
			targetProxy = 24A798F02E3718C400109F03 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		24A798F92E3718C400109F03 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B303165BB9FB4BD1B54DA0E /* Pods-MTAISDK-Demo.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEPLOYMENT_LOCATION = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "MTAISDK-Demo/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "MTAISDK-Demo";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.meitu.aisdk-demo";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "MTAISDK-Demo/objc/MTAISDK-Demo-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		24A798FA2E3718C400109F03 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F5DCC1F41BB7DF17B8D57C1D /* Pods-MTAISDK-Demo.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEPLOYMENT_LOCATION = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "MTAISDK-Demo/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "MTAISDK-Demo";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.meitu.whee;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "MTAISDK-Demo/objc/MTAISDK-Demo-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		24A798FB2E3718C400109F03 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		24A798FC2E3718C400109F03 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		24A798FE2E3718C400109F03 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AE684367A623383C09A770EB /* Pods-MTAISDK-DemoTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.prodbits.apps.MTAISDK-DemoTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MTAISDK-Demo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MTAISDK-Demo";
			};
			name = Debug;
		};
		24A798FF2E3718C400109F03 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 615AC7F99B73F27585E0754F /* Pods-MTAISDK-DemoTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.prodbits.apps.MTAISDK-DemoTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MTAISDK-Demo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MTAISDK-Demo";
			};
			name = Release;
		};
		24A799012E3718C400109F03 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8B7F6A6FBF76CBF8BCBBF64C /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.prodbits.apps.MTAISDK-DemoUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "MTAISDK-Demo";
			};
			name = Debug;
		};
		24A799022E3718C400109F03 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 41448F0E063643646C6EC606 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.prodbits.apps.MTAISDK-DemoUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "MTAISDK-Demo";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		24A798CA2E3718C300109F03 /* Build configuration list for PBXProject "MTAISDK-Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				24A798FB2E3718C400109F03 /* Debug */,
				24A798FC2E3718C400109F03 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		24A798F82E3718C400109F03 /* Build configuration list for PBXNativeTarget "MTAISDK-Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				24A798F92E3718C400109F03 /* Debug */,
				24A798FA2E3718C400109F03 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		24A798FD2E3718C400109F03 /* Build configuration list for PBXNativeTarget "MTAISDK-DemoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				24A798FE2E3718C400109F03 /* Debug */,
				24A798FF2E3718C400109F03 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		24A799002E3718C400109F03 /* Build configuration list for PBXNativeTarget "MTAISDK-DemoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				24A799012E3718C400109F03 /* Debug */,
				24A799022E3718C400109F03 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 24A798C72E3718C300109F03 /* Project object */;
}
