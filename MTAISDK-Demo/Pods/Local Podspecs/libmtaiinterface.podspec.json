{"name": "libmtaiinterface", "version": "0.4.0.2010.********-OnlyIOS", "summary": "An Object-C interface of libmtai.", "description": "A long long description of libmtai.", "homepage": "http://techgit.meitu.com/MTlabBinarys/MTlabSDK/libmtai", "license": {"type": "Copyright", "text": "    © 2008-2019 Mei<PERSON>. All rights reserved.\n"}, "authors": {"dhk": "<EMAIL>"}, "platforms": {"ios": "8.0"}, "source": {"git": "*********************:MTlabBinarys/MTlabSDK/libmtai.git", "tag": "0.4.0.2010.********-OnlyIOS"}, "default_subspecs": "framework", "pod_target_xcconfig": {"EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64"}, "user_target_xcconfig": {"EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64"}, "subspecs": [{"name": "framework", "vendored_frameworks": "iOS/MTAiInterface.framework", "public_header_files": "iOS/MTAiInterface.framework/**/*.{h,hpp}", "source_files": "iOS/MTAiInterface.framework/**/*.{h,hpp}", "dependencies": {"mtlabrecord/shared": ["0.1.1"]}}, {"name": "framework_static", "vendored_frameworks": "iOS_static/MTAiInterface.framework", "public_header_files": "iOS_static/MTAiInterface.framework/**/*.{h,hpp}", "source_files": "iOS_static/MTAiInterface.framework/**/*.{h,hpp}", "dependencies": {"mtlabrecord/static": ["0.1.1"]}}, {"name": "MTAiMerakEyelidPhotoGL", "resources": "iOS/ModelModules/MTAiMerakEyelidPhotoGL/manis/MTAiModel"}, {"name": "MTAiMerakInnovationHairCurly", "resources": "iOS/ModelModules/MTAiMerakInnovationHairCurly/manis/MTAiModel"}, {"name": "MTAiMerakInnovationHairFluffy", "resources": "iOS/ModelModules/MTAiMerakInnovationHairFluffy/manis/MTAiModel"}, {"name": "MTAiMerakInnovationHairStraight", "resources": "iOS/ModelModules/MTAiMerakInnovationHairStraight/manis/MTAiModel"}, {"name": "3DFace3D", "resources": "iOS/ModelModules/MTAiModel3DFace3D/manis/MTAiModel"}, {"name": "Animal", "resources": "iOS/ModelModules/MTAiModelAnimal/manis/MTAiModel"}, {"name": "AuroraModel", "resources": "iOS/ModelModules/MTAiModelAuroraModel/manis/MTAiModel"}, {"name": "AuroraModelManisa", "resources": "iOS/ModelModules/MTAiModelAuroraModel/manisa/MTAiModel"}, {"name": "AuroraModelManisc", "resources": "iOS/ModelModules/MTAiModelAuroraModel/manisc/MTAiModel"}, {"name": "AuroraModelCamera", "resources": "iOS/ModelModules/MTAiModelAuroraModelCamera/manis/MTAiModel"}, {"name": "AuroraModelCameraManisa", "resources": "iOS/ModelModules/MTAiModelAuroraModelCamera/manisa/MTAiModel"}, {"name": "AuroraModelCameraManisc", "resources": "iOS/ModelModules/MTAiModelAuroraModelCamera/manisc/MTAiModel"}, {"name": "BodyHuman", "resources": "iOS/ModelModules/MTAiModelBodyHuman/manis/MTAiModel"}, {"name": "BodyInOneBox", "resources": "iOS/ModelModules/MTAiModelBodyInOneBox/manis/MTAiModel"}, {"name": "BodyInOneBoxYolo", "resources": "iOS/ModelModules/MTAiModelBodyInOneBoxYolo/manis/MTAiModel"}, {"name": "BodyInOneBreastImageLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageLarge/manis/MTAiModel"}, {"name": "BodyInOneBreastImageLargeManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageLarge/manisa/MTAiModel"}, {"name": "BodyInOneBreastImageLargeManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageLarge/manisc/MTAiModel"}, {"name": "BodyInOneBreastImageMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageMiddle/manis/MTAiModel"}, {"name": "BodyInOneBreastImageMiddleManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageMiddle/manisa/MTAiModel"}, {"name": "BodyInOneBreastImageMiddleManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageMiddle/manisc/MTAiModel"}, {"name": "BodyInOneBreastImageSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageSmall/manis/MTAiModel"}, {"name": "BodyInOneBreastImageSmallManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageSmall/manisa/MTAiModel"}, {"name": "BodyInOneBreastImageSmallManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastImageSmall/manisc/MTAiModel"}, {"name": "BodyInOneBreastVideoLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoLarge/manis/MTAiModel"}, {"name": "BodyInOneBreastVideoLargeManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoLarge/manisa/MTAiModel"}, {"name": "BodyInOneBreastVideoLargeManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoLarge/manisc/MTAiModel"}, {"name": "BodyInOneBreastVideoMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoMiddle/manis/MTAiModel"}, {"name": "BodyInOneBreastVideoMiddleManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoMiddle/manisa/MTAiModel"}, {"name": "BodyInOneBreastVideoMiddleManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoMiddle/manisc/MTAiModel"}, {"name": "BodyInOneBreastVideoSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoSmall/manis/MTAiModel"}, {"name": "BodyInOneBreastVideoSmallManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoSmall/manisa/MTAiModel"}, {"name": "BodyInOneBreastVideoSmallManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneBreastVideoSmall/manisc/MTAiModel"}, {"name": "BodyInOneContour", "resources": "iOS/ModelModules/MTAiModelBodyInOneContour/manis/MTAiModel"}, {"name": "BodyInOneMulti", "resources": "iOS/ModelModules/MTAiModelBodyInOneMulti/manis/MTAiModel"}, {"name": "BodyInOneMultiLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiLarge/manis/MTAiModel"}, {"name": "BodyInOneMultiLargeManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiLarge/manisa/MTAiModel"}, {"name": "BodyInOneMultiLargeManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiLarge/manisc/MTAiModel"}, {"name": "BodyInOneMultiMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiMiddle/manis/MTAiModel"}, {"name": "BodyInOneMultiMiddleManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiMiddle/manisa/MTAiModel"}, {"name": "BodyInOneMultiMiddleManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiMiddle/manisc/MTAiModel"}, {"name": "BodyInOneMultiSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiSmall/manis/MTAiModel"}, {"name": "BodyInOneMultiSmallManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiSmall/manisa/MTAiModel"}, {"name": "BodyInOneMultiSmallManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneMultiSmall/manisc/MTAiModel"}, {"name": "BodyInOneNeckImageLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageLarge/manis/MTAiModel"}, {"name": "BodyInOneNeckImageLargeManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageLarge/manisa/MTAiModel"}, {"name": "BodyInOneNeckImageLargeManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageLarge/manisc/MTAiModel"}, {"name": "BodyInOneNeckImageMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageMiddle/manis/MTAiModel"}, {"name": "BodyInOneNeckImageMiddleManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageMiddle/manisa/MTAiModel"}, {"name": "BodyInOneNeckImageMiddleManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageMiddle/manisc/MTAiModel"}, {"name": "BodyInOneNeckImageSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageSmall/manis/MTAiModel"}, {"name": "BodyInOneNeckImageSmallManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageSmall/manisa/MTAiModel"}, {"name": "BodyInOneNeckImageSmallManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckImageSmall/manisc/MTAiModel"}, {"name": "BodyInOneNeckVideoLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoLarge/manis/MTAiModel"}, {"name": "BodyInOneNeckVideoLargeManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoLarge/manisa/MTAiModel"}, {"name": "BodyInOneNeckVideoLargeManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoLarge/manisc/MTAiModel"}, {"name": "BodyInOneNeckVideoMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoMiddle/manis/MTAiModel"}, {"name": "BodyInOneNeckVideoMiddleManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoMiddle/manisa/MTAiModel"}, {"name": "BodyInOneNeckVideoMiddleManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoMiddle/manisc/MTAiModel"}, {"name": "BodyInOneNeckVideoSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoSmall/manis/MTAiModel"}, {"name": "BodyInOneNeckVideoSmallManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoSmall/manisa/MTAiModel"}, {"name": "BodyInOneNeckVideoSmallManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOneNeckVideoSmall/manisc/MTAiModel"}, {"name": "BodyInOnePose", "resources": "iOS/ModelModules/MTAiModelBodyInOnePose/manis/MTAiModel"}, {"name": "BodyInOnePoseImageLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageLarge/manis/MTAiModel"}, {"name": "BodyInOnePoseImageLargeManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageLarge/manisa/MTAiModel"}, {"name": "BodyInOnePoseImageLargeManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageLarge/manisc/MTAiModel"}, {"name": "BodyInOnePoseImageMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageMiddle/manis/MTAiModel"}, {"name": "BodyInOnePoseImageMiddleManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageMiddle/manisa/MTAiModel"}, {"name": "BodyInOnePoseImageMiddleManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageMiddle/manisc/MTAiModel"}, {"name": "BodyInOnePoseImageSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageSmall/manis/MTAiModel"}, {"name": "BodyInOnePoseImageSmallManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageSmall/manisa/MTAiModel"}, {"name": "BodyInOnePoseImageSmallManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseImageSmall/manisc/MTAiModel"}, {"name": "BodyInOnePoseLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseLarge/manis/MTAiModel"}, {"name": "BodyInOnePoseLarge33", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseLarge33/manis/MTAiModel"}, {"name": "BodyInOnePoseMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseMiddle/manis/MTAiModel"}, {"name": "BodyInOnePoseMiddle33", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseMiddle33/manis/MTAiModel"}, {"name": "BodyInOnePoseSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseSmall/manis/MTAiModel"}, {"name": "BodyInOnePoseSmall33", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseSmall33/manis/MTAiModel"}, {"name": "BodyInOnePoseVideoLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoLarge/manis/MTAiModel"}, {"name": "BodyInOnePoseVideoLargeManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoLarge/manisa/MTAiModel"}, {"name": "BodyInOnePoseVideoLargeManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoLarge/manisc/MTAiModel"}, {"name": "BodyInOnePoseVideoMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoMiddle/manis/MTAiModel"}, {"name": "BodyInOnePoseVideoMiddleManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoMiddle/manisa/MTAiModel"}, {"name": "BodyInOnePoseVideoMiddleManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoMiddle/manisc/MTAiModel"}, {"name": "BodyInOnePoseVideoSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoSmall/manis/MTAiModel"}, {"name": "BodyInOnePoseVideoSmallManisa", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoSmall/manisa/MTAiModel"}, {"name": "BodyInOnePoseVideoSmallManisc", "resources": "iOS/ModelModules/MTAiModelBodyInOnePoseVideoSmall/manisc/MTAiModel"}, {"name": "BodyInOneReid", "resources": "iOS/ModelModules/MTAiModelBodyInOneReid/manis/MTAiModel"}, {"name": "BodyInOneReidLarge", "resources": "iOS/ModelModules/MTAiModelBodyInOneReidLarge/manis/MTAiModel"}, {"name": "BodyInOneReidMiddle", "resources": "iOS/ModelModules/MTAiModelBodyInOneReidMiddle/manis/MTAiModel"}, {"name": "BodyInOneReidSmall", "resources": "iOS/ModelModules/MTAiModelBodyInOneReidSmall/manis/MTAiModel"}, {"name": "BodyInOneShoulder", "resources": "iOS/ModelModules/MTAiModelBodyInOneShoulder/manis/MTAiModel"}, {"name": "BodyPose", "resources": "iOS/ModelModules/MTAiModelBodyPose/manis/MTAiModel"}, {"name": "BrowSeg", "resources": "iOS/ModelModules/MTAiModelBrowSeg/manis/MTAiModel"}, {"name": "ColorTransfer", "resources": "iOS/ModelModules/MTAiModelColorTransfer/manis/MTAiModel"}, {"name": "ColorTransferManisa", "resources": "iOS/ModelModules/MTAiModelColorTransfer/manisa/MTAiModel"}, {"name": "ColorTransferManisc", "resources": "iOS/ModelModules/MTAiModelColorTransfer/manisc/MTAiModel"}, {"name": "ColortoningColorAC", "resources": "iOS/ModelModules/MTAiModelColortoningColorAC/manis/MTAiModel"}, {"name": "ColortoningColorACManisc", "resources": "iOS/ModelModules/MTAiModelColortoningColorAC/manisc/MTAiModel"}, {"name": "ColortoningEvaA", "resources": "iOS/ModelModules/MTAiModelColortoningEvaA/manis/MTAiModel"}, {"name": "ColortoningMtctenhance", "resources": "iOS/ModelModules/MTAiModelColortoningMtctenhance/manis/MTAiModel"}, {"name": "ColortoningMtctenhanceManisc", "resources": "iOS/ModelModules/MTAiModelColortoningMtctenhance/manisc/MTAiModel"}, {"name": "DL3D", "resources": "iOS/ModelModules/MTAiModelDL3D/manis/MTAiModel"}, {"name": "DLBeautyBaseGloom", "resources": "iOS/ModelModules/MTAiModelDLBeautyBaseGloom/manis/MTAiModel"}, {"name": "DLBeautyBaseGloomManisa", "resources": "iOS/ModelModules/MTAiModelDLBeautyBaseGloom/manisa/MTAiModel"}, {"name": "DLBeautyBaseGloomManisc", "resources": "iOS/ModelModules/MTAiModelDLBeautyBaseGloom/manisc/MTAiModel"}, {"name": "DLBeautyBaseRaichu", "resources": "iOS/ModelModules/MTAiModelDLBeautyBaseRaichu/manis/MTAiModel"}, {"name": "DLBeautyBaseRaichuManisa", "resources": "iOS/ModelModules/MTAiModelDLBeautyBaseRaichu/manisa/MTAiModel"}, {"name": "DLBeautyBaseRaichuManisc", "resources": "iOS/ModelModules/MTAiModelDLBeautyBaseRaichu/manisc/MTAiModel"}, {"name": "DenseHairHairLine", "resources": "iOS/ModelModules/MTAiModelDenseHairHairLine/manis/MTAiModel"}, {"name": "DenseHairSparse", "resources": "iOS/ModelModules/MTAiModelDenseHairSparse/manis/MTAiModel"}, {"name": "DetectFrameWorkNeck", "resources": "iOS/ModelModules/MTAiModelDetectFrameWorkNeck/manis/MTAiModel"}, {"name": "DetectFrameWorkNeckConfig", "resources": "iOS/ModelModules/MTAiModelDetectFrameWorkNeckConfig/manis/MTAiModel"}, {"name": "DetectFrameWorkRootConfig", "resources": "iOS/ModelModules/MTAiModelDetectFrameWorkRootConfig/manis/MTAiModel"}, {"name": "Effect", "resources": "iOS/ModelModules/MTAiModelEffect/manis/MTAiModel"}, {"name": "EyeSegment", "resources": "iOS/ModelModules/MTAiModelEyeSegment/manis/MTAiModel"}, {"name": "Face3DFa", "resources": "iOS/ModelModules/MTAiModelFace3DFa/manis/MTAiModel"}, {"name": "Face3DFaCrop", "resources": "iOS/ModelModules/MTAiModelFace3DFaCrop/manis/MTAiModel"}, {"name": "FaceAge", "resources": "iOS/ModelModules/MTAiModelFaceAge/manis/MTAiModel"}, {"name": "FaceAgeSea", "resources": "iOS/ModelModules/MTAiModelFaceAgeSea/manis/MTAiModel"}, {"name": "FaceAnalysis2CheekBoneType", "resources": "iOS/ModelModules/MTAiModelFaceAnalysis2CheekBoneType/manis/MTAiModel"}, {"name": "FaceAnalysis2ChinShape", "resources": "iOS/ModelModules/MTAiModelFaceAnalysis2ChinShape/manis/MTAiModel"}, {"name": "FaceAnalysis2EyeBag", "resources": "iOS/ModelModules/MTAiModelFaceAnalysis2EyeBag/manis/MTAiModel"}, {"name": "FaceAnalysis2EyelidType", "resources": "iOS/ModelModules/MTAiModelFaceAnalysis2EyelidType/manis/MTAiModel"}, {"name": "FaceAnalysis2FaceShapeDL", "resources": "iOS/ModelModules/MTAiModelFaceAnalysis2FaceShapeDL/manis/MTAiModel"}, {"name": "FaceAnalysis2Risorius", "resources": "iOS/ModelModules/MTAiModelFaceAnalysis2Risorius/manis/MTAiModel"}, {"name": "FaceAnalysis2TempleType", "resources": "iOS/ModelModules/MTAiModelFaceAnalysis2TempleType/manis/MTAiModel"}, {"name": "FaceAnalysisDLConfig", "resources": "iOS/ModelModules/MTAiModelFaceAnalysisDLConfig/manis/MTAiModel"}, {"name": "FaceAnalysisDLEyeBag", "resources": "iOS/ModelModules/MTAiModelFaceAnalysisDLEyeBag/manis/MTAiModel"}, {"name": "FaceAnalysisDLFaceType", "resources": "iOS/ModelModules/MTAiModelFaceAnalysisDLFaceType/manis/MTAiModel"}, {"name": "FaceAnalysisDLRisorius", "resources": "iOS/ModelModules/MTAiModelFaceAnalysisDLRisorius/manis/MTAiModel"}, {"name": "FaceAnalysisDLTemple", "resources": "iOS/ModelModules/MTAiModelFaceAnalysisDLTemple/manis/MTAiModel"}, {"name": "FaceB<PERSON>ty", "resources": "iOS/ModelModules/MTAiModelFaceBeauty/manis/MTAiModel"}, {"name": "FaceCheek", "resources": "iOS/ModelModules/MTAiModelFaceCheek/manis/MTAiModel"}, {"name": "FaceContour", "resources": "iOS/ModelModules/MTAiModelFaceContour/manis/MTAiModel"}, {"name": "FaceDL3D", "resources": "iOS/ModelModules/MTAiModelFaceDL3D/manis/MTAiModel"}, {"name": "FaceDetector", "resources": "iOS/ModelModules/MTAiModelFaceDetector/manis/MTAiModel"}, {"name": "FaceEar", "resources": "iOS/ModelModules/MTAiModelFaceEar/manis/MTAiModel"}, {"name": "FaceEmotion", "resources": "iOS/ModelModules/MTAiModelFaceEmotion/manis/MTAiModel"}, {"name": "FaceE<PERSON>lid", "resources": "iOS/ModelModules/MTAiModelFaceEyelid/manis/MTAiModel"}, {"name": "FaceFAHeavy", "resources": "iOS/ModelModules/MTAiModelFaceFAHeavy/manis/MTAiModel"}, {"name": "FaceFALight", "resources": "iOS/ModelModules/MTAiModelFaceFALight/manis/MTAiModel"}, {"name": "FaceFAMedium", "resources": "iOS/ModelModules/MTAiModelFaceFAMedium/manis/MTAiModel"}, {"name": "FaceFD", "resources": "iOS/ModelModules/MTAiModelFaceFD/manis/MTAiModel"}, {"name": "FaceFR", "resources": "iOS/ModelModules/MTAiModelFaceFR/manis/MTAiModel"}, {"name": "FaceFacialFeature", "resources": "iOS/ModelModules/MTAiModelFaceFacialFeature/manis/MTAiModel"}, {"name": "<PERSON><PERSON><PERSON>", "resources": "iOS/ModelModules/MTAiModelFaceGender/manis/MTAiModel"}, {"name": "FaceGlasses", "resources": "iOS/ModelModules/MTAiModelFaceGlasses/manis/MTAiModel"}, {"name": "FaceHead", "resources": "iOS/ModelModules/MTAiModelFaceHead/manis/MTAiModel"}, {"name": "<PERSON><PERSON><PERSON>", "resources": "iOS/ModelModules/MTAiModelFaceJaw/manis/MTAiModel"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "resources": "iOS/ModelModules/MTAiModelFaceMustache/manis/MTAiModel"}, {"name": "<PERSON><PERSON><PERSON>", "resources": "iOS/ModelModules/MTAiModelFaceNose/manis/MTAiModel"}, {"name": "FaceOcclusion", "resources": "iOS/ModelModules/MTAiModelFaceOcclusion/manis/MTAiModel"}, {"name": "FaceParsing", "resources": "iOS/ModelModules/MTAiModelFaceParsing/manis/MTAiModel"}, {"name": "FaceParsingHeavy", "resources": "iOS/ModelModules/MTAiModelFaceParsingHeavy/manis/MTAiModel"}, {"name": "FaceParsingHeavyVideo", "resources": "iOS/ModelModules/MTAiModelFaceParsingHeavyVideo/manis/MTAiModel"}, {"name": "FaceParsingHeavyVideoManisa", "resources": "iOS/ModelModules/MTAiModelFaceParsingHeavyVideo/manisa/MTAiModel"}, {"name": "FaceParsingHeavyVideoManisc", "resources": "iOS/ModelModules/MTAiModelFaceParsingHeavyVideo/manisc/MTAiModel"}, {"name": "FaceParsingLight", "resources": "iOS/ModelModules/MTAiModelFaceParsingLight/manis/MTAiModel"}, {"name": "FaceParsingLightVideo", "resources": "iOS/ModelModules/MTAiModelFaceParsingLightVideo/manis/MTAiModel"}, {"name": "FaceParsingLightVideoManisa", "resources": "iOS/ModelModules/MTAiModelFaceParsingLightVideo/manisa/MTAiModel"}, {"name": "FaceParsingLightVideoManisc", "resources": "iOS/ModelModules/MTAiModelFaceParsingLightVideo/manisc/MTAiModel"}, {"name": "FaceParsingVideo", "resources": "iOS/ModelModules/MTAiModelFaceParsingVideo/manis/MTAiModel"}, {"name": "FaceParsingVideoManisa", "resources": "iOS/ModelModules/MTAiModelFaceParsingVideo/manisa/MTAiModel"}, {"name": "FaceParsingVideoManisc", "resources": "iOS/ModelModules/MTAiModelFaceParsingVideo/manisc/MTAiModel"}, {"name": "FaceQuality", "resources": "iOS/ModelModules/MTAiModelFaceQuality/manis/MTAiModel"}, {"name": "FaceRace", "resources": "iOS/ModelModules/MTAiModelFaceRace/manis/MTAiModel"}, {"name": "FaceRefineEyes", "resources": "iOS/ModelModules/MTAiModelFaceRefineEyes/manis/MTAiModel"}, {"name": "FaceRefineMouth", "resources": "iOS/ModelModules/MTAiModelFaceRefineMouth/manis/MTAiModel"}, {"name": "FaceSceneChange", "resources": "iOS/ModelModules/MTAiModelFaceSceneChange/manis/MTAiModel"}, {"name": "HairClassifier", "resources": "iOS/ModelModules/MTAiModelHairClassifier/manis/MTAiModel"}, {"name": "HandContours", "resources": "iOS/ModelModules/MTAiModelHandContours/manis/MTAiModel"}, {"name": "HandDetector", "resources": "iOS/ModelModules/MTAiModelHandDetector/manis/MTAiModel"}, {"name": "HandGesture", "resources": "iOS/ModelModules/MTAiModelHandGesture/manis/MTAiModel"}, {"name": "ImageRecognition", "resources": "iOS/ModelModules/MTAiModelImageRecognition/manis/MTAiModel"}, {"name": "InceptionBeautyBest", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyBest/manis/MTAiModel"}, {"name": "InceptionBeautyBestManisa", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyBest/manisa/MTAiModel"}, {"name": "InceptionBeautyBestManisc", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyBest/manisc/MTAiModel"}, {"name": "InceptionBeautyBestCoreml", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyBestCoreml/manis/MTAiModel"}, {"name": "InceptionBeautyBestIOS17Manisa", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyBestIOS17/manisa/MTAiModel"}, {"name": "InceptionBeautyBestIOS17Manisc", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyBestIOS17/manisc/MTAiModel"}, {"name": "InceptionBeautyDinoysos", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyDinoysos/manis/MTAiModel"}, {"name": "InceptionBeautyFast", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyFast/manis/MTAiModel"}, {"name": "InceptionBeautyPh", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyPh/manis/MTAiModel"}, {"name": "InceptionBeautyPhManisa", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyPh/manisa/MTAiModel"}, {"name": "InceptionBeautyPhManisc", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyPh/manisc/MTAiModel"}, {"name": "InceptionBeautyRt", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyRt/manis/MTAiModel"}, {"name": "InceptionBeautyRtWink", "resources": "iOS/ModelModules/MTAiModelInceptionBeautyRtWink/manis/MTAiModel"}, {"name": "InstanceSegment", "resources": "iOS/ModelModules/MTAiModelInstanceSegment/manis/MTAiModel"}, {"name": "IntelligentFusion", "resources": "iOS/ModelModules/MTAiModelIntelligentFusion/manis/MTAiModel"}, {"name": "MakeupRecognitionV2", "resources": "iOS/ModelModules/MTAiModelMakeupRecognitionV2/manis/MTAiModel"}, {"name": "MaterialTracking", "resources": "iOS/ModelModules/MTAiModelMaterialTracking/manis/MTAiModel"}, {"name": "MtvenusDittany", "resources": "iOS/ModelModules/MTAiModelMtvenusDittany/manis/MTAiModel"}, {"name": "MtvenusDittanyManisa", "resources": "iOS/ModelModules/MTAiModelMtvenusDittany/manisa/MTAiModel"}, {"name": "MtvenusDittanyManisc", "resources": "iOS/ModelModules/MTAiModelMtvenusDittany/manisc/MTAiModel"}, {"name": "MtvenusEos", "resources": "iOS/ModelModules/MTAiModelMtvenusEos/manis/MTAiModel"}, {"name": "Mtvenus<PERSON><PERSON><PERSON>", "resources": "iOS/ModelModules/MTAiModelMtvenusFelixFelicis/manis/MTAiModel"}, {"name": "MtvenusHercules", "resources": "iOS/ModelModules/MTAiModelMtvenusHercules/manis/MTAiModel"}, {"name": "MtvenusHerculesManisa", "resources": "iOS/ModelModules/MTAiModelMtvenusHercules/manisa/MTAiModel"}, {"name": "MtvenusHerculesManisc", "resources": "iOS/ModelModules/MTAiModelMtvenusHercules/manisc/MTAiModel"}, {"name": "MtvenusHiccupingSolution", "resources": "iOS/ModelModules/MTAiModelMtvenusHiccupingSolution/manis/MTAiModel"}, {"name": "MtvenusHiccupingSolutionManisa", "resources": "iOS/ModelModules/MTAiModelMtvenusHiccupingSolution/manisa/MTAiModel"}, {"name": "MtvenusHiccupingSolutionManisc", "resources": "iOS/ModelModules/MTAiModelMtvenusHiccupingSolution/manisc/MTAiModel"}, {"name": "MtvenusHorusEyeEgypt", "resources": "iOS/ModelModules/MTAiModelMtvenusHorusEyeEgypt/manis/MTAiModel"}, {"name": "MtvenusHorusEyeEgyptManisa", "resources": "iOS/ModelModules/MTAiModelMtvenusHorusEyeEgypt/manisa/MTAiModel"}, {"name": "MtvenusHorusEyeEgyptManisc", "resources": "iOS/ModelModules/MTAiModelMtvenusHorusEyeEgypt/manisc/MTAiModel"}, {"name": "Mt<PERSON>us<PERSON>owler", "resources": "iOS/ModelModules/MTAiModelMtvenusHowler/manis/MTAiModel"}, {"name": "MtvenusHowlerManisa", "resources": "iOS/ModelModules/MTAiModelMtvenusHowler/manisa/MTAiModel"}, {"name": "MtvenusHowlerManisc", "resources": "iOS/ModelModules/MTAiModelMtvenusHowler/manisc/MTAiModel"}, {"name": "MtvenusPolyjuicePotion", "resources": "iOS/ModelModules/MTAiModelMtvenusPolyjuicePotion/manis/MTAiModel"}, {"name": "MtvenusPoseidon", "resources": "iOS/ModelModules/MTAiModelMtvenusPoseidon/manis/MTAiModel"}, {"name": "MtvenusPoseidonManisa", "resources": "iOS/ModelModules/MTAiModelMtvenusPoseidon/manisa/MTAiModel"}, {"name": "MtvenusPoseidonManisc", "resources": "iOS/ModelModules/MTAiModelMtvenusPoseidon/manisc/MTAiModel"}, {"name": "MtvenusShrinkingSolution", "resources": "iOS/ModelModules/MTAiModelMtvenusShrinkingSolution/manis/MTAiModel"}, {"name": "MtvenusVeritaserum", "resources": "iOS/ModelModules/MTAiModelMtvenusVeritaserum/manis/MTAiModel"}, {"name": "MtvenusVeritaserumManisa", "resources": "iOS/ModelModules/MTAiModelMtvenusVeritaserum/manisa/MTAiModel"}, {"name": "MtvenusVeritaserumManisc", "resources": "iOS/ModelModules/MTAiModelMtvenusVeritaserum/manisc/MTAiModel"}, {"name": "MtvenusVeritaserumBest", "resources": "iOS/ModelModules/MTAiModelMtvenusVeritaserumBest/manis/MTAiModel"}, {"name": "MtvenusVeritaserumBestManisa", "resources": "iOS/ModelModules/MTAiModelMtvenusVeritaserumBest/manisa/MTAiModel"}, {"name": "MtvenusVeritaserumBestManisc", "resources": "iOS/ModelModules/MTAiModelMtvenusVeritaserumBest/manisc/MTAiModel"}, {"name": "NevusDetection", "resources": "iOS/ModelModules/MTAiModelNevusDetection/manis/MTAiModel"}, {"name": "NevusDetectionSmall", "resources": "iOS/ModelModules/MTAiModelNevusDetectionSmall/manis/MTAiModel"}, {"name": "Ornament", "resources": "iOS/ModelModules/MTAiModelOrnament/manis/MTAiModel"}, {"name": "PortraitInpaintingMobile", "resources": "iOS/ModelModules/MTAiModelPortraitInpaintingMobile/manis/MTAiModel"}, {"name": "RTDenseHairModelManisa", "resources": "iOS/ModelModules/MTAiModelRTDenseHairModel/manisa/MTAiModel"}, {"name": "RTDenseHairModelManisc", "resources": "iOS/ModelModules/MTAiModelRTDenseHairModel/manisc/MTAiModel"}, {"name": "RTDenseHairModelAndroid", "resources": "iOS/ModelModules/MTAiModelRTDenseHairModelAndroid/manis/MTAiModel"}, {"name": "RT<PERSON>eckWrinkle", "resources": "iOS/ModelModules/MTAiModelRTNeckWrinkle/manis/MTAiModel"}, {"name": "RTTeethRetouchModelManisa", "resources": "iOS/ModelModules/MTAiModelRTTeethRetouchModel/manisa/MTAiModel"}, {"name": "RTTeethRetouchModelManisc", "resources": "iOS/ModelModules/MTAiModelRTTeethRetouchModel/manisc/MTAiModel"}, {"name": "RTTeethRetouchModelGen", "resources": "iOS/ModelModules/MTAiModelRTTeethRetouchModelGen/manis/MTAiModel"}, {"name": "RTTeethRetouchModelSeg", "resources": "iOS/ModelModules/MTAiModelRTTeethRetouchModelSeg/manis/MTAiModel"}, {"name": "SegmentPhotoFace", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoFace/manis/MTAiModel"}, {"name": "SegmentPhotoFaceContour", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoFaceContour/manis/MTAiModel"}, {"name": "SegmentPhotoFullbody", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoFullbody/manis/MTAiModel"}, {"name": "SegmentPhotoHair", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoHair/manis/MTAiModel"}, {"name": "SegmentPhotoHalfbody", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoHalfbody/manis/MTAiModel"}, {"name": "SegmentPhotoHead", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoHead/manis/MTAiModel"}, {"name": "SegmentPhotoMidas", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoMidas/manis/MTAiModel"}, {"name": "SegmentPhotoSegmentation", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoSegmentation/manis/MTAiModel"}, {"name": "SegmentPhotoSkin", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoSkin/manis/MTAiModel"}, {"name": "SegmentPhotoSky", "resources": "iOS/ModelModules/MTAiModelSegmentPhotoSky/manis/MTAiModel"}, {"name": "SegmentRealtimeAllbody", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeAllbody/manis/MTAiModel"}, {"name": "SegmentRealtimeCW", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeCW/manis/MTAiModel"}, {"name": "SegmentRealtimeCloth", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeCloth/manis/MTAiModel"}, {"name": "SegmentRealtimeHair", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeHair/manis/MTAiModel"}, {"name": "SegmentRealtimeHairManisa", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeHair/manisa/MTAiModel"}, {"name": "SegmentRealtimeHairManisc", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeHair/manisc/MTAiModel"}, {"name": "SegmentRealtimeHalfbody", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeHalfbody/manis/MTAiModel"}, {"name": "SegmentRealtimeHalfbodyVideoHigh", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeHalfbodyVideoHigh/manis/MTAiModel"}, {"name": "SegmentRealtimeHead", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeHead/manis/MTAiModel"}, {"name": "SegmentRealtimeInteractiveKey", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeInteractiveKey/manis/MTAiModel"}, {"name": "SegmentRealtimeInteractiveKeyManisa", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeInteractiveKey/manisa/MTAiModel"}, {"name": "SegmentRealtimeInteractiveKeyManisc", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeInteractiveKey/manisc/MTAiModel"}, {"name": "SegmentRealtimeInteractiveValue", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeInteractiveValue/manis/MTAiModel"}, {"name": "SegmentRealtimeInteractiveValueManisa", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeInteractiveValue/manisa/MTAiModel"}, {"name": "SegmentRealtimeInteractiveValueManisc", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeInteractiveValue/manisc/MTAiModel"}, {"name": "SegmentRealtimeMuti", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeMuti/manis/MTAiModel"}, {"name": "SegmentRealtimeMutiHeavy", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeMutiHeavy/manis/MTAiModel"}, {"name": "SegmentRealtimeSalientDetection", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeSalientDetection/manis/MTAiModel"}, {"name": "SegmentRealtimeSalientDetectionManisa", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeSalientDetection/manisa/MTAiModel"}, {"name": "SegmentRealtimeSalientDetectionManisc", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeSalientDetection/manisc/MTAiModel"}, {"name": "SegmentRealtimeSkin", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeSkin/manis/MTAiModel"}, {"name": "SegmentRealtimeSky", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeSky/manis/MTAiModel"}, {"name": "SegmentRealtimeSpaceDepth", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeSpaceDepth/manis/MTAiModel"}, {"name": "SegmentRealtimeSpaceDepthManisa", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeSpaceDepth/manisa/MTAiModel"}, {"name": "SegmentRealtimeSpaceDepthManisc", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeSpaceDepth/manisc/MTAiModel"}, {"name": "SegmentRealtimeVideoBody", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeVideoBody/manis/MTAiModel"}, {"name": "SegmentRealtimeVideoBodyManisa", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeVideoBody/manisa/MTAiModel"}, {"name": "SegmentRealtimeVideoBodyManisc", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeVideoBody/manisc/MTAiModel"}, {"name": "SegmentRealtimeVideoSkin", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeVideoSkin/manis/MTAiModel"}, {"name": "SegmentRealtimeVideoSkinManisa", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeVideoSkin/manisa/MTAiModel"}, {"name": "SegmentRealtimeVideoSkinManisc", "resources": "iOS/ModelModules/MTAiModelSegmentRealtimeVideoSkin/manisc/MTAiModel"}, {"name": "Shoulder", "resources": "iOS/ModelModules/MTAiModelShoulder/manis/MTAiModel"}, {"name": "SkinAcneFleck", "resources": "iOS/ModelModules/MTAiModelSkinAcneFleck/manis/MTAiModel"}, {"name": "SkinAcneMark", "resources": "iOS/ModelModules/MTAiModelSkinAcneMark/manis/MTAiModel"}, {"name": "SkinAcneMarkFront", "resources": "iOS/ModelModules/MTAiModelSkinAcneMarkFront/manis/MTAiModel"}, {"name": "SkinAcneV1", "resources": "iOS/ModelModules/MTAiModelSkinAcneV1/manis/MTAiModel"}, {"name": "SkinApi", "resources": "iOS/ModelModules/MTAiModelSkinApi/manis/MTAiModel"}, {"name": "SkinBCC", "resources": "iOS/ModelModules/MTAiModelSkinBCC/manis/MTAiModel"}, {"name": "SkinBlackHead", "resources": "iOS/ModelModules/MTAiModelSkinBlackHead/manis/MTAiModel"}, {"name": "SkinBlackHead3", "resources": "iOS/ModelModules/MTAiModelSkinBlackHead3/manis/MTAiModel"}, {"name": "SkinCrowsFeet", "resources": "iOS/ModelModules/MTAiModelSkinCrowsFeet/manis/MTAiModel"}, {"name": "SkinCrowsFeetFront", "resources": "iOS/ModelModules/MTAiModelSkinCrowsFeetFront/manis/MTAiModel"}, {"name": "SkinEyeBag", "resources": "iOS/ModelModules/MTAiModelSkinEyeBag/manis/MTAiModel"}, {"name": "SkinEyeBrowLine", "resources": "iOS/ModelModules/MTAiModelSkinEyeBrowLine/manis/MTAiModel"}, {"name": "<PERSON><PERSON>ye<PERSON><PERSON>ck", "resources": "iOS/ModelModules/MTAiModelSkinEyeFleck/manis/MTAiModel"}, {"name": "SkinEyeSeg", "resources": "iOS/ModelModules/MTAiModelSkinEyeSeg/manis/MTAiModel"}, {"name": "SkinEyeWrinkle", "resources": "iOS/ModelModules/MTAiModelSkinEyeWrinkle/manis/MTAiModel"}, {"name": "SkinEyelid", "resources": "iOS/ModelModules/MTAiModelSkinEyelid/manis/MTAiModel"}, {"name": "SkinFlaw", "resources": "iOS/ModelModules/MTAiModelSkinFlaw/manis/MTAiModel"}, {"name": "SkinForeheadWrinkle", "resources": "iOS/ModelModules/MTAiModelSkinForeheadWrinkle/manis/MTAiModel"}, {"name": "SkinForeheadWrinkleFront", "resources": "iOS/ModelModules/MTAiModelSkinForeheadWrinkleFront/manis/MTAiModel"}, {"name": "SkinFullFaceSeg", "resources": "iOS/ModelModules/MTAiModelSkinFullFaceSeg/manis/MTAiModel"}, {"name": "SkinGeneralAcne", "resources": "iOS/ModelModules/MTAiModelSkinGeneralAcne/manis/MTAiModel"}, {"name": "SkinLYHPortCrowsFeet", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortCrowsFeet/manis/MTAiModel"}, {"name": "SkinLYHPortEyeFront", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortEyeFront/manis/MTAiModel"}, {"name": "SkinLYHPortForehead", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortForehead/manis/MTAiModel"}, {"name": "SkinLYHPortNasolabial", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortNasolabial/manis/MTAiModel"}, {"name": "SkinLYHPortV2CrowsFeet", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortV2CrowsFeet/manis/MTAiModel"}, {"name": "SkinLYHPortV2Eye", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortV2Eye/manis/MTAiModel"}, {"name": "SkinLYHPortV2Forehead", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortV2Forehead/manis/MTAiModel"}, {"name": "SkinLYHPortV2MouthCorner", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortV2MouthCorner/manis/MTAiModel"}, {"name": "SkinLYHPortV2Nasolabial", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortV2Nasolabial/manis/MTAiModel"}, {"name": "SkinLYHPortV2Nasolabial2", "resources": "iOS/ModelModules/MTAiModelSkinLYHPortV2Nasolabial2/manis/MTAiModel"}, {"name": "SkinNasolabialFolds", "resources": "iOS/ModelModules/MTAiModelSkinNasolabialFolds/manis/MTAiModel"}, {"name": "SkinNasolabialFoldsFront", "resources": "iOS/ModelModules/MTAiModelSkinNasolabialFoldsFront/manis/MTAiModel"}, {"name": "Skin<PERSON><PERSON><PERSON>", "resources": "iOS/ModelModules/MTAiModelSkinNevus/manis/MTAiModel"}, {"name": "SkinPandaEyeFineGrainedClassifier", "resources": "iOS/ModelModules/MTAiModelSkinPandaEyeFineGrainedClassifier/manis/MTAiModel"}, {"name": "SkinPandaEyeFront", "resources": "iOS/ModelModules/MTAiModelSkinPandaEyeFront/manis/MTAiModel"}, {"name": "SkinPandaEyeGeneralFront", "resources": "iOS/ModelModules/MTAiModelSkinPandaEyeGeneralFront/manis/MTAiModel"}, {"name": "SkinPoreFront", "resources": "iOS/ModelModules/MTAiModelSkinPoreFront/manis/MTAiModel"}, {"name": "SkinPoreSeg2", "resources": "iOS/ModelModules/MTAiModelSkinPoreSeg2/manis/MTAiModel"}, {"name": "SkinPoreSeg2_2", "resources": "iOS/ModelModules/MTAiModelSkinPoreSeg2_2/manis/MTAiModel"}, {"name": "SkinPoresBack", "resources": "iOS/ModelModules/MTAiModelSkinPoresBack/manis/MTAiModel"}, {"name": "SkinPoresSegmentBack", "resources": "iOS/ModelModules/MTAiModelSkinPoresSegmentBack/manis/MTAiModel"}, {"name": "SkinRosacea", "resources": "iOS/ModelModules/MTAiModelSkinRosacea/manis/MTAiModel"}, {"name": "SkinSC", "resources": "iOS/ModelModules/MTAiModelSkinSC/manis/MTAiModel"}, {"name": "SkinStainV1", "resources": "iOS/ModelModules/MTAiModelSkinStainV1/manis/MTAiModel"}, {"name": "SkinTearT<PERSON>ough", "resources": "iOS/ModelModules/MTAiModelSkinTearThrough/manis/MTAiModel"}, {"name": "SkinTone", "resources": "iOS/ModelModules/MTAiModelSkinTone/manis/MTAiModel"}, {"name": "SkinUserPandaEye", "resources": "iOS/ModelModules/MTAiModelSkinUserPandaEye/manis/MTAiModel"}, {"name": "SkinUserSensitivity", "resources": "iOS/ModelModules/MTAiModelSkinUserSensitivity/manis/MTAiModel"}, {"name": "SkinUserSkinTypeClassifier", "resources": "iOS/ModelModules/MTAiModelSkinUserSkinTypeClassifier/manis/MTAiModel"}, {"name": "SkinUserWrinkleSeg", "resources": "iOS/ModelModules/MTAiModelSkinUserWrinkleSeg/manis/MTAiModel"}, {"name": "<PERSON><PERSON>", "resources": "iOS/ModelModules/MTAiModelTeeth/manis/MTAiModel"}, {"name": "VenusModel", "resources": "iOS/ModelModules/MTAiModelVenusModel/manis/MTAiModel"}, {"name": "VideoRecognition", "resources": "iOS/ModelModules/MTAiModelVideoRecognition/manis/MTAiModel"}, {"name": "WrinkleDetectionEye", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionEye/manis/MTAiModel"}, {"name": "WrinkleDetectionEyeLarge", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionEyeLarge/manis/MTAiModel"}, {"name": "WrinkleDetectionEyeMedium", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionEyeMedium/manis/MTAiModel"}, {"name": "WrinkleDetectionForehead", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionForehead/manis/MTAiModel"}, {"name": "WrinkleDetectionForeheadLarge", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionForeheadLarge/manis/MTAiModel"}, {"name": "WrinkleDetectionForeheadMedium", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionForeheadMedium/manis/MTAiModel"}, {"name": "WrinkleDetectionNaso", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionNaso/manis/MTAiModel"}, {"name": "WrinkleDetectionNasoMedium", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionNasoMedium/manis/MTAiModel"}, {"name": "WrinkleDetectionNeck", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionNeck/manis/MTAiModel"}, {"name": "WrinkleDetectionNeckMedium", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionNeckMedium/manis/MTAiModel"}, {"name": "WrinkleDetectionSilkworm", "resources": "iOS/ModelModules/MTAiModelWrinkleDetectionSilkworm/manis/MTAiModel"}, {"name": "MTAiModel", "dependencies": {"libmtaiinterface/MTAiMerakEyelidPhotoGL": [], "libmtaiinterface/MTAiMerakInnovationHairCurly": [], "libmtaiinterface/MTAiMerakInnovationHairFluffy": [], "libmtaiinterface/MTAiMerakInnovationHairStraight": [], "libmtaiinterface/3DFace3D": [], "libmtaiinterface/Animal": [], "libmtaiinterface/AuroraModel": [], "libmtaiinterface/AuroraModelManisa": [], "libmtaiinterface/AuroraModelManisc": [], "libmtaiinterface/AuroraModelCamera": [], "libmtaiinterface/AuroraModelCameraManisa": [], "libmtaiinterface/AuroraModelCameraManisc": [], "libmtaiinterface/BodyHuman": [], "libmtaiinterface/BodyInOneBox": [], "libmtaiinterface/BodyInOneBoxYolo": [], "libmtaiinterface/BodyInOneBreastImageLarge": [], "libmtaiinterface/BodyInOneBreastImageLargeManisa": [], "libmtaiinterface/BodyInOneBreastImageLargeManisc": [], "libmtaiinterface/BodyInOneBreastImageMiddle": [], "libmtaiinterface/BodyInOneBreastImageMiddleManisa": [], "libmtaiinterface/BodyInOneBreastImageMiddleManisc": [], "libmtaiinterface/BodyInOneBreastImageSmall": [], "libmtaiinterface/BodyInOneBreastImageSmallManisa": [], "libmtaiinterface/BodyInOneBreastImageSmallManisc": [], "libmtaiinterface/BodyInOneBreastVideoLarge": [], "libmtaiinterface/BodyInOneBreastVideoLargeManisa": [], "libmtaiinterface/BodyInOneBreastVideoLargeManisc": [], "libmtaiinterface/BodyInOneBreastVideoMiddle": [], "libmtaiinterface/BodyInOneBreastVideoMiddleManisa": [], "libmtaiinterface/BodyInOneBreastVideoMiddleManisc": [], "libmtaiinterface/BodyInOneBreastVideoSmall": [], "libmtaiinterface/BodyInOneBreastVideoSmallManisa": [], "libmtaiinterface/BodyInOneBreastVideoSmallManisc": [], "libmtaiinterface/BodyInOneContour": [], "libmtaiinterface/BodyInOneMulti": [], "libmtaiinterface/BodyInOneMultiLarge": [], "libmtaiinterface/BodyInOneMultiLargeManisa": [], "libmtaiinterface/BodyInOneMultiLargeManisc": [], "libmtaiinterface/BodyInOneMultiMiddle": [], "libmtaiinterface/BodyInOneMultiMiddleManisa": [], "libmtaiinterface/BodyInOneMultiMiddleManisc": [], "libmtaiinterface/BodyInOneMultiSmall": [], "libmtaiinterface/BodyInOneMultiSmallManisa": [], "libmtaiinterface/BodyInOneMultiSmallManisc": [], "libmtaiinterface/BodyInOneNeckImageLarge": [], "libmtaiinterface/BodyInOneNeckImageLargeManisa": [], "libmtaiinterface/BodyInOneNeckImageLargeManisc": [], "libmtaiinterface/BodyInOneNeckImageMiddle": [], "libmtaiinterface/BodyInOneNeckImageMiddleManisa": [], "libmtaiinterface/BodyInOneNeckImageMiddleManisc": [], "libmtaiinterface/BodyInOneNeckImageSmall": [], "libmtaiinterface/BodyInOneNeckImageSmallManisa": [], "libmtaiinterface/BodyInOneNeckImageSmallManisc": [], "libmtaiinterface/BodyInOneNeckVideoLarge": [], "libmtaiinterface/BodyInOneNeckVideoLargeManisa": [], "libmtaiinterface/BodyInOneNeckVideoLargeManisc": [], "libmtaiinterface/BodyInOneNeckVideoMiddle": [], "libmtaiinterface/BodyInOneNeckVideoMiddleManisa": [], "libmtaiinterface/BodyInOneNeckVideoMiddleManisc": [], "libmtaiinterface/BodyInOneNeckVideoSmall": [], "libmtaiinterface/BodyInOneNeckVideoSmallManisa": [], "libmtaiinterface/BodyInOneNeckVideoSmallManisc": [], "libmtaiinterface/BodyInOnePose": [], "libmtaiinterface/BodyInOnePoseImageLarge": [], "libmtaiinterface/BodyInOnePoseImageLargeManisa": [], "libmtaiinterface/BodyInOnePoseImageLargeManisc": [], "libmtaiinterface/BodyInOnePoseImageMiddle": [], "libmtaiinterface/BodyInOnePoseImageMiddleManisa": [], "libmtaiinterface/BodyInOnePoseImageMiddleManisc": [], "libmtaiinterface/BodyInOnePoseImageSmall": [], "libmtaiinterface/BodyInOnePoseImageSmallManisa": [], "libmtaiinterface/BodyInOnePoseImageSmallManisc": [], "libmtaiinterface/BodyInOnePoseLarge": [], "libmtaiinterface/BodyInOnePoseLarge33": [], "libmtaiinterface/BodyInOnePoseMiddle": [], "libmtaiinterface/BodyInOnePoseMiddle33": [], "libmtaiinterface/BodyInOnePoseSmall": [], "libmtaiinterface/BodyInOnePoseSmall33": [], "libmtaiinterface/BodyInOnePoseVideoLarge": [], "libmtaiinterface/BodyInOnePoseVideoLargeManisa": [], "libmtaiinterface/BodyInOnePoseVideoLargeManisc": [], "libmtaiinterface/BodyInOnePoseVideoMiddle": [], "libmtaiinterface/BodyInOnePoseVideoMiddleManisa": [], "libmtaiinterface/BodyInOnePoseVideoMiddleManisc": [], "libmtaiinterface/BodyInOnePoseVideoSmall": [], "libmtaiinterface/BodyInOnePoseVideoSmallManisa": [], "libmtaiinterface/BodyInOnePoseVideoSmallManisc": [], "libmtaiinterface/BodyInOneReid": [], "libmtaiinterface/BodyInOneReidLarge": [], "libmtaiinterface/BodyInOneReidMiddle": [], "libmtaiinterface/BodyInOneReidSmall": [], "libmtaiinterface/BodyInOneShoulder": [], "libmtaiinterface/BodyPose": [], "libmtaiinterface/BrowSeg": [], "libmtaiinterface/ColorTransfer": [], "libmtaiinterface/ColorTransferManisa": [], "libmtaiinterface/ColorTransferManisc": [], "libmtaiinterface/ColortoningColorAC": [], "libmtaiinterface/ColortoningColorACManisc": [], "libmtaiinterface/ColortoningEvaA": [], "libmtaiinterface/ColortoningMtctenhance": [], "libmtaiinterface/ColortoningMtctenhanceManisc": [], "libmtaiinterface/DL3D": [], "libmtaiinterface/DLBeautyBaseGloom": [], "libmtaiinterface/DLBeautyBaseGloomManisa": [], "libmtaiinterface/DLBeautyBaseGloomManisc": [], "libmtaiinterface/DLBeautyBaseRaichu": [], "libmtaiinterface/DLBeautyBaseRaichuManisa": [], "libmtaiinterface/DLBeautyBaseRaichuManisc": [], "libmtaiinterface/DenseHairHairLine": [], "libmtaiinterface/DenseHairSparse": [], "libmtaiinterface/DetectFrameWorkNeck": [], "libmtaiinterface/DetectFrameWorkNeckConfig": [], "libmtaiinterface/DetectFrameWorkRootConfig": [], "libmtaiinterface/Effect": [], "libmtaiinterface/EyeSegment": [], "libmtaiinterface/Face3DFa": [], "libmtaiinterface/Face3DFaCrop": [], "libmtaiinterface/FaceAge": [], "libmtaiinterface/FaceAgeSea": [], "libmtaiinterface/FaceAnalysis2CheekBoneType": [], "libmtaiinterface/FaceAnalysis2ChinShape": [], "libmtaiinterface/FaceAnalysis2EyeBag": [], "libmtaiinterface/FaceAnalysis2EyelidType": [], "libmtaiinterface/FaceAnalysis2FaceShapeDL": [], "libmtaiinterface/FaceAnalysis2Risorius": [], "libmtaiinterface/FaceAnalysis2TempleType": [], "libmtaiinterface/FaceAnalysisDLConfig": [], "libmtaiinterface/FaceAnalysisDLEyeBag": [], "libmtaiinterface/FaceAnalysisDLFaceType": [], "libmtaiinterface/FaceAnalysisDLRisorius": [], "libmtaiinterface/FaceAnalysisDLTemple": [], "libmtaiinterface/FaceBeauty": [], "libmtaiinterface/FaceCheek": [], "libmtaiinterface/FaceContour": [], "libmtaiinterface/FaceDL3D": [], "libmtaiinterface/FaceDetector": [], "libmtaiinterface/FaceEar": [], "libmtaiinterface/FaceEmotion": [], "libmtaiinterface/FaceEyelid": [], "libmtaiinterface/FaceFAHeavy": [], "libmtaiinterface/FaceFALight": [], "libmtaiinterface/FaceFAMedium": [], "libmtaiinterface/FaceFD": [], "libmtaiinterface/FaceFR": [], "libmtaiinterface/FaceFacialFeature": [], "libmtaiinterface/FaceGender": [], "libmtaiinterface/FaceGlasses": [], "libmtaiinterface/FaceHead": [], "libmtaiinterface/FaceJaw": [], "libmtaiinterface/FaceMustache": [], "libmtaiinterface/FaceNose": [], "libmtaiinterface/FaceOcclusion": [], "libmtaiinterface/FaceParsing": [], "libmtaiinterface/FaceParsingHeavy": [], "libmtaiinterface/FaceParsingHeavyVideo": [], "libmtaiinterface/FaceParsingHeavyVideoManisa": [], "libmtaiinterface/FaceParsingHeavyVideoManisc": [], "libmtaiinterface/FaceParsingLight": [], "libmtaiinterface/FaceParsingLightVideo": [], "libmtaiinterface/FaceParsingLightVideoManisa": [], "libmtaiinterface/FaceParsingLightVideoManisc": [], "libmtaiinterface/FaceParsingVideo": [], "libmtaiinterface/FaceParsingVideoManisa": [], "libmtaiinterface/FaceParsingVideoManisc": [], "libmtaiinterface/FaceQuality": [], "libmtaiinterface/FaceRace": [], "libmtaiinterface/FaceRefineEyes": [], "libmtaiinterface/FaceRefineMouth": [], "libmtaiinterface/FaceSceneChange": [], "libmtaiinterface/HairClassifier": [], "libmtaiinterface/HandContours": [], "libmtaiinterface/HandDetector": [], "libmtaiinterface/HandGesture": [], "libmtaiinterface/ImageRecognition": [], "libmtaiinterface/InceptionBeautyBest": [], "libmtaiinterface/InceptionBeautyBestManisa": [], "libmtaiinterface/InceptionBeautyBestManisc": [], "libmtaiinterface/InceptionBeautyBestCoreml": [], "libmtaiinterface/InceptionBeautyBestIOS17Manisa": [], "libmtaiinterface/InceptionBeautyBestIOS17Manisc": [], "libmtaiinterface/InceptionBeautyDinoysos": [], "libmtaiinterface/InceptionBeautyFast": [], "libmtaiinterface/InceptionBeautyPh": [], "libmtaiinterface/InceptionBeautyPhManisa": [], "libmtaiinterface/InceptionBeautyPhManisc": [], "libmtaiinterface/InceptionBeautyRt": [], "libmtaiinterface/InceptionBeautyRtWink": [], "libmtaiinterface/InstanceSegment": [], "libmtaiinterface/IntelligentFusion": [], "libmtaiinterface/MakeupRecognitionV2": [], "libmtaiinterface/MaterialTracking": [], "libmtaiinterface/MtvenusDittany": [], "libmtaiinterface/MtvenusDittanyManisa": [], "libmtaiinterface/MtvenusDittanyManisc": [], "libmtaiinterface/MtvenusEos": [], "libmtaiinterface/MtvenusFelixFelicis": [], "libmtaiinterface/MtvenusHercules": [], "libmtaiinterface/MtvenusHerculesManisa": [], "libmtaiinterface/MtvenusHerculesManisc": [], "libmtaiinterface/MtvenusHiccupingSolution": [], "libmtaiinterface/MtvenusHiccupingSolutionManisa": [], "libmtaiinterface/MtvenusHiccupingSolutionManisc": [], "libmtaiinterface/MtvenusHorusEyeEgypt": [], "libmtaiinterface/MtvenusHorusEyeEgyptManisa": [], "libmtaiinterface/MtvenusHorusEyeEgyptManisc": [], "libmtaiinterface/MtvenusHowler": [], "libmtaiinterface/MtvenusHowlerManisa": [], "libmtaiinterface/MtvenusHowlerManisc": [], "libmtaiinterface/MtvenusPolyjuicePotion": [], "libmtaiinterface/MtvenusPoseidon": [], "libmtaiinterface/MtvenusPoseidonManisa": [], "libmtaiinterface/MtvenusPoseidonManisc": [], "libmtaiinterface/MtvenusShrinkingSolution": [], "libmtaiinterface/MtvenusVeritaserum": [], "libmtaiinterface/MtvenusVeritaserumManisa": [], "libmtaiinterface/MtvenusVeritaserumManisc": [], "libmtaiinterface/MtvenusVeritaserumBest": [], "libmtaiinterface/MtvenusVeritaserumBestManisa": [], "libmtaiinterface/MtvenusVeritaserumBestManisc": [], "libmtaiinterface/NevusDetection": [], "libmtaiinterface/NevusDetectionSmall": [], "libmtaiinterface/Ornament": [], "libmtaiinterface/PortraitInpaintingMobile": [], "libmtaiinterface/RTDenseHairModelManisa": [], "libmtaiinterface/RTDenseHairModelManisc": [], "libmtaiinterface/RTDenseHairModelAndroid": [], "libmtaiinterface/RTNeckWrinkle": [], "libmtaiinterface/RTTeethRetouchModelManisa": [], "libmtaiinterface/RTTeethRetouchModelManisc": [], "libmtaiinterface/RTTeethRetouchModelGen": [], "libmtaiinterface/RTTeethRetouchModelSeg": [], "libmtaiinterface/SegmentPhotoFace": [], "libmtaiinterface/SegmentPhotoFaceContour": [], "libmtaiinterface/SegmentPhotoFullbody": [], "libmtaiinterface/SegmentPhotoHair": [], "libmtaiinterface/SegmentPhotoHalfbody": [], "libmtaiinterface/SegmentPhotoHead": [], "libmtaiinterface/SegmentPhotoMidas": [], "libmtaiinterface/SegmentPhotoSegmentation": [], "libmtaiinterface/SegmentPhotoSkin": [], "libmtaiinterface/SegmentPhotoSky": [], "libmtaiinterface/SegmentRealtimeAllbody": [], "libmtaiinterface/SegmentRealtimeCW": [], "libmtaiinterface/SegmentRealtimeCloth": [], "libmtaiinterface/SegmentRealtimeHair": [], "libmtaiinterface/SegmentRealtimeHairManisa": [], "libmtaiinterface/SegmentRealtimeHairManisc": [], "libmtaiinterface/SegmentRealtimeHalfbody": [], "libmtaiinterface/SegmentRealtimeHalfbodyVideoHigh": [], "libmtaiinterface/SegmentRealtimeHead": [], "libmtaiinterface/SegmentRealtimeInteractiveKey": [], "libmtaiinterface/SegmentRealtimeInteractiveKeyManisa": [], "libmtaiinterface/SegmentRealtimeInteractiveKeyManisc": [], "libmtaiinterface/SegmentRealtimeInteractiveValue": [], "libmtaiinterface/SegmentRealtimeInteractiveValueManisa": [], "libmtaiinterface/SegmentRealtimeInteractiveValueManisc": [], "libmtaiinterface/SegmentRealtimeMuti": [], "libmtaiinterface/SegmentRealtimeMutiHeavy": [], "libmtaiinterface/SegmentRealtimeSalientDetection": [], "libmtaiinterface/SegmentRealtimeSalientDetectionManisa": [], "libmtaiinterface/SegmentRealtimeSalientDetectionManisc": [], "libmtaiinterface/SegmentRealtimeSkin": [], "libmtaiinterface/SegmentRealtimeSky": [], "libmtaiinterface/SegmentRealtimeSpaceDepth": [], "libmtaiinterface/SegmentRealtimeSpaceDepthManisa": [], "libmtaiinterface/SegmentRealtimeSpaceDepthManisc": [], "libmtaiinterface/SegmentRealtimeVideoBody": [], "libmtaiinterface/SegmentRealtimeVideoBodyManisa": [], "libmtaiinterface/SegmentRealtimeVideoBodyManisc": [], "libmtaiinterface/SegmentRealtimeVideoSkin": [], "libmtaiinterface/SegmentRealtimeVideoSkinManisa": [], "libmtaiinterface/SegmentRealtimeVideoSkinManisc": [], "libmtaiinterface/Shoulder": [], "libmtaiinterface/SkinAcneFleck": [], "libmtaiinterface/SkinAcneMark": [], "libmtaiinterface/SkinAcneMarkFront": [], "libmtaiinterface/SkinAcneV1": [], "libmtaiinterface/SkinApi": [], "libmtaiinterface/SkinBCC": [], "libmtaiinterface/SkinBlackHead": [], "libmtaiinterface/SkinBlackHead3": [], "libmtaiinterface/SkinCrowsFeet": [], "libmtaiinterface/SkinCrowsFeetFront": [], "libmtaiinterface/SkinEyeBag": [], "libmtaiinterface/SkinEyeBrowLine": [], "libmtaiinterface/SkinEyeFleck": [], "libmtaiinterface/SkinEyeSeg": [], "libmtaiinterface/SkinEyeWrinkle": [], "libmtaiinterface/SkinEyelid": [], "libmtaiinterface/SkinFlaw": [], "libmtaiinterface/SkinForeheadWrinkle": [], "libmtaiinterface/SkinForeheadWrinkleFront": [], "libmtaiinterface/SkinFullFaceSeg": [], "libmtaiinterface/SkinGeneralAcne": [], "libmtaiinterface/SkinLYHPortCrowsFeet": [], "libmtaiinterface/SkinLYHPortEyeFront": [], "libmtaiinterface/SkinLYHPortForehead": [], "libmtaiinterface/SkinLYHPortNasolabial": [], "libmtaiinterface/SkinLYHPortV2CrowsFeet": [], "libmtaiinterface/SkinLYHPortV2Eye": [], "libmtaiinterface/SkinLYHPortV2Forehead": [], "libmtaiinterface/SkinLYHPortV2MouthCorner": [], "libmtaiinterface/SkinLYHPortV2Nasolabial": [], "libmtaiinterface/SkinLYHPortV2Nasolabial2": [], "libmtaiinterface/SkinNasolabialFolds": [], "libmtaiinterface/SkinNasolabialFoldsFront": [], "libmtaiinterface/SkinNevus": [], "libmtaiinterface/SkinPandaEyeFineGrainedClassifier": [], "libmtaiinterface/SkinPandaEyeFront": [], "libmtaiinterface/SkinPandaEyeGeneralFront": [], "libmtaiinterface/SkinPoreFront": [], "libmtaiinterface/SkinPoreSeg2": [], "libmtaiinterface/SkinPoreSeg2_2": [], "libmtaiinterface/SkinPoresBack": [], "libmtaiinterface/SkinPoresSegmentBack": [], "libmtaiinterface/SkinRosacea": [], "libmtaiinterface/SkinSC": [], "libmtaiinterface/SkinStainV1": [], "libmtaiinterface/SkinTearThrough": [], "libmtaiinterface/SkinTone": [], "libmtaiinterface/SkinUserPandaEye": [], "libmtaiinterface/SkinUserSensitivity": [], "libmtaiinterface/SkinUserSkinTypeClassifier": [], "libmtaiinterface/SkinUserWrinkleSeg": [], "libmtaiinterface/Teeth": [], "libmtaiinterface/VenusModel": [], "libmtaiinterface/VideoRecognition": [], "libmtaiinterface/WrinkleDetectionEye": [], "libmtaiinterface/WrinkleDetectionEyeLarge": [], "libmtaiinterface/WrinkleDetectionEyeMedium": [], "libmtaiinterface/WrinkleDetectionForehead": [], "libmtaiinterface/WrinkleDetectionForeheadLarge": [], "libmtaiinterface/WrinkleDetectionForeheadMedium": [], "libmtaiinterface/WrinkleDetectionNaso": [], "libmtaiinterface/WrinkleDetectionNasoMedium": [], "libmtaiinterface/WrinkleDetectionNeck": [], "libmtaiinterface/WrinkleDetectionNeckMedium": [], "libmtaiinterface/WrinkleDetectionSilkworm": []}}]}