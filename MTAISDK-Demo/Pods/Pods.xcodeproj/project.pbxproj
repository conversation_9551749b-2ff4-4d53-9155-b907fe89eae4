// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXAggregateTarget section */
		0A4E93A920C41658659C81ADE6AD435B /* libmtaiinterface */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 5E855EDBF0C2C3FC151E86F2C4654011 /* Build configuration list for PBXAggregateTarget "libmtaiinterface" */;
			buildPhases = (
			);
			dependencies = (
				4740C4CD6FDEDA027FB5915F6B3CBB9C /* PBXTargetDependency */,
			);
			name = libmtaiinterface;
		};
		4B6589A87574893065F6CEA7FD28C290 /* libmanis */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = E2220B36984985DDDCADD2E186846FFE /* Build configuration list for PBXAggregateTarget "libmanis" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = libmanis;
		};
		731483C741D3522F4DC8242EC19A6C3C /* mvgif */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 7EF458808BD9FFADFFCB3C8B8EECF084 /* Build configuration list for PBXAggregateTarget "mvgif" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = mvgif;
		};
		FC329C2B3FCA1F25298C3AFF669647F5 /* mtlabrecord */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = C4BB81FAD17BADAA0C9A723B98751F2B /* Build configuration list for PBXAggregateTarget "mtlabrecord" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = mtlabrecord;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		064D909CD827405E8DCC309DB1B7775A /* ConstraintLayoutSupportDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3B24C6502C5CBB0D966D40FA3BCACEE1 /* ConstraintLayoutSupportDSL.swift */; };
		09E1F569A93FAD4B9149E30B9301F44A /* ConstraintPriority.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5C2D883A320F44D6046ED546D66BCF13 /* ConstraintPriority.swift */; };
		0CA7A132ABE7018DE9295456732F38BB /* ConstraintAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = A843C54D2CD3C6332A2FD8B675EDBDC9 /* ConstraintAttributes.swift */; };
		0DE5DB9C6227B3416778D8417DD95EA9 /* ConstraintView+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 78C75B1847B7793F457F7C95DD9CAD1F /* ConstraintView+Extensions.swift */; };
		1194E62AA3F6F506799B1A43B16942B5 /* ConstraintDirectionalInsets.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9522031A21249BA4A0EE770C6B6ED571 /* ConstraintDirectionalInsets.swift */; };
		140B85DB7B9728CDADE24B89FBF13046 /* Pods-MTAISDK-Demo-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = D1FB8E88AF76ECA0C30E81F633E1F7AF /* Pods-MTAISDK-Demo-dummy.m */; };
		205EB01AED14BB574DD54EAFE26E4786 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		2B2EB369550CE92CEEFCBFD3D32B8A3F /* ConstraintInsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0AF5ED1324592889C66CC0F4552DFD95 /* ConstraintInsetTarget.swift */; };
		3577F172FA68CBAE47CFEE6FE25C5404 /* ConstraintOffsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3AA908141A906DC0A050BB7A4ED557EB /* ConstraintOffsetTarget.swift */; };
		3D3B646B4988314275B40E97BEB16C7F /* ConstraintLayoutGuideDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0C08E9263742D27691289F4A576B81C2 /* ConstraintLayoutGuideDSL.swift */; };
		4D33ABF05CD58F7AFDD595E2F09465D4 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 98C17B3F0975AEB8522068BB1671D0B0 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-dummy.m */; };
		4F4DEB687C0E4834A5B291DEE0651D6A /* ConstraintMaker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B0AEA3EB83116EC3E7231CF132D8DA9 /* ConstraintMaker.swift */; };
		57C4F6EFB30DDD14E960AC2D6B34F904 /* SnapKit-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = B393D052A1E7E9C9560C94ED07FA7C66 /* SnapKit-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5922A6A0AE7152CF436356B3556F1835 /* ConstraintItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = F1D63C42147F3D45D8395F70C01DF798 /* ConstraintItem.swift */; };
		59F34874DA4ABB2F5C4E09EA6865936B /* ConstraintLayoutGuide.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51411C81BFADDF8F784E8A5B97ABE4CD /* ConstraintLayoutGuide.swift */; };
		6709186EC6C112BEAEC6C2B2FDC3CA70 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = B1FD03EB51DE25FD35D1EAD5BCCA1441 /* PrivacyInfo.xcprivacy */; };
		6B448131017162474260D7691FE72391 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		6E39129FC8643A70C276801FEF4C280D /* Constraint.swift in Sources */ = {isa = PBXBuildFile; fileRef = E19340609C74BD1342C90452BD5B4FF6 /* Constraint.swift */; };
		7AF516B98D45391B909D507D0244104C /* ConstraintDescription.swift in Sources */ = {isa = PBXBuildFile; fileRef = F2A625A0DC98B5CA8384C0497DDDF032 /* ConstraintDescription.swift */; };
		7D42390CDB4FA147504B03DA2A174A0C /* ConstraintViewDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = A3C4BE83192B1881F2D444215B22458C /* ConstraintViewDSL.swift */; };
		85D325033EA4387D788C9CEB3074B59C /* Pods-MTAISDK-Demo-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 00B92D6586C55C658AAA068F8771C7C3 /* Pods-MTAISDK-Demo-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		868A9F524A7985BDA1EA124D9BF4CA63 /* ConstraintDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20602C81DBE436846BAF8C6125F7E007 /* ConstraintDSL.swift */; };
		86CAB01D950C8BC35EDE0BDC01A2500B /* ConstraintView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 012E18F31B4434528EC4B70D3A7CBD9F /* ConstraintView.swift */; };
		883EDEE1C699497CF2A77C3B8A32A790 /* ConstraintMultiplierTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = D9DCA2FF747F0470870B9DA19CC58FD4 /* ConstraintMultiplierTarget.swift */; };
		8AFD2354D62DC9C6257412C298FC0CA2 /* Pods-MTAISDK-DemoTests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 5973054DE54CC0CE28B157136B6535AD /* Pods-MTAISDK-DemoTests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8BABA32F7B94A25D8E9208C0A8D90B2E /* ConstraintMakerRelatable+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 12C8B2779825E81B49315DEA943A10E9 /* ConstraintMakerRelatable+Extensions.swift */; };
		97F15922353F3026FEE8720BE25382C9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		9B9E48DFF50D0C3CEFD641F391588334 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 616888DF0091F596E6EB52564B055E79 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9E0045B41BFE697DB4ADE151228024D2 /* SnapKit-SnapKit_Privacy in Resources */ = {isa = PBXBuildFile; fileRef = B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */; };
		AABEF13464BA7F4621BD94736C1D057C /* ConstraintMakerPrioritizable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 17B84108AD8FEEE4161BD4425B5931DA /* ConstraintMakerPrioritizable.swift */; };
		AE224EDB6D044C0FE86B086E950FC2F9 /* Debugging.swift in Sources */ = {isa = PBXBuildFile; fileRef = 587E1AFA22841ED54F0B405083DAFB58 /* Debugging.swift */; };
		AF760C78F1C7E11BF7CB9E9B29903530 /* ConstraintInsets.swift in Sources */ = {isa = PBXBuildFile; fileRef = BB1019D96BD52835E7B6D1DE2809110E /* ConstraintInsets.swift */; };
		B0875E3AB8718E7DFE5C53497C02A15E /* ConstraintLayoutSupport.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27A72570BCD51660E8E5215816D09838 /* ConstraintLayoutSupport.swift */; };
		B903049E7C1BED7918DAB208754107C7 /* ConstraintMakerFinalizable.swift in Sources */ = {isa = PBXBuildFile; fileRef = EC31BE468120B421718CAADF1723FC92 /* ConstraintMakerFinalizable.swift */; };
		BA2FB695DEB0D179253EEB8DFCE3578B /* SnapKit-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = D535817BC2E5C0CEF28383944DD6F844 /* SnapKit-dummy.m */; };
		BDA5C7CC91E86448237CF40954FAC5AF /* ConstraintMakerRelatable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A387F144056BD1593B18022A938F6093 /* ConstraintMakerRelatable.swift */; };
		BF1AE4D97E813B95C43EA4A298B973D1 /* LayoutConstraint.swift in Sources */ = {isa = PBXBuildFile; fileRef = D4F3EBB3FD715B1AA0B4DE96F0ACF4EC /* LayoutConstraint.swift */; };
		C07CB3E9A4D1BF00F841E4285629A2B2 /* ConstraintRelatableTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1E4896E072CDC3DF3427F09F24BF0E40 /* ConstraintRelatableTarget.swift */; };
		C14F10B663FE2898EACAB90C202B3F50 /* ConstraintMakerEditable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0F38AD669A4A13134403CDEEB9937ED4 /* ConstraintMakerEditable.swift */; };
		C6A4302ACE006C4E2CDD481287E2916B /* Typealiases.swift in Sources */ = {isa = PBXBuildFile; fileRef = E31376FCA4F439DED09B0F6627399548 /* Typealiases.swift */; };
		C6F45595676957ADBEC18EB3F23EAEC4 /* LayoutConstraintItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0EF44FE64FCA4ACA4E3F37488DA9BEE /* LayoutConstraintItem.swift */; };
		CE593943A9E7CF83822CF60304BCAD43 /* ConstraintConstantTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2635463178B3E8802CE18E0466F0A5E /* ConstraintConstantTarget.swift */; };
		D4218DA55B2BA45937589200CC0DF1FB /* ConstraintMakerExtendable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F5620037A88373341C572871C5FFB62 /* ConstraintMakerExtendable.swift */; };
		DBA4803F4765E1650B8C6841157F5D73 /* ConstraintPriorityTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E12191FFF32C8211C0ABBADFC92FF71 /* ConstraintPriorityTarget.swift */; };
		E37671A03B4C17A1CF3766A6125833BB /* ConstraintDirectionalInsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0108DD77CD42DC5050040074A3EB06B7 /* ConstraintDirectionalInsetTarget.swift */; };
		E3D779DEE753C0B0D33BA8E73A980265 /* ConstraintLayoutGuide+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 692C7432002D55943BF8AC866AF7333C /* ConstraintLayoutGuide+Extensions.swift */; };
		ECC5C2ADC2682F9171FEA22AF10DCE53 /* ConstraintRelation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 90D141B8F92646B088015629442E5A0C /* ConstraintRelation.swift */; };
		EE2FE29CFC992EB13EC00CE57C9796F0 /* Pods-MTAISDK-DemoTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = C8F911498295C41579C0FB6EAF435819 /* Pods-MTAISDK-DemoTests-dummy.m */; };
		F0D264D9EA7066F7724B67B79AA24366 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		F6F33E8B268F3D41075374D95B8088DC /* UILayoutSupport+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 164DBE52F60515A603635DA50C9EC1C5 /* UILayoutSupport+Extensions.swift */; };
		F9EBA65892D78A31C068D727D84BCB88 /* ConstraintConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DD19394C90076DA2D462DA402D67A4F /* ConstraintConfig.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		01286C1675C76CDBF362F0FCBF8B610D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = FC329C2B3FCA1F25298C3AFF669647F5;
			remoteInfo = mtlabrecord;
		};
		076D8B95EE711C8E591BEE9AE24194CA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0A4E93A920C41658659C81ADE6AD435B;
			remoteInfo = libmtaiinterface;
		};
		09069672CA29661511849425278080FC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4B6589A87574893065F6CEA7FD28C290;
			remoteInfo = libmanis;
		};
		21AE02CACC50BC427AB6A41499C842FA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 731483C741D3522F4DC8242EC19A6C3C;
			remoteInfo = mvgif;
		};
		2EC6993B1501E6DC61AB19E0079C2407 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 19622742EBA51E823D6DAE3F8CDBFAD4;
			remoteInfo = SnapKit;
		};
		4F4B621A1D5B9038C3BFEEF8405659D8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 09A4CA6090EF04F652EAB0074A1330E6;
			remoteInfo = "Pods-MTAISDK-Demo";
		};
		651CFBE09F73F2446C448A16CDD570AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0A4E93A920C41658659C81ADE6AD435B;
			remoteInfo = libmtaiinterface;
		};
		694CD45162556CB918CA15B0A00DF92C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = FC329C2B3FCA1F25298C3AFF669647F5;
			remoteInfo = mtlabrecord;
		};
		7597D4D973970FFA89E5B677E9BA8E9F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 19622742EBA51E823D6DAE3F8CDBFAD4;
			remoteInfo = SnapKit;
		};
		848BFCBCF54D90408DE4D3EBF4A451AA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = FC329C2B3FCA1F25298C3AFF669647F5;
			remoteInfo = mtlabrecord;
		};
		88712F38BB5311B03DA646813FE1AA89 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 731483C741D3522F4DC8242EC19A6C3C;
			remoteInfo = mvgif;
		};
		E3521A03BBC4742B91E5CB3A6A05359F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8A8DB685241263AFDF5E6B20FE67B93A;
			remoteInfo = "SnapKit-SnapKit_Privacy";
		};
		F57C4BEC00E9C0622AA8DCB6A961590B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4B6589A87574893065F6CEA7FD28C290;
			remoteInfo = libmanis;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		001D5144C990C4188AF4D9DFA5836A75 /* MTAiHandOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHandOption.h; path = iOS/MTAiInterface.framework/Headers/MTHandModule/MTAiHandOption.h; sourceTree = "<group>"; };
		00285EB3599220B5EB96B612CD04A883 /* MTOrnamentModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTOrnamentModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTOrnamentModule/MTOrnamentModuleOption.h; sourceTree = "<group>"; };
		00B92D6586C55C658AAA068F8771C7C3 /* Pods-MTAISDK-Demo-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-MTAISDK-Demo-umbrella.h"; sourceTree = "<group>"; };
		00D30660E334EBC9C10C41ED3812C996 /* MTSceneryBoundaryLineResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSceneryBoundaryLineResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSceneryBoundaryLineModule/MTSceneryBoundaryLineResult.h; sourceTree = "<group>"; };
		0108DD77CD42DC5050040074A3EB06B7 /* ConstraintDirectionalInsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDirectionalInsetTarget.swift; path = Sources/ConstraintDirectionalInsetTarget.swift; sourceTree = "<group>"; };
		011CCA9CA551C9A8682E02EBA4C8E750 /* SnapKit.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SnapKit.modulemap; sourceTree = "<group>"; };
		012E18F31B4434528EC4B70D3A7CBD9F /* ConstraintView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintView.swift; path = Sources/ConstraintView.swift; sourceTree = "<group>"; };
		014773D80D4A24CED2CE2DCC89418028 /* MTSubFaceAnalysis.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubFaceAnalysis.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubFaceAnalysis/MTSubFaceAnalysis.h; sourceTree = "<group>"; };
		0156CD6A3712FAC4E62A8EC06BC8CFA2 /* MTAiEngineTextureUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineTextureUtils.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineTextureUtils.h; sourceTree = "<group>"; };
		02286C688478F6BCB57C30210EB3D117 /* MTSubAIEngineAnalysis.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubAIEngineAnalysis.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubAIEngineAnalysis/MTSubAIEngineAnalysis.h; sourceTree = "<group>"; };
		024CE261211368FA1A7A4CF66323F71D /* MTRestoreTeethUtility.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTRestoreTeethUtility.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTRestoreTeethModule/MTRestoreTeethUtility.h; sourceTree = "<group>"; };
		02E55D26CFE50916985D8E86BAE9CCE5 /* MTMakeupModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTMakeupModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTMakeupModule/MTMakeupModuleOption.h; sourceTree = "<group>"; };
		032DA0EDCC9EE590222CE1DDB0F52EBA /* MTRTTeethRetouchResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTRTTeethRetouchResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTRTTeethRetouchModule/MTRTTeethRetouchResult.h; sourceTree = "<group>"; };
		036B26F09808629D6B188E113742F717 /* MTAiFood.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFood.h; path = iOS/MTAiInterface.framework/Headers/MTFoodModule/MTAiFood.h; sourceTree = "<group>"; };
		03FBFA5177C02C673250BD42C7EAD426 /* MTRestoreTeethModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTRestoreTeethModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTRestoreTeethModule/MTRestoreTeethModuleOption.h; sourceTree = "<group>"; };
		058D2F10F87B59747BF6D7E716A42C66 /* MTVector.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTVector.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTVector.h; sourceTree = "<group>"; };
		063AD3379EC3AD598F80DD40BD1D28B7 /* MTAiOrnamentOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiOrnamentOption.h; path = iOS/MTAiInterface.framework/Headers/MTOrnamentModule/MTAiOrnamentOption.h; sourceTree = "<group>"; };
		0750AA723EEAB61F944EC465BA40968C /* MTPortraitInpaintingResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTPortraitInpaintingResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTPortraitInpaintingModule/MTPortraitInpaintingResult.h; sourceTree = "<group>"; };
		07968C4F4D0AB4C6A603E6A2BE7835A1 /* Pods-MTAISDK-Demo */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-MTAISDK-Demo"; path = Pods_MTAISDK_Demo.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		08B6DF856CF3A0BBE28C55B83062FE92 /* MTNevusDetectionModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTNevusDetectionModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTNevusDetectionModule/MTNevusDetectionModuleOption.h; sourceTree = "<group>"; };
		09761B4CEC7F7346B9E38DF25BA350BA /* MTAiHairStraight.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairStraight.h; path = iOS/MTAiInterface.framework/Headers/MTHairStraightModule/MTAiHairStraight.h; sourceTree = "<group>"; };
		09ABF3B8119FD3F4F00135C751E96EE2 /* mtstitch_caption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mtstitch_caption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtstitch/mtstitch_caption.h; sourceTree = "<group>"; };
		0AF5ED1324592889C66CC0F4552DFD95 /* ConstraintInsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintInsetTarget.swift; path = Sources/ConstraintInsetTarget.swift; sourceTree = "<group>"; };
		0B2B97A931B2272C9FFF823D2BC14952 /* MTAiWrinkleDetectionOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiWrinkleDetectionOption.h; path = iOS/MTAiInterface.framework/Headers/MTWrinkleDetectionModule/MTAiWrinkleDetectionOption.h; sourceTree = "<group>"; };
		0B5FCC199F24FA4100CED46D22F1CCF8 /* MTAiSkinToneMappingResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinToneMappingResult.h; path = iOS/MTAiInterface.framework/Headers/MTSkinToneMappingModule/MTAiSkinToneMappingResult.h; sourceTree = "<group>"; };
		0C0417B27E7579F39633D343D8B09FF8 /* MTAiHuman3dResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHuman3dResult.h; path = iOS/MTAiInterface.framework/Headers/MTHuman3dModule/MTAiHuman3dResult.h; sourceTree = "<group>"; };
		0C08E9263742D27691289F4A576B81C2 /* ConstraintLayoutGuideDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutGuideDSL.swift; path = Sources/ConstraintLayoutGuideDSL.swift; sourceTree = "<group>"; };
		0C7C57D0AB7BA11B0D57AD922C65520B /* MTAiAnimalAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAnimalAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTAnimalModule/Attribute/MTAiAnimalAttribute.h; sourceTree = "<group>"; };
		0D377B69EB7D10649236E52A8BE1214E /* MTTeethResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTTeethResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTTeethModule/MTTeethResult.h; sourceTree = "<group>"; };
		0D7025859B9D7EBA432B841ABE10E2B4 /* MTAiHairAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTHairModule/Attribute/MTAiHairAttribute.h; sourceTree = "<group>"; };
		0D9B34405E8FB09DF6CF7099093CEC5D /* MeituAiEngine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MeituAiEngine.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MeituAiEngine.h; sourceTree = "<group>"; };
		0EF784CCA1F4F71822C46E62807E80C1 /* MTAiMakeupAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiMakeupAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTMakeupModule/Attribute/MTAiMakeupAttribute.h; sourceTree = "<group>"; };
		0F38AD669A4A13134403CDEEB9937ED4 /* ConstraintMakerEditable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerEditable.swift; path = Sources/ConstraintMakerEditable.swift; sourceTree = "<group>"; };
		0F568AFFFF21C005AF6A928423D858FD /* MTSkinARModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinARModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinARModule/MTSkinARModuleOption.h; sourceTree = "<group>"; };
		0F8FF42D2BDED09B3E146BEE4AB074BF /* MTSubColorToningType.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorToningType.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubColorToning/MTSubColorToningType.h; sourceTree = "<group>"; };
		1020031E4B9DF801A4CF2CAB94DA0C44 /* MTSkinMicroResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinMicroResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinMicroModule/MTSkinMicroResult.h; sourceTree = "<group>"; };
		10B5147A75EA09BB3FDA0F53B2626B21 /* MTAiAnchorGeneration.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAnchorGeneration.h; path = iOS/MTAiInterface.framework/Headers/MTAnchorGenerationModule/MTAiAnchorGeneration.h; sourceTree = "<group>"; };
		11C7272C9A7CB9F8E5280CC0EA87DEA6 /* MTVideoStabilizationResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTVideoStabilizationResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTVideoStabilizationModule/MTVideoStabilizationResult.h; sourceTree = "<group>"; };
		12C8B2779825E81B49315DEA943A10E9 /* ConstraintMakerRelatable+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintMakerRelatable+Extensions.swift"; path = "Sources/ConstraintMakerRelatable+Extensions.swift"; sourceTree = "<group>"; };
		1359D6A0F11942057776E7E305FEC84E /* MTAiBody.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiBody.h; path = iOS/MTAiInterface.framework/Headers/MTBodyModule/MTAiBody.h; sourceTree = "<group>"; };
		14509E5038F428D72F54B102D44FE43B /* MTHighDofEyelidResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHighDofEyelidResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHighDofEyelidModule/MTHighDofEyelidResult.h; sourceTree = "<group>"; };
		14D9C6530870475BA04D057BF4AD0241 /* MTAiFaceHDResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceHDResult.h; path = iOS/MTAiInterface.framework/Headers/MTFaceHDModule/MTAiFaceHDResult.h; sourceTree = "<group>"; };
		14DDA0D06CDF7A9F6508266788A10B44 /* MTHairModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairModule/MTHairModuleOption.h; sourceTree = "<group>"; };
		15825EE6BA62F33189390EABC3A194FA /* MTAiSkinMicro.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinMicro.h; path = iOS/MTAiInterface.framework/Headers/MTSkinMicroModule/MTAiSkinMicro.h; sourceTree = "<group>"; };
		159367A4486D08E0E71136D3FFEB7C61 /* MTAiEngineLogDelegate.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineLogDelegate.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/iOS/MTAiEngineLogDelegate.h; sourceTree = "<group>"; };
		15C3D1CDD4319E6C64D36308295DF804 /* MTFoodStyleModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFoodStyleModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFoodStyleModule/MTFoodStyleModuleOption.h; sourceTree = "<group>"; };
		15F41B65D2950438D39A5CAB27E391FA /* MTAiEngineEnableOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineEnableOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineEnableOption.h; sourceTree = "<group>"; };
		15FD2DB774088207D1FEFB49293633FD /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests-resources.sh"; sourceTree = "<group>"; };
		164DBE52F60515A603635DA50C9EC1C5 /* UILayoutSupport+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UILayoutSupport+Extensions.swift"; path = "Sources/UILayoutSupport+Extensions.swift"; sourceTree = "<group>"; };
		164EE4515360B1D0E3C08A2ACF689197 /* MTAiEngineResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineResult.h; sourceTree = "<group>"; };
		1678A2DD03AB1412D323B612AA697985 /* MTAiVideoOptimizerOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoOptimizerOption.h; path = iOS/MTAiInterface.framework/Headers/MTVideoOptimizerModule/MTAiVideoOptimizerOption.h; sourceTree = "<group>"; };
		16C18D39B8EC9E88F20B0997FFEA8151 /* Manis.metallib */ = {isa = PBXFileReference; includeInIndex = 1; name = Manis.metallib; path = iOS/universal/metallib/Manis.metallib; sourceTree = "<group>"; };
		171ABCFB481B695F5E1546740303E247 /* MTAikiev3DMakeResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAikiev3DMakeResult.h; path = iOS/MTAiInterface.framework/Headers/MTKiev3DMakeModule/MTAikiev3DMakeResult.h; sourceTree = "<group>"; };
		178030E25A3578775413AD9B0812379E /* MTAiEngineGLInclude.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineGLInclude.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineGLInclude.h; sourceTree = "<group>"; };
		178D086A106785DB66D8067BCDDB5BA5 /* MTAiRTTeethRetouch.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiRTTeethRetouch.h; path = iOS/MTAiInterface.framework/Headers/MTRTTeethRetouchModule/MTAiRTTeethRetouch.h; sourceTree = "<group>"; };
		179B2B015CB0F7CE0E93139EABB105F4 /* MTAiDenseHairResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiDenseHairResult.h; path = iOS/MTAiInterface.framework/Headers/MTDenseHairModule/MTAiDenseHairResult.h; sourceTree = "<group>"; };
		17B84108AD8FEEE4161BD4425B5931DA /* ConstraintMakerPrioritizable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerPrioritizable.swift; path = Sources/ConstraintMakerPrioritizable.swift; sourceTree = "<group>"; };
		18340AC2A6E2402B65EC2C6128850792 /* MTLandmarkResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTLandmarkResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTLandmarkModule/MTLandmarkResult.h; sourceTree = "<group>"; };
		******************************** /* MTSkinResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinModule/MTSkinResult.h; sourceTree = "<group>"; };
		190912AB5F57BA1D558233514E9E1C4E /* MTFoodStyleResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFoodStyleResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFoodStyleModule/MTFoodStyleResult.h; sourceTree = "<group>"; };
		196D358355876FCBDFF16F0F73151C60 /* MTAiEngineLog.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineLog.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineLog.h; sourceTree = "<group>"; };
		19C31FB904A5D41B6A7C941C50CFE3D1 /* MTAiShoulderOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiShoulderOption.h; path = iOS/MTAiInterface.framework/Headers/MTShoulderModule/MTAiShoulderOption.h; sourceTree = "<group>"; };
		19D75636C125B161FF172AF05CD8C8A5 /* MTAiSegment.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSegment.h; path = iOS/MTAiInterface.framework/Headers/MTSegmentModule/MTAiSegment.h; sourceTree = "<group>"; };
		1AABBB5BD0ECF440F15EDC013F43740C /* MTAiDL3DOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiDL3DOption.h; path = iOS/MTAiInterface.framework/Headers/MTDL3DModule/MTAiDL3DOption.h; sourceTree = "<group>"; };
		1B932D0E06BC5DE49342AB3AF0882654 /* MTAiLandmarkOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiLandmarkOption.h; path = iOS/MTAiInterface.framework/Headers/MTLandmarkModule/MTAiLandmarkOption.h; sourceTree = "<group>"; };
		1C2D4FC6526B0FCC59FC11235BEF677C /* MTAiSegmentOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSegmentOption.h; path = iOS/MTAiInterface.framework/Headers/MTSegmentModule/MTAiSegmentOption.h; sourceTree = "<group>"; };
		1E0302658CFDDE5CCDB1A70200623FE2 /* MTAiMaterialTrackingOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiMaterialTrackingOption.h; path = iOS/MTAiInterface.framework/Headers/MTMaterialTrackingModule/MTAiMaterialTrackingOption.h; sourceTree = "<group>"; };
		1E4896E072CDC3DF3427F09F24BF0E40 /* ConstraintRelatableTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintRelatableTarget.swift; path = Sources/ConstraintRelatableTarget.swift; sourceTree = "<group>"; };
		1E6472FB5DAD265ECF3D9849360F3A2F /* mvgif.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = mvgif.framework; path = ios/mvgif.framework; sourceTree = "<group>"; };
		1E7C12E2407A8AABCE0CCB0284922599 /* MTAiEnginePlatform.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEnginePlatform.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEnginePlatform.h; sourceTree = "<group>"; };
		20602C81DBE436846BAF8C6125F7E007 /* ConstraintDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDSL.swift; path = Sources/ConstraintDSL.swift; sourceTree = "<group>"; };
		217D1290BAFEBFBE20FFF2EC043E7ED9 /* MTApmUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTApmUtils.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/Apm/MTApmUtils.h; sourceTree = "<group>"; };
		2234919422550AE8AEB678F75F818DE1 /* MTNoseBlendModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTNoseBlendModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTNoseBlendModule/MTNoseBlendModuleOption.h; sourceTree = "<group>"; };
		2265594E2C0EB2307E41BA2FBE3E9E7C /* MTPortraitDetectionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTPortraitDetectionResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTPortraitDetectionModule/MTPortraitDetectionResult.h; sourceTree = "<group>"; };
		2300BCC4EEBEECA8F40B9E397F771AFD /* MTAIKitResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAIKitResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTAIKitModule/MTAIKitResult.h; sourceTree = "<group>"; };
		2375E2BEE8BB70AF37ABB777B38FF8FF /* MTFaceModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceModule/MTFaceModuleOption.h; sourceTree = "<group>"; };
		245E6EB7C0A47B25619035F7A5336E21 /* MTAiFoodStyleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFoodStyleOption.h; path = iOS/MTAiInterface.framework/Headers/MTFoodStyleModule/MTAiFoodStyleOption.h; sourceTree = "<group>"; };
		27A72570BCD51660E8E5215816D09838 /* ConstraintLayoutSupport.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutSupport.swift; path = Sources/ConstraintLayoutSupport.swift; sourceTree = "<group>"; };
		28D2DECFBE03B74485CE59E4EBFB70E9 /* MTVideoRecognitionModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTVideoRecognitionModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTVideoRecognitionModule/MTVideoRecognitionModuleOption.h; sourceTree = "<group>"; };
		2A75056332507A3D28753B74BED70748 /* Pods-MTAISDK-Demo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-MTAISDK-Demo.release.xcconfig"; sourceTree = "<group>"; };
		2A8DD038146DC553CB97CB7FCE945ADE /* MTCgStyleModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTCgStyleModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTCgStyleModule/MTCgStyleModuleOption.h; sourceTree = "<group>"; };
		2CB8B7E65817BF9089BA076D38474E87 /* Pods-MTAISDK-Demo-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-MTAISDK-Demo-acknowledgements.markdown"; sourceTree = "<group>"; };
		2E0C2F6B52B37E811FA40ABD102AA294 /* Pods-MTAISDK-DemoTests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-MTAISDK-DemoTests"; path = Pods_MTAISDK_DemoTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2F8489773D7E3CBCA6DE4A9D4877487B /* MTSubIntelligentFusion.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubIntelligentFusion.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/IntelligentFusion/MTSubIntelligentFusion.h; sourceTree = "<group>"; };
		3036D0B5EF6AE3C85C79B16E3FD78206 /* MTDenseHairResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTDenseHairResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTDenseHairModule/MTDenseHairResult.h; sourceTree = "<group>"; };
		30D57168482462E36BFB09C70D1ECC83 /* MTWrinkleDetectionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTWrinkleDetectionResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTWrinkleDetectionModule/MTWrinkleDetectionResult.h; sourceTree = "<group>"; };
		3149C43670A8094936669DD1934A280C /* MTEyelidImageModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEyelidImageModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEyelidImageModule/MTEyelidImageModuleOption.h; sourceTree = "<group>"; };
		318E7FCBE64F5C04EDC5CDD26B544D6C /* MTSubFrameSelect.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubFrameSelect.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubFrameSelect/MTSubFrameSelect.h; sourceTree = "<group>"; };
		31CCB145D9EF3C9D1EFD03BE6F645766 /* MTAiAIKitOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAIKitOption.h; path = iOS/MTAiInterface.framework/Headers/MTAIKitModule/MTAiAIKitOption.h; sourceTree = "<group>"; };
		31FAE423D5771AB8AFCFD8DE1E1D2D3E /* MTSubBlendEffect.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubBlendEffect.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/IntelligentFusion/MTSubBlendEffect.h; sourceTree = "<group>"; };
		326CD77FB5F28B101660D62B0D9BF76C /* MTAiSceneryBoundaryLine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSceneryBoundaryLine.h; path = iOS/MTAiInterface.framework/Headers/MTSceneryBoundaryLineModule/MTAiSceneryBoundaryLine.h; sourceTree = "<group>"; };
		32C3B8B847AEEC6D7D89AD81A07E7295 /* MTAiOrnamentResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiOrnamentResult.h; path = iOS/MTAiInterface.framework/Headers/MTOrnamentModule/MTAiOrnamentResult.h; sourceTree = "<group>"; };
		32EE5715EB5CDF739115C245E828425C /* MTSubDoubleChinFix.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubDoubleChinFix.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubDoubleChinFix/MTSubDoubleChinFix.h; sourceTree = "<group>"; };
		33A645A189DDFB394BDCB63B4613D84D /* MTAiFoodOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFoodOption.h; path = iOS/MTAiInterface.framework/Headers/MTFoodModule/MTAiFoodOption.h; sourceTree = "<group>"; };
		33AD9578FA3B2704B5CDAC09E8DA42E1 /* MTAiSkinMicroOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinMicroOption.h; path = iOS/MTAiInterface.framework/Headers/MTSkinMicroModule/MTAiSkinMicroOption.h; sourceTree = "<group>"; };
		352D1BB4BB54B3CDD4316401F3E45C28 /* MTShoulderResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTShoulderResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTShoulderModule/MTShoulderResult.h; sourceTree = "<group>"; };
		35537B002D9C7EC873DD829BD59A23A7 /* MTHighDofEyelidModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHighDofEyelidModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHighDofEyelidModule/MTHighDofEyelidModuleOption.h; sourceTree = "<group>"; };
		359B03AF4BB3A05932B9748FD4C65BE7 /* MTSubColorACUtilGL.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorACUtilGL.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubColorToning/MTSubColorACUtilGL/MTSubColorACUtilGL.h; sourceTree = "<group>"; };
		37669ED41530C98575C7E46BA0613E74 /* MTAiFaceResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceResult.h; path = iOS/MTAiInterface.framework/Headers/MTFaceModule/MTAiFaceResult.h; sourceTree = "<group>"; };
		3778513DBDFE0A37C770260E11953175 /* MTAiEngineOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineOption.h; sourceTree = "<group>"; };
		384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		38688426D871410D0D5EB59883CFC088 /* MTSubDLTextureInpainting.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubDLTextureInpainting.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubDLTextureInpainting/MTSubDLTextureInpainting.h; sourceTree = "<group>"; };
		3A1C149CD1E27755E4FB5840A569EE4C /* MTAiSkinToneMapping.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinToneMapping.h; path = iOS/MTAiInterface.framework/Headers/MTSkinToneMappingModule/MTAiSkinToneMapping.h; sourceTree = "<group>"; };
		3A44FC37A848EBCB5B71C69DE617EA3C /* InceptionBeautyDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = InceptionBeautyDefine.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtinceptionbeauty/InceptionBeautyDefine.h; sourceTree = "<group>"; };
		3AA908141A906DC0A050BB7A4ED557EB /* ConstraintOffsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintOffsetTarget.swift; path = Sources/ConstraintOffsetTarget.swift; sourceTree = "<group>"; };
		3B160BA3C7634CEA52937C152FB3F654 /* MTSubAiPhotoTimelapseAdaptor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubAiPhotoTimelapseAdaptor.h; path = iOS/MTAiInterface.framework/Headers/MT3rtpartyModule/MTPhotoTimelapse/MTSubAiPhotoTimelapseAdaptor.h; sourceTree = "<group>"; };
		3B24C6502C5CBB0D966D40FA3BCACEE1 /* ConstraintLayoutSupportDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutSupportDSL.swift; path = Sources/ConstraintLayoutSupportDSL.swift; sourceTree = "<group>"; };
		3B3B9F08639325065AFFA00976F3C733 /* MTFrame.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFrame.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTFrame.h; sourceTree = "<group>"; };
		3C1C6CB214DF6E61247EB2D900DC9FE5 /* MTAiEngineLogReflection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineLogReflection.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineLogReflection.h; sourceTree = "<group>"; };
		3C7283F93F12ABD00082645B6FA964BC /* MTSubAiVideoRecognitionAdaptor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubAiVideoRecognitionAdaptor.h; path = iOS/MTAiInterface.framework/Headers/MT3rtpartyModule/VideoRecognition/MTSubAiVideoRecognitionAdaptor.h; sourceTree = "<group>"; };
		3CC5C0B54A36F01E2AC87CBAF707EE81 /* MTSubEyeLifting.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubEyeLifting.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubEyeLifting/MTSubEyeLifting.h; sourceTree = "<group>"; };
		3D51315A901AFD4473A402D0A3D8F4D1 /* mtlabrecord.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = mtlabrecord.release.xcconfig; sourceTree = "<group>"; };
		******************************** /* MTAiPortraitInpaintingResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiPortraitInpaintingResult.h; path = iOS/MTAiInterface.framework/Headers/MTPortraitInpaintingModule/MTAiPortraitInpaintingResult.h; sourceTree = "<group>"; };
		3EF5A5F2AA9C5F1D5085DC90A54B8F4D /* MTSubFaceRefinerEngine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubFaceRefinerEngine.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubFaceRefinerEngine/MTSubFaceRefinerEngine.h; sourceTree = "<group>"; };
		3F624352DC4C6B9F9998FE5DE4BE1427 /* MTDL3DModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTDL3DModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTDL3DModule/MTDL3DModuleOption.h; sourceTree = "<group>"; };
		3F79E174DBDA947AAA2FAD2E3B8FB4AD /* MTAiCgStyle.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiCgStyle.h; path = iOS/MTAiInterface.framework/Headers/MTCgStyleModule/MTAiCgStyle.h; sourceTree = "<group>"; };
		40D9B903682F39EC3CEAFFD12365DBB6 /* MTAiHuman3dOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHuman3dOption.h; path = iOS/MTAiInterface.framework/Headers/MTHuman3dModule/MTAiHuman3dOption.h; sourceTree = "<group>"; };
		41DAC6D8924B29C7F74305BCE93A78D4 /* MTAiHairGrouthOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairGrouthOption.h; path = iOS/MTAiInterface.framework/Headers/MTHairGrouthModule/MTAiHairGrouthOption.h; sourceTree = "<group>"; };
		422193BE3220F1F4C2F3D771C73FED87 /* MTSubFaceAnalysis.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubFaceAnalysis.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubFaceAnalysis/MTSubFaceAnalysis.h; sourceTree = "<group>"; };
		422DDA698093A8550F603D7DA217238C /* MTSkinModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinModule/MTSkinModuleOption.h; sourceTree = "<group>"; };
		42478C16B94B5DE3D505402CA0017E4B /* MTSubColorToningEW.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorToningEW.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubColorToning/MTSubColorToningEW/MTSubColorToningEW.h; sourceTree = "<group>"; };
		42485051DCA6A84BFDF2D052A7BD4CD7 /* MTEyelidRealtimeResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEyelidRealtimeResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEyelidRealtimeModule/MTEyelidRealtimeResult.h; sourceTree = "<group>"; };
		42C682D99D1B37031853708D51B6AF88 /* MTFaceResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceModule/MTFaceResult.h; sourceTree = "<group>"; };
		42F1ED460E96E2B3CA2C771BD2EDA164 /* MTAiEyelidImageResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEyelidImageResult.h; path = iOS/MTAiInterface.framework/Headers/MTEyelidImageModule/MTAiEyelidImageResult.h; sourceTree = "<group>"; };
		434EDD7D9783EA82DA26AC5FF8D5D256 /* MTToKidResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTToKidResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTToKidModule/MTToKidResult.h; sourceTree = "<group>"; };
		436F6EAC5516875378932A6C47C1E828 /* MTAiEngineType.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineType.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineType.h; sourceTree = "<group>"; };
		43B448D73F7AE619E385EFA276203D12 /* MTAiEveQualityOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEveQualityOption.h; path = iOS/MTAiInterface.framework/Headers/MTEveQualityModule/MTAiEveQualityOption.h; sourceTree = "<group>"; };
		43EF74B1D521DDCBEB9D1775488C873C /* SnapKit-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SnapKit-prefix.pch"; sourceTree = "<group>"; };
		4488060A266116B419C842ABEA1CB92C /* MTSubColorToning.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorToning.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubColorToning/MTSubColorToning/MTSubColorToning.h; sourceTree = "<group>"; };
		459A913A4AD105197A407419752FA697 /* MTKiev3DMakeResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTKiev3DMakeResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTKiev3DMakeModule/MTKiev3DMakeResult.h; sourceTree = "<group>"; };
		45CFDFA432F39BF3E4E688D51F311C8C /* MTCgStyleResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTCgStyleResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTCgStyleModule/MTCgStyleResult.h; sourceTree = "<group>"; };
		464E4CAE6DDEAF4DF7F68CA952AAF901 /* MTAiEngineImage+UIImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "MTAiEngineImage+UIImage.h"; path = "iOS/MTAiInterface.framework/Headers/common/MTAiEngineImage+UIImage.h"; sourceTree = "<group>"; };
		46742ACF9E9F416549F386C9568111E1 /* MTSubInceptionBeautyUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubInceptionBeautyUtil.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/InceptionBeauty/MTSubInceptionBeautyUtil.h; sourceTree = "<group>"; };
		474760CC7BA1C27DD48F25C87852E2E8 /* MTSubOpenEye.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubOpenEye.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubOpenEye/MTSubOpenEye.h; sourceTree = "<group>"; };
		474AA5202ACAFE47BE04030A636129C5 /* MTAiBodyInOne.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiBodyInOne.h; path = iOS/MTAiInterface.framework/Headers/MTBodyInOneModule/MTAiBodyInOne.h; sourceTree = "<group>"; };
		47AEB2945A74B3DDF31611E69A6B1E4A /* MTAiDL3D.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiDL3D.h; path = iOS/MTAiInterface.framework/Headers/MTDL3DModule/MTAiDL3D.h; sourceTree = "<group>"; };
		47E0146A1C699A83A56034B4BF94188C /* MTAiSkinOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinOption.h; path = iOS/MTAiInterface.framework/Headers/MTSkinModule/MTAiSkinOption.h; sourceTree = "<group>"; };
		48A9F47AF9111CF246D3848531DE461A /* MTAi3DFaceOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAi3DFaceOption.h; path = iOS/MTAiInterface.framework/Headers/MT3DFaceModule/MTAi3DFaceOption.h; sourceTree = "<group>"; };
		48F1ED2B029AC222D147E01330B28368 /* MTAi3DFace.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAi3DFace.h; path = iOS/MTAiInterface.framework/Headers/MT3DFaceModule/MTAi3DFace.h; sourceTree = "<group>"; };
		49366AC1E35D61CCB1B8B7DBEFB83074 /* MTVideoOptimizerResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTVideoOptimizerResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTVideoOptimizerModule/MTVideoOptimizerResult.h; sourceTree = "<group>"; };
		498D1059B5B35D7E06DB319B43819856 /* MTSubOpenEye.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubOpenEye.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubOpenEye/MTSubOpenEye.h; sourceTree = "<group>"; };
		49A5D25C0D01C8F4AC63A1966DA71306 /* MTAiSkinBCCOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinBCCOption.h; path = iOS/MTAiInterface.framework/Headers/MTSkinBCCModule/MTAiSkinBCCOption.h; sourceTree = "<group>"; };
		4A7537FA87775343DE7E141E19601D2F /* MTImageDetectionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTImageDetectionResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTImageDetectionModule/MTImageDetectionResult.h; sourceTree = "<group>"; };
		4AF5F960AAD70E7604E28987E0016AAE /* MTSubColorACUtilGL.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorACUtilGL.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubColorToning/MTSubColorACUtilGL/MTSubColorACUtilGL.h; sourceTree = "<group>"; };
		4BFE5C5116E5EFF071F73ADE6A3A63CA /* Pods-MTAISDK-DemoTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-MTAISDK-DemoTests.release.xcconfig"; sourceTree = "<group>"; };
		4C3348D1920C0CD5C8B5CFF22C4D8850 /* MTAiHair.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHair.h; path = iOS/MTAiInterface.framework/Headers/MTHairModule/MTAiHair.h; sourceTree = "<group>"; };
		4C84506C061BBF608F67C41487F1CBAB /* MTAiSkinToneMappingOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinToneMappingOption.h; path = iOS/MTAiInterface.framework/Headers/MTSkinToneMappingModule/MTAiSkinToneMappingOption.h; sourceTree = "<group>"; };
		4D669C5B1A5DB701931F5285ABFA8803 /* InceptionBeautyRejuvenationUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = InceptionBeautyRejuvenationUtil.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtinceptionbeauty/InceptionBeautyRejuvenationUtil.h; sourceTree = "<group>"; };
		4DA825A23EDA5037B67C0005242B8999 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests-acknowledgements.markdown"; sourceTree = "<group>"; };
		4DAB926AAE845A85D7F03B82E90CAA01 /* libmtaiinterface.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = libmtaiinterface.release.xcconfig; sourceTree = "<group>"; };
		51411C81BFADDF8F784E8A5B97ABE4CD /* ConstraintLayoutGuide.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutGuide.swift; path = Sources/ConstraintLayoutGuide.swift; sourceTree = "<group>"; };
		53B2982DCA16034C036D0AFC5ED41243 /* MTSubPhotoTimelapse.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubPhotoTimelapse.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/MTPhotoTimelapse/MTSubPhotoTimelapse.h; sourceTree = "<group>"; };
		548DFBF21279A938AE713E9FA5893DDA /* MTRTTeethRetouchModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTRTTeethRetouchModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTRTTeethRetouchModule/MTRTTeethRetouchModuleOption.h; sourceTree = "<group>"; };
		5551D21660348845A2384F5B74035665 /* MTDoubleChinFixResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTDoubleChinFixResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTDoubleChinFixModule/MTDoubleChinFixResult.h; sourceTree = "<group>"; };
		563C469D831CF988ABDAA4094ECF125D /* MTSubGazeManipulation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubGazeManipulation.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/libGazeManipulation/MTSubGazeManipulation.h; sourceTree = "<group>"; };
		56480E479C38B52C0B83DD80B3190A04 /* MTAiHighDofEyelidOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHighDofEyelidOption.h; path = iOS/MTAiInterface.framework/Headers/MTHighDofEyelidModule/MTAiHighDofEyelidOption.h; sourceTree = "<group>"; };
		5732BC06945CC6C30DCA22FF67899B72 /* MTAiVideoRecognitionOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoRecognitionOption.h; path = iOS/MTAiInterface.framework/Headers/MTVideoRecognitionModule/MTAiVideoRecognitionOption.h; sourceTree = "<group>"; };
		57916654D6891DE974CAF52208935FC4 /* MTAiShoulder.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiShoulder.h; path = iOS/MTAiInterface.framework/Headers/MTShoulderModule/MTAiShoulder.h; sourceTree = "<group>"; };
		57D558EFE3E37AEAB3C06D4D289DA2CF /* MTAiSkinMicroResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinMicroResult.h; path = iOS/MTAiInterface.framework/Headers/MTSkinMicroModule/MTAiSkinMicroResult.h; sourceTree = "<group>"; };
		57D916785A97C686AB1594AE3E8FE37B /* MTAiHairStraightOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairStraightOption.h; path = iOS/MTAiInterface.framework/Headers/MTHairStraightModule/MTAiHairStraightOption.h; sourceTree = "<group>"; };
		585CAC44BD2EC485C15A33CBE40547C3 /* MTPackageName.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTPackageName.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTPackageName.h; sourceTree = "<group>"; };
		587E1AFA22841ED54F0B405083DAFB58 /* Debugging.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Debugging.swift; path = Sources/Debugging.swift; sourceTree = "<group>"; };
		58C59DBD92A48F2EC535D26020E898B7 /* MTAiEyeSegment.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEyeSegment.h; path = iOS/MTAiInterface.framework/Headers/MTEyeSegmentModule/MTAiEyeSegment.h; sourceTree = "<group>"; };
		58DB902B22B71C44DBABE54B107C4828 /* MTAiImageRecognitionOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiImageRecognitionOption.h; path = iOS/MTAiInterface.framework/Headers/MTImageRecognitionModule/MTAiImageRecognitionOption.h; sourceTree = "<group>"; };
		5973054DE54CC0CE28B157136B6535AD /* Pods-MTAISDK-DemoTests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-MTAISDK-DemoTests-umbrella.h"; sourceTree = "<group>"; };
		5A6C36ADEE6025C328CC6B0DC07E7A43 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig"; sourceTree = "<group>"; };
		5AB975F812E54087DB10BCB97B172FA2 /* MTAiLandmarkResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiLandmarkResult.h; path = iOS/MTAiInterface.framework/Headers/MTLandmarkModule/MTAiLandmarkResult.h; sourceTree = "<group>"; };
		5ABC1B7A5B71C15207ED99C1F6D73057 /* MTAiEyelidRealtimeResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEyelidRealtimeResult.h; path = iOS/MTAiInterface.framework/Headers/MTEyelidRealtimeModule/MTAiEyelidRealtimeResult.h; sourceTree = "<group>"; };
		5AF8F058633C517A1FF54E47FAC4A781 /* MTAiSkinBCC.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinBCC.h; path = iOS/MTAiInterface.framework/Headers/MTSkinBCCModule/MTAiSkinBCC.h; sourceTree = "<group>"; };
		5B0AEA3EB83116EC3E7231CF132D8DA9 /* ConstraintMaker.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMaker.swift; path = Sources/ConstraintMaker.swift; sourceTree = "<group>"; };
		5C2D883A320F44D6046ED546D66BCF13 /* ConstraintPriority.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintPriority.swift; path = Sources/ConstraintPriority.swift; sourceTree = "<group>"; };
		5D2C5D0B9F1A00295902DCA41F37F7AA /* MTRestoreTeethResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTRestoreTeethResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTRestoreTeethModule/MTRestoreTeethResult.h; sourceTree = "<group>"; };
		5D55FFD0F11B032B030E38AB0A90EABE /* MTRemoveWatermarkResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTRemoveWatermarkResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTRemoveWatermarkModule/MTRemoveWatermarkResult.h; sourceTree = "<group>"; };
		5DCBF293526E08F45D00439CF40FDA12 /* MTAiInstanceSeg.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiInstanceSeg.h; path = iOS/MTAiInterface.framework/Headers/MTInstanceSegmentModule/MTAiInstanceSeg.h; sourceTree = "<group>"; };
		5DD88C969112CFEBF1C65FCE6AB43814 /* MTAiKiev3DMake.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiKiev3DMake.h; path = iOS/MTAiInterface.framework/Headers/MTKiev3DMakeModule/MTAiKiev3DMake.h; sourceTree = "<group>"; };
		5E0367893484F71BD8EC95FA840B6B5F /* mtlabrecord.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = mtlabrecord.framework; path = apple/ios/shared/release/universal/mtlabrecord.framework; sourceTree = "<group>"; };
		5EEE9D3FF14AEEF96FF14CAF0347488D /* MTAiEngineResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineResult.h; path = iOS/MTAiInterface.framework/Headers/MTAiEngineResult.h; sourceTree = "<group>"; };
		5F25B435AAAB9E592FF5FEB4B706FC5A /* SnapKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SnapKit-Info.plist"; sourceTree = "<group>"; };
		605650638190F868B93E1EBAA2604B9E /* MTAiHuman3dSmpl.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHuman3dSmpl.h; path = iOS/MTAiInterface.framework/Headers/MTHuman3dModule/MTAiHuman3dSmpl.h; sourceTree = "<group>"; };
		60F2FCCE6C7B2D8BAF2079CA90352D63 /* MTAiEngineImage+Painter.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "MTAiEngineImage+Painter.h"; path = "iOS/MTAiInterface.framework/Headers/common/MTAiEngineImage+Painter.h"; sourceTree = "<group>"; };
		60F6123D3C637CAE6254383416427E5B /* InceptionBeautyUtilGL.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = InceptionBeautyUtilGL.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtinceptionbeauty/InceptionBeautyUtilGL.h; sourceTree = "<group>"; };
		6152983F59F4DD10E13C957F72311C04 /* MTPortraitDetectionModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTPortraitDetectionModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTPortraitDetectionModule/MTPortraitDetectionModuleOption.h; sourceTree = "<group>"; };
		616888DF0091F596E6EB52564B055E79 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests-umbrella.h"; sourceTree = "<group>"; };
		61A1F5B162A3D83B8F056B6F76A45214 /* MTAiFaceAnalysisX.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceAnalysisX.h; path = iOS/MTAiInterface.framework/Headers/MTFaceAnalysisXModule/MTAiFaceAnalysisX.h; sourceTree = "<group>"; };
		62540477461C3BA53EB9468D79AA2A5E /* MTFoodModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFoodModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFoodModule/MTFoodModuleOption.h; sourceTree = "<group>"; };
		626F1C305B50370489286F63E746CE5B /* MTAiTeethResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiTeethResult.h; path = iOS/MTAiInterface.framework/Headers/MTTeethModule/MTAiTeethResult.h; sourceTree = "<group>"; };
		62C0881686EC20934785A7377D755544 /* MTAiEyelidImageOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEyelidImageOption.h; path = iOS/MTAiInterface.framework/Headers/MTEyelidImageModule/MTAiEyelidImageOption.h; sourceTree = "<group>"; };
		62E81994604749F98DA10745F5B61736 /* mtlabrecord.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = mtlabrecord.debug.xcconfig; sourceTree = "<group>"; };
		636611703D6177AECC616089B16C50F2 /* MTHairGrouthResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairGrouthResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairGrouthModule/MTHairGrouthResult.h; sourceTree = "<group>"; };
		64211AC72AD7702428F048989E35E855 /* MTAiRemoveWatermarkResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiRemoveWatermarkResult.h; path = iOS/MTAiInterface.framework/Headers/MTRemoveWatermarkModule/MTAiRemoveWatermarkResult.h; sourceTree = "<group>"; };
		6444163469C702704A368F44C67686F1 /* MTAiImageRecognitionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiImageRecognitionResult.h; path = iOS/MTAiInterface.framework/Headers/MTImageRecognitionModule/MTAiImageRecognitionResult.h; sourceTree = "<group>"; };
		64B87B2DF61FEBE119E1A9695881B928 /* MTAiShoulderResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiShoulderResult.h; path = iOS/MTAiInterface.framework/Headers/MTShoulderModule/MTAiShoulderResult.h; sourceTree = "<group>"; };
		665177B79034E8AF5BCD3BB290DF264A /* MTHairFluffyResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairFluffyResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairFluffyModule/MTHairFluffyResult.h; sourceTree = "<group>"; };
		66657826CC350E3661ECBDB2ACBA7191 /* MTAiHandResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHandResult.h; path = iOS/MTAiInterface.framework/Headers/MTHandModule/MTAiHandResult.h; sourceTree = "<group>"; };
		67604714ACCD307595663392C964481C /* MTFaceBlitModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceBlitModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceBlitModule/MTFaceBlitModuleOption.h; sourceTree = "<group>"; };
		67AE46A5E3FE5D6F9A145804C82A150A /* MTDenseHairModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTDenseHairModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTDenseHairModule/MTDenseHairModuleOption.h; sourceTree = "<group>"; };
		682E9815C66D408ACA1D18DC321E90EF /* MTEyeSegmentResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEyeSegmentResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEyeSegmentModule/MTEyeSegmentResult.h; sourceTree = "<group>"; };
		68E20D2ED2761482625D7B5C2FBFA6F0 /* MTHuman3dModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHuman3dModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHuman3dModule/MTHuman3dModuleOption.h; sourceTree = "<group>"; };
		692C7432002D55943BF8AC866AF7333C /* ConstraintLayoutGuide+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintLayoutGuide+Extensions.swift"; path = "Sources/ConstraintLayoutGuide+Extensions.swift"; sourceTree = "<group>"; };
		6979F4CC8A3069671DB36A85BE5C976C /* MTEveSkinModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEveSkinModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEveSkinModule/MTEveSkinModuleOption.h; sourceTree = "<group>"; };
		69FBAB24A451CD5CB69E613032F4AF4F /* MTAiEvePreDetectOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEvePreDetectOption.h; path = iOS/MTAiInterface.framework/Headers/MTEvePreDetectModule/MTAiEvePreDetectOption.h; sourceTree = "<group>"; };
		6A261181EEE7DACF251DA4E30E8B34D9 /* MTSmileResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSmileResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSmileModule/MTSmileResult.h; sourceTree = "<group>"; };
		6A2ADCEBB47568FD54816164D3D1C25A /* MTImageDetectionModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTImageDetectionModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTImageDetectionModule/MTImageDetectionModuleOption.h; sourceTree = "<group>"; };
		6AAF683C445D331CC003E2DB9FE2FD43 /* MTSubSegment.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubSegment.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubSegment/MTSubSegment.h; sourceTree = "<group>"; };
		6B04455540DC2284A2B84B3B1DA6CC6D /* MTAiInstanceSegmentResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiInstanceSegmentResult.h; path = iOS/MTAiInterface.framework/Headers/MTInstanceSegmentModule/MTAiInstanceSegmentResult.h; sourceTree = "<group>"; };
		6B89E62F225AC8F94E99B74CF7C972F2 /* mtstitch_del_repeat.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mtstitch_del_repeat.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtstitch/mtstitch_del_repeat.h; sourceTree = "<group>"; };
		6BE52F8495DE734CD54C351D59560EBD /* MTSmileModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSmileModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSmileModule/MTSmileModuleOption.h; sourceTree = "<group>"; };
		6C188C5BBAE3DE42E1D720EA05CCC0F6 /* MTAiSkinAROption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinAROption.h; path = iOS/MTAiInterface.framework/Headers/MTSkinARModule/MTAiSkinAROption.h; sourceTree = "<group>"; };
		6CF479F75CF7260AB73603D871C3A402 /* MTAiHairGrouth.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairGrouth.h; path = iOS/MTAiInterface.framework/Headers/MTHairGrouthModule/MTAiHairGrouth.h; sourceTree = "<group>"; };
		6E02B401B75BA86BC492B0352A3CD77F /* MTFoodResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFoodResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFoodModule/MTFoodResult.h; sourceTree = "<group>"; };
		6EAB9B6E2500B52CE7835F39E6CD8EF8 /* MTAiFaceHDFeature.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceHDFeature.h; path = iOS/MTAiInterface.framework/Headers/MTFaceHDModule/MTAiFaceHDFeature.h; sourceTree = "<group>"; };
		6F1571924C2351B540D78977C0453FE1 /* MTAIKitModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAIKitModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTAIKitModule/MTAIKitModuleOption.h; sourceTree = "<group>"; };
		70D520FB42BE8EB0F1229FDB77130055 /* MTAiHairDye.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairDye.h; path = iOS/MTAiInterface.framework/Headers/MTHairDyeModule/MTAiHairDye.h; sourceTree = "<group>"; };
		71014FBE0B12AD850EB3AAFAB95B4C9E /* libmanis.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = libmanis.debug.xcconfig; sourceTree = "<group>"; };
		71493672BCFB04F4829B7E92ED8DF4F1 /* MTAiEngineTexture.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineTexture.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineTexture.h; sourceTree = "<group>"; };
		716EAC225749BD3D6ACF25232D38115D /* MTAiAnimalResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAnimalResult.h; path = iOS/MTAiInterface.framework/Headers/MTAnimalModule/MTAiAnimalResult.h; sourceTree = "<group>"; };
		7219AAE08973446ED9CD6C6344D79223 /* MTSkinV1238.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinV1238.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinModule/MTSkinV1238/MTSkinV1238.h; sourceTree = "<group>"; };
		724470B10A446E2C232754B52C1057EE /* MTEveSkinResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEveSkinResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEveSkinModule/MTEveSkinResult.h; sourceTree = "<group>"; };
		727C8E31A68BCD21D0D26916258EF75E /* MTHuman3dResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHuman3dResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHuman3dModule/MTHuman3dResult.h; sourceTree = "<group>"; };
		72EF7A3639A2A7CC792BAD260F15D20F /* MTSubColorChecker.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorChecker.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubColorChecker/MTSubColorChecker.h; sourceTree = "<group>"; };
		73B199322A707F7B76D4421FF35B5885 /* MTAiToKidFeature.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiToKidFeature.h; path = iOS/MTAiInterface.framework/Headers/MTToKidModule/MTAiToKidFeature.h; sourceTree = "<group>"; };
		73CC16074AFF78B3D3644D7AF8089E19 /* MTAnchorGenerationResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAnchorGenerationResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTAnchorGenerationModule/MTAnchorGenerationResult.h; sourceTree = "<group>"; };
		754A023B633FA7DA59AC3279CE9209FA /* MTFaceAnalysisXModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceAnalysisXModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceAnalysisXModule/MTFaceAnalysisXModuleOption.h; sourceTree = "<group>"; };
		759B37D4FD1442C9C6DE988539707C38 /* MTAiVideoStabilizationOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoStabilizationOption.h; path = iOS/MTAiInterface.framework/Headers/MTVideoStabilizationModule/MTAiVideoStabilizationOption.h; sourceTree = "<group>"; };
		75B561B2E1BD709FDA0BBF1FDAF4019E /* MTSubFrameSelect.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubFrameSelect.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubFrameSelect/MTSubFrameSelect.h; sourceTree = "<group>"; };
		75BEA0D0B777D31C938A59DCB8B366D5 /* MTDoubleChinFixModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTDoubleChinFixModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTDoubleChinFixModule/MTDoubleChinFixModuleOption.h; sourceTree = "<group>"; };
		75ECD57CD5E7DF1BD77EA715AF101617 /* MTAiEveQuality.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEveQuality.h; path = iOS/MTAiInterface.framework/Headers/MTEveQualityModule/MTAiEveQuality.h; sourceTree = "<group>"; };
		76EE384BFEB9D955C6865D246F85EEE2 /* MTAiInterface.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MTAiInterface.framework; path = iOS/MTAiInterface.framework; sourceTree = "<group>"; };
		770F9F926AB39A1AB73D6B03F17088C4 /* MTAiEvePreDetectResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEvePreDetectResult.h; path = iOS/MTAiInterface.framework/Headers/MTEvePreDetectModule/MTAiEvePreDetectResult.h; sourceTree = "<group>"; };
		771C4041DD2FFB45F7F472FA37F8AD5F /* MTAiLicense.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiLicense.h; path = iOS/MTAiInterface.framework/Headers/common/MTAiLicense.h; sourceTree = "<group>"; };
		772E2F0EDF3313FE5ECE337C2B3A2333 /* Pods-MTAISDK-DemoTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-MTAISDK-DemoTests-acknowledgements.plist"; sourceTree = "<group>"; };
		7764259E9AE834FEF68FA316CA52F00B /* MeituAiEngine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MeituAiEngine.h; path = iOS/MTAiInterface.framework/Headers/MeituAiEngine.h; sourceTree = "<group>"; };
		7802B05B071AC13CDB30837DA9EA119A /* MTAiCsketchOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiCsketchOption.h; path = iOS/MTAiInterface.framework/Headers/MTCsketchModule/MTAiCsketchOption.h; sourceTree = "<group>"; };
		789AF9A9304B086097876D211D53341F /* MTAiEngineImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineImage.h; path = iOS/MTAiInterface.framework/Headers/common/MTAiEngineImage.h; sourceTree = "<group>"; };
		78B8545E055C947361E10447AF4A1A14 /* MTAiFaceAnalysisXResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceAnalysisXResult.h; path = iOS/MTAiInterface.framework/Headers/MTFaceAnalysisXModule/MTAiFaceAnalysisXResult.h; sourceTree = "<group>"; };
		78C75B1847B7793F457F7C95DD9CAD1F /* ConstraintView+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintView+Extensions.swift"; path = "Sources/ConstraintView+Extensions.swift"; sourceTree = "<group>"; };
		796B9B7B4FEF3ECEB5AEB1E613F412F8 /* MTAiEveQualityResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEveQualityResult.h; path = iOS/MTAiInterface.framework/Headers/MTEveQualityModule/MTAiEveQualityResult.h; sourceTree = "<group>"; };
		79EFA025797728B000F104A57FC16A1D /* MTAiSegmentResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSegmentResult.h; path = iOS/MTAiInterface.framework/Headers/MTSegmentModule/MTAiSegmentResult.h; sourceTree = "<group>"; };
		7BF618B62F9EAF4B11E446B6E5D50C1E /* MTAi3DFaceAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAi3DFaceAttribute.h; path = iOS/MTAiInterface.framework/Headers/MT3DFaceModule/Attribute/MTAi3DFaceAttribute.h; sourceTree = "<group>"; };
		7C4C30175C370B9FED0F060F1EA47E18 /* MTAiEveAutoSkinColor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEveAutoSkinColor.h; path = iOS/MTAiInterface.framework/Headers/MTEveAutoSkinColorModule/MTAiEveAutoSkinColor.h; sourceTree = "<group>"; };
		7E12191FFF32C8211C0ABBADFC92FF71 /* ConstraintPriorityTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintPriorityTarget.swift; path = Sources/ConstraintPriorityTarget.swift; sourceTree = "<group>"; };
		7E4FF76C4C4C837F3231717070F6214D /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig"; sourceTree = "<group>"; };
		7EC4ED89CB3D20EB1182FCF58F47988F /* MTEveQualityResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEveQualityResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEveQualityModule/MTEveQualityResult.h; sourceTree = "<group>"; };
		7EF5CD654DD588916C8BE48B38388414 /* InceptionBeautyUtilCoreML.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = InceptionBeautyUtilCoreML.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtinceptionbeauty/InceptionBeautyUtilCoreML.h; sourceTree = "<group>"; };
		7F5620037A88373341C572871C5FFB62 /* ConstraintMakerExtendable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerExtendable.swift; path = Sources/ConstraintMakerExtendable.swift; sourceTree = "<group>"; };
		802BED11ED27B7D219393B0D4F8313A8 /* MTHairGrouthModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairGrouthModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairGrouthModule/MTHairGrouthModuleOption.h; sourceTree = "<group>"; };
		80F77E02068C26DE80A601702EBD09B6 /* MTSkinToneMappingResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinToneMappingResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinToneMappingModule/MTSkinToneMappingResult.h; sourceTree = "<group>"; };
		81CFAD1CB96C07B59C5BB818457EA337 /* MTBodyInOneModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTBodyInOneModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTBodyInOneModule/MTBodyInOneModuleOption.h; sourceTree = "<group>"; };
		81FCD542D9366D569E08FF6ED875F7D0 /* MTAiDL3DResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiDL3DResult.h; path = iOS/MTAiInterface.framework/Headers/MTDL3DModule/MTAiDL3DResult.h; sourceTree = "<group>"; };
		823BA689FCEB87FFD974402E733A417E /* InceptionBeautyRejuvenationUtilGL.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = InceptionBeautyRejuvenationUtilGL.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtinceptionbeauty/InceptionBeautyRejuvenationUtilGL.h; sourceTree = "<group>"; };
		834EA6EC9E6ECB46B143ADB9A9EADE77 /* MTCsketchResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTCsketchResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTCsketchModule/MTCsketchResult.h; sourceTree = "<group>"; };
		83790908BE03557F48848EACF0099467 /* MTSegmentResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSegmentResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSegmentModule/MTSegmentResult.h; sourceTree = "<group>"; };
		8386B1D734AC8907DE7478743E63E290 /* SnapKit.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SnapKit.debug.xcconfig; sourceTree = "<group>"; };
		83A4B6EC4777A2AF55BF45EBEB3167EB /* MTAiBodyInOneResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiBodyInOneResult.h; path = iOS/MTAiInterface.framework/Headers/MTBodyInOneModule/MTAiBodyInOneResult.h; sourceTree = "<group>"; };
		83CF260696E7BA3E111236298FEDAA5A /* MTAiEngineTexture.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineTexture.h; path = iOS/MTAiInterface.framework/Headers/common/MTAiEngineTexture.h; sourceTree = "<group>"; };
		84096218E72A2C041C7C09935CF99AEC /* MTHairStraightModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairStraightModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairStraightModule/MTHairStraightModuleOption.h; sourceTree = "<group>"; };
		84570D8CCE0E69A15126B9E6D6BF9022 /* MTSubEyeLifting.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubEyeLifting.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubEyeLifting/MTSubEyeLifting.h; sourceTree = "<group>"; };
		84C0D1B38C05A2EAB7F1AD72F25FFFAA /* MTDL3DResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTDL3DResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTDL3DModule/MTDL3DResult.h; sourceTree = "<group>"; };
		84E0D547B19287C9895194FEF7FB703D /* MTTeethModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTTeethModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTTeethModule/MTTeethModuleOption.h; sourceTree = "<group>"; };
		86447333FA37BF01E8D352422810A1C4 /* MTSubInceptionBeautyUtilGL.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubInceptionBeautyUtilGL.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/InceptionBeauty/MTSubInceptionBeautyUtilGL.h; sourceTree = "<group>"; };
		86AEAEE27051B479A9E473AA54DEFDB9 /* MTAiHairStraightResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairStraightResult.h; path = iOS/MTAiInterface.framework/Headers/MTHairStraightModule/MTAiHairStraightResult.h; sourceTree = "<group>"; };
		871435DDB09220290E8DD0BB8341931C /* MTAiVideoRecognitionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoRecognitionResult.h; path = iOS/MTAiInterface.framework/Headers/MTVideoRecognitionModule/MTAiVideoRecognitionResult.h; sourceTree = "<group>"; };
		8737203005DC45C5936EF06EFB5C9196 /* MTSubDLAlgorithmUtilGL.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubDLAlgorithmUtilGL.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/libDLBeautyBase/MTSubDLAlgorithmUtilGL.h; sourceTree = "<group>"; };
		876FA3F8ECFE99BFEFAE1BD1FD3159D3 /* MTAiAnchorGenerationOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAnchorGenerationOption.h; path = iOS/MTAiInterface.framework/Headers/MTAnchorGenerationModule/MTAiAnchorGenerationOption.h; sourceTree = "<group>"; };
		87E6171F30F60C9B03D0636A36A0A5C2 /* MTSubVideoOptimize.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubVideoOptimize.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubVideoOptimize/MTSubVideoOptimize.h; sourceTree = "<group>"; };
		894D941CB569EF6C59BCADC50D34EE2A /* Pods-MTAISDK-Demo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-MTAISDK-Demo.debug.xcconfig"; sourceTree = "<group>"; };
		89FD8022B5B81828CD1402886538E888 /* MTAiMaterialResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiMaterialResult.h; path = iOS/MTAiInterface.framework/Headers/MTMaterialTrackingModule/MTAiMaterialResult.h; sourceTree = "<group>"; };
		8A4F215DE83866C05A87DFBF513644CD /* MTAiHairResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairResult.h; path = iOS/MTAiInterface.framework/Headers/MTHairModule/MTAiHairResult.h; sourceTree = "<group>"; };
		8C581F65BA98F281CD273DD0BB1FF353 /* MTAiHand.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHand.h; path = iOS/MTAiInterface.framework/Headers/MTHandModule/MTAiHand.h; sourceTree = "<group>"; };
		8C80EFFE9240C985B87BD56EAFB28C6B /* MTAiSceneryBoundaryLineOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSceneryBoundaryLineOption.h; path = iOS/MTAiInterface.framework/Headers/MTSceneryBoundaryLineModule/MTAiSceneryBoundaryLineOption.h; sourceTree = "<group>"; };
		8C9534A25639AB68B09059BB201586B9 /* Pods-MTAISDK-DemoTests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-MTAISDK-DemoTests-Info.plist"; sourceTree = "<group>"; };
		8CD17CB68A83EEEB4D7DB54015FAFCC5 /* MTAiFaceAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTFaceModule/Attribute/MTAiFaceAttribute.h; sourceTree = "<group>"; };
		8D575EBECC197C2906480C767B9361B1 /* MTKiev3DMakeModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTKiev3DMakeModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTKiev3DMakeModule/MTKiev3DMakeModuleOption.h; sourceTree = "<group>"; };
		8DD19394C90076DA2D462DA402D67A4F /* ConstraintConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintConfig.swift; path = Sources/ConstraintConfig.swift; sourceTree = "<group>"; };
		8E28E3E3A758DBD27C00C567C946D808 /* MTVideoOptimizerModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTVideoOptimizerModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTVideoOptimizerModule/MTVideoOptimizerModuleOption.h; sourceTree = "<group>"; };
		8E2D894B028512C0F75CF321F536DEF1 /* MTSubAiModelKit.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubAiModelKit.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/AiModelKit/MTSubAiModelKit.h; sourceTree = "<group>"; };
		8EAAA09C6573C666B532F4F76A007A17 /* MTAiNevusDetection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiNevusDetection.h; path = iOS/MTAiInterface.framework/Headers/MTNevusDetectionModule/MTAiNevusDetection.h; sourceTree = "<group>"; };
		9009942F3230913D8DA4E0DBE36E396A /* MTCsketchModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTCsketchModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTCsketchModule/MTCsketchModuleOption.h; sourceTree = "<group>"; };
		90B92BF7928A7602FC7A82CC56CECF02 /* MTValueMap.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTValueMap.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTValueMap.h; sourceTree = "<group>"; };
		90BE91979A7730FDF12F69B20FC96831 /* MTInstanceSegmentModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTInstanceSegmentModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTInstanceSegmentModule/MTInstanceSegmentModuleOption.h; sourceTree = "<group>"; };
		90D141B8F92646B088015629442E5A0C /* ConstraintRelation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintRelation.swift; path = Sources/ConstraintRelation.swift; sourceTree = "<group>"; };
		920F90E42F77C978F939E3F747478F00 /* MTAiRemoveWatermarkOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiRemoveWatermarkOption.h; path = iOS/MTAiInterface.framework/Headers/MTRemoveWatermarkModule/MTAiRemoveWatermarkOption.h; sourceTree = "<group>"; };
		9215EFEEC9AEC43F17D99122CBD03F5D /* MTAiCgStyleResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiCgStyleResult.h; path = iOS/MTAiInterface.framework/Headers/MTCgStyleModule/MTAiCgStyleResult.h; sourceTree = "<group>"; };
		92D7EF0D77E417E28193769B9BFA302F /* MTFaceUtility.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceUtility.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceModule/MTFaceUtility.h; sourceTree = "<group>"; };
		92E4F327419F771CFA5ECAAA09B3D87D /* MTFaceBlitResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceBlitResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceBlitModule/MTFaceBlitResult.h; sourceTree = "<group>"; };
		9522031A21249BA4A0EE770C6B6ED571 /* ConstraintDirectionalInsets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDirectionalInsets.swift; path = Sources/ConstraintDirectionalInsets.swift; sourceTree = "<group>"; };
		956237C360444609A65CCBACEECAD876 /* MT3DFaceResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MT3DFaceResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3DFaceModule/MT3DFaceResult.h; sourceTree = "<group>"; };
		95A8FAA1852010F25B6292FBF0D3A229 /* MTHairCurlyModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairCurlyModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairCurlyModule/MTHairCurlyModuleOption.h; sourceTree = "<group>"; };
		9633B78B6DC1BD044B7BC8BBC582720A /* Pods-MTAISDK-Demo.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-MTAISDK-Demo.modulemap"; sourceTree = "<group>"; };
		9650C5F3398FEB6332833894733F179E /* MTEveAutoSkinColorResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEveAutoSkinColorResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEveAutoSkinColorModule/MTEveAutoSkinColorResult.h; sourceTree = "<group>"; };
		96A1E0DAEE84D21A4AF41FB967F7D1DB /* MTAiEngineConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineConfig.h; path = iOS/MTAiInterface.framework/Headers/common/MTAiEngineConfig.h; sourceTree = "<group>"; };
		97678617EB9DEB261179BC8BE14D54C0 /* MTSubLowPoly.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubLowPoly.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/IntelligentFusion/MTSubLowPoly.h; sourceTree = "<group>"; };
		979486118B3E90C08386079D57962701 /* SnapKit */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = SnapKit; path = SnapKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		97E6DFCFBA11A776D444F15BC73C557B /* MTImageRecognitionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTImageRecognitionResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTImageRecognitionModule/MTImageRecognitionResult.h; sourceTree = "<group>"; };
		986DC64F8BEDD6D28DB25236BB9BE0C0 /* MTAiDoubleChinFixResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiDoubleChinFixResult.h; path = iOS/MTAiInterface.framework/Headers/MTDoubleChinFixModule/MTAiDoubleChinFixResult.h; sourceTree = "<group>"; };
		98B9838C4C93D87E4C7EDF5899FE8136 /* MTAiHuman3dBody.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHuman3dBody.h; path = iOS/MTAiInterface.framework/Headers/MTHuman3dModule/MTAiHuman3dBody.h; sourceTree = "<group>"; };
		98C17B3F0975AEB8522068BB1671D0B0 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests-dummy.m"; sourceTree = "<group>"; };
		98E40FDBCF6F4761B703F642C0BE0516 /* Pods-MTAISDK-DemoTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-MTAISDK-DemoTests.debug.xcconfig"; sourceTree = "<group>"; };
		98E600BC095C2691ED65F07200E66887 /* MTSubDLAlgorithmUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubDLAlgorithmUtil.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/libDLBeautyBase/MTSubDLAlgorithmUtil.h; sourceTree = "<group>"; };
		9912FC610C976E5461B69CBDE9066F91 /* MTBodyModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTBodyModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTBodyModule/MTBodyModuleOption.h; sourceTree = "<group>"; };
		99422FAA4205FD46E6BBC569B936BF47 /* InceptionBeautyDoubleChinFixUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = InceptionBeautyDoubleChinFixUtil.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtinceptionbeauty/InceptionBeautyDoubleChinFixUtil.h; sourceTree = "<group>"; };
		99DEC70867D29B4267582C4AED6905C2 /* MTBodyInOneResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTBodyInOneResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTBodyInOneModule/MTBodyInOneResult.h; sourceTree = "<group>"; };
		9A4ACC99E1F380BB4D880CE3376629CD /* MTSubAIBeauty.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubAIBeauty.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubAIBeauty/MTSubAIBeauty.h; sourceTree = "<group>"; };
		9C342374D82F33AEADCE345C8CAACB6A /* MTAiBodyResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiBodyResult.h; path = iOS/MTAiInterface.framework/Headers/MTBodyModule/MTAiBodyResult.h; sourceTree = "<group>"; };
		9C66499FF0604DC461F205CE72E4FBD5 /* MTAiEveAutoSkinColorOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEveAutoSkinColorOption.h; path = iOS/MTAiInterface.framework/Headers/MTEveAutoSkinColorModule/MTAiEveAutoSkinColorOption.h; sourceTree = "<group>"; };
		9D166578015013BF1069BFF4CEDEAC01 /* Manis.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Manis.framework; path = iOS/universal/Manis.framework; sourceTree = "<group>"; };
		9D6D0926CFB76C4FC3B3DE9F1785C77B /* MTHairResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairModule/MTHairResult.h; sourceTree = "<group>"; };
		9D6DBD5057495139CB3707D8DCDDF055 /* MTSkinBCCResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinBCCResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinBCCModule/MTSkinBCCResult.h; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9D9D310D9B76C88B884C1780548D1CDC /* MTSubTempIntelligentFusion.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubTempIntelligentFusion.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/IntelligentFusion/MTSubTempIntelligentFusion.h; sourceTree = "<group>"; };
		9E1549033AC14F1CEEA28AA00D325DBD /* MTSubDLTextureInpainting.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubDLTextureInpainting.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubDLTextureInpainting/MTSubDLTextureInpainting.h; sourceTree = "<group>"; };
		9F1B2DEFDBEDB361FF31ABC8DE2FF038 /* mvgif.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = mvgif.debug.xcconfig; sourceTree = "<group>"; };
		9F8B597CD8F47B9692D372B77B99F978 /* MTAiInstanceSegmentOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiInstanceSegmentOption.h; path = iOS/MTAiInterface.framework/Headers/MTInstanceSegmentModule/MTAiInstanceSegmentOption.h; sourceTree = "<group>"; };
		A0016DD4CFF17C01B62811AB6FCAC2BA /* MTAiFaceUtility.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceUtility.h; path = iOS/MTAiInterface.framework/Headers/MTFaceModule/MTAiFaceUtility.h; sourceTree = "<group>"; };
		A07B6C17DD730ADC236315AF7A127516 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests"; path = Pods_MTAISDK_Demo_MTAISDK_DemoUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A0D56C81BA6DC15D9B8CFDFFDD337EE4 /* MTAiEngineFrame.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineFrame.h; path = iOS/MTAiInterface.framework/Headers/MTAiEngineFrame.h; sourceTree = "<group>"; };
		A227DD25C672540CBB969A809D3C08D6 /* MTAiEngineUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineUtils.h; path = iOS/MTAiInterface.framework/Headers/common/MTAiEngineUtils.h; sourceTree = "<group>"; };
		A24D941BE68C8939EC817693A1F260C6 /* MTAiVideoRecognition.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoRecognition.h; path = iOS/MTAiInterface.framework/Headers/MTVideoRecognitionModule/MTAiVideoRecognition.h; sourceTree = "<group>"; };
		A29A0D416F45998B3E35BE682773F9D0 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests-acknowledgements.plist"; sourceTree = "<group>"; };
		A36D568F10ECE7750D89E1F131027C5C /* MTAiFaceOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceOption.h; path = iOS/MTAiInterface.framework/Headers/MTFaceModule/MTAiFaceOption.h; sourceTree = "<group>"; };
		A387F144056BD1593B18022A938F6093 /* ConstraintMakerRelatable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerRelatable.swift; path = Sources/ConstraintMakerRelatable.swift; sourceTree = "<group>"; };
		A3C4BE83192B1881F2D444215B22458C /* ConstraintViewDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintViewDSL.swift; path = Sources/ConstraintViewDSL.swift; sourceTree = "<group>"; };
		A4957FB26719B2D9D9D2F9A8A27CA3BC /* MTLandmarkModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTLandmarkModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTLandmarkModule/MTLandmarkModuleOption.h; sourceTree = "<group>"; };
		A4BE01112630DB4AE08001E2719C3C5F /* MTSubAIBeauty.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubAIBeauty.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubAIBeauty/MTSubAIBeauty.h; sourceTree = "<group>"; };
		A4CE480598B4BE551460793C30463C50 /* MTSubInceptionBeautyUtilCoreML.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubInceptionBeautyUtilCoreML.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/InceptionBeauty/MTSubInceptionBeautyUtilCoreML.h; sourceTree = "<group>"; };
		A4F381AC482DA2B5EB2D1B42FB29C29E /* MTAiModelKitReflection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiModelKitReflection.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiModelKitReflection.h; sourceTree = "<group>"; };
		A62C0C6993C00974ADCEA65220DC1CCD /* MTSkinMicroModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinMicroModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinMicroModule/MTSkinMicroModuleOption.h; sourceTree = "<group>"; };
		A7EDDD96154AF118A19C9F0593C3AAA0 /* MTAiFaceAnalysisXOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceAnalysisXOption.h; path = iOS/MTAiInterface.framework/Headers/MTFaceAnalysisXModule/MTAiFaceAnalysisXOption.h; sourceTree = "<group>"; };
		A81675293C6F08A973E63799D5699DBA /* Pods-MTAISDK-DemoTests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-MTAISDK-DemoTests.modulemap"; sourceTree = "<group>"; };
		A8434FFF0A9425FA9F47713DD508405D /* MTAiHighDofEyelidResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHighDofEyelidResult.h; path = iOS/MTAiInterface.framework/Headers/MTHighDofEyelidModule/MTAiHighDofEyelidResult.h; sourceTree = "<group>"; };
		A843C54D2CD3C6332A2FD8B675EDBDC9 /* ConstraintAttributes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintAttributes.swift; path = Sources/ConstraintAttributes.swift; sourceTree = "<group>"; };
		A87C3ED296E5A27B432395F784C365D4 /* MTAnimalModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAnimalModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTAnimalModule/MTAnimalModuleOption.h; sourceTree = "<group>"; };
		A90ADDBAEDB36A3FBCBA2F4F2973DF26 /* MTAiPortraitInpainting.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiPortraitInpainting.h; path = iOS/MTAiInterface.framework/Headers/MTPortraitInpaintingModule/MTAiPortraitInpainting.h; sourceTree = "<group>"; };
		A9176B2B961BCD888C9365606A781C0D /* MTSubHairTransfer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubHairTransfer.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubHairTransfer/MTSubHairTransfer.h; sourceTree = "<group>"; };
		A968DC02EB2A53A4314F9EBD63F45D75 /* MTAiImageDetectionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiImageDetectionResult.h; path = iOS/MTAiInterface.framework/Headers/MTImageDetectionModule/MTAiImageDetectionResult.h; sourceTree = "<group>"; };
		A9F64031C165E652ED4AFCBF751809E2 /* libmanis.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = libmanis.release.xcconfig; sourceTree = "<group>"; };
		AAF624EDB6705B789FAABF369D4ECD17 /* Pods-MTAISDK-Demo-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-MTAISDK-Demo-frameworks.sh"; sourceTree = "<group>"; };
		AB20F72C2BF45BCC81B921276C1704A8 /* MTAiEngineSupport.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineSupport.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineSupport.h; sourceTree = "<group>"; };
		ABA648F20A2405F35D98AFCCCEBFBD54 /* MTAiNail.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiNail.h; path = iOS/MTAiInterface.framework/Headers/MTHandModule/MTAiNail.h; sourceTree = "<group>"; };
		ABF11022B8FD76975BABA83002917AB0 /* MTAiFoodAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFoodAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTFoodModule/Attribute/MTAiFoodAttribute.h; sourceTree = "<group>"; };
		AC0D277A95D1EF7E0A50DA8A542394E6 /* MTAiEyeSegmentResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEyeSegmentResult.h; path = iOS/MTAiInterface.framework/Headers/MTEyeSegmentModule/MTAiEyeSegmentResult.h; sourceTree = "<group>"; };
		ACB584D159DD5DB73BDFB1EF372E228E /* MTAiBodyInOneOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiBodyInOneOption.h; path = iOS/MTAiInterface.framework/Headers/MTBodyInOneModule/MTAiBodyInOneOption.h; sourceTree = "<group>"; };
		AD334EE6FDD7E9B7780469AB979AD06B /* MTAiFace.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFace.h; path = iOS/MTAiInterface.framework/Headers/MTFaceModule/MTAiFace.h; sourceTree = "<group>"; };
		AE0E9EA950D7430E90C174FB13F793A3 /* libmtaiinterface.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = libmtaiinterface.debug.xcconfig; sourceTree = "<group>"; };
		AEA193BDDFF2A27225363685752504F8 /* MTAiErrorCallbackDetegate.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiErrorCallbackDetegate.h; path = iOS/MTAiInterface.framework/Headers/common/MTAiErrorCallbackDetegate.h; sourceTree = "<group>"; };
		B01F9C04BEC5BBD3CB8FC894D5B88F74 /* MTAiRTTeethRetouchOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiRTTeethRetouchOption.h; path = iOS/MTAiInterface.framework/Headers/MTRTTeethRetouchModule/MTAiRTTeethRetouchOption.h; sourceTree = "<group>"; };
		B02A7E4CEDFA89130959B261EADA40A2 /* MTAi3DFaceResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAi3DFaceResult.h; path = iOS/MTAiInterface.framework/Headers/MT3DFaceModule/MTAi3DFaceResult.h; sourceTree = "<group>"; };
		B07AC8A84E11D123B63870E139CEAF39 /* MTAiHairFluffyResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairFluffyResult.h; path = iOS/MTAiInterface.framework/Headers/MTHairFluffyModule/MTAiHairFluffyResult.h; sourceTree = "<group>"; };
		B123561F9246609FA80D0F071B6D6F87 /* MTAiHuman3dHand.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHuman3dHand.h; path = iOS/MTAiInterface.framework/Headers/MTHuman3dModule/MTAiHuman3dHand.h; sourceTree = "<group>"; };
		B1FD03EB51DE25FD35D1EAD5BCCA1441 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = Sources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		B2D14C23BD95FF009D1695EE1BA46F7C /* MTAiFaceHDOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceHDOption.h; path = iOS/MTAiInterface.framework/Headers/MTFaceHDModule/MTAiFaceHDOption.h; sourceTree = "<group>"; };
		B393D052A1E7E9C9560C94ED07FA7C66 /* SnapKit-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SnapKit-umbrella.h"; sourceTree = "<group>"; };
		B3BDF355F1E15B2E375F20E3C1645CEA /* MTAiHairFluffy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairFluffy.h; path = iOS/MTAiInterface.framework/Headers/MTHairFluffyModule/MTAiHairFluffy.h; sourceTree = "<group>"; };
		B3CF28136520A7D08BE3F587C9CE96AC /* ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist"; sourceTree = "<group>"; };
		B49F0A82229F92F737BA0252C40E812F /* MTMakeupResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTMakeupResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTMakeupModule/MTMakeupResult.h; sourceTree = "<group>"; };
		B4BFE4A78B63ACC43EE8A41E3E04CDB3 /* MTAiEngineType.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineType.h; path = iOS/MTAiInterface.framework/Headers/common/MTAiEngineType.h; sourceTree = "<group>"; };
		B581CCC4077CC21651940C57AE7097C7 /* MTAiCgStyleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiCgStyleOption.h; path = iOS/MTAiInterface.framework/Headers/MTCgStyleModule/MTAiCgStyleOption.h; sourceTree = "<group>"; };
		B5D7C5E325A8A13B80F29F11EF06C920 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests.modulemap"; sourceTree = "<group>"; };
		B5D921E97027FB45BDA2500CC1BB620E /* MTImageRecognitionModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTImageRecognitionModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTImageRecognitionModule/MTImageRecognitionModuleOption.h; sourceTree = "<group>"; };
		B617EF0D906B1F459E4B91D6D84A6B4B /* MTAiMaterial.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiMaterial.h; path = iOS/MTAiInterface.framework/Headers/MTMaterialTrackingModule/MTAiMaterial.h; sourceTree = "<group>"; };
		B827F7A1B6742829B34488A2CFE3E202 /* MTAiPortraitInpaintingOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiPortraitInpaintingOption.h; path = iOS/MTAiInterface.framework/Headers/MTPortraitInpaintingModule/MTAiPortraitInpaintingOption.h; sourceTree = "<group>"; };
		B962BE394E0B7880D1A82F306139A52A /* MTAiVideoOptimizerResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoOptimizerResult.h; path = iOS/MTAiInterface.framework/Headers/MTVideoOptimizerModule/MTAiVideoOptimizerResult.h; sourceTree = "<group>"; };
		B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SnapKit-SnapKit_Privacy"; path = SnapKit_Privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		BB1019D96BD52835E7B6D1DE2809110E /* ConstraintInsets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintInsets.swift; path = Sources/ConstraintInsets.swift; sourceTree = "<group>"; };
		BB5F00D044256126F8689290113E6BC9 /* MTHandResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHandResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHandModule/MTHandResult.h; sourceTree = "<group>"; };
		BB81036B19FCE9CDDBF4168AC41C3B0D /* InceptionBeautyUtil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = InceptionBeautyUtil.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtinceptionbeauty/InceptionBeautyUtil.h; sourceTree = "<group>"; };
		BB8192C8345812F082D75D185652DEE6 /* MTAiVideoStabilizationResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoStabilizationResult.h; path = iOS/MTAiInterface.framework/Headers/MTVideoStabilizationModule/MTAiVideoStabilizationResult.h; sourceTree = "<group>"; };
		BBABBD09629F464E32673355341B0221 /* MTAiToKidFeatureResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiToKidFeatureResult.h; path = iOS/MTAiInterface.framework/Headers/MTToKidModule/MTAiToKidFeatureResult.h; sourceTree = "<group>"; };
		BC174B964F2DFE4A88C359637A67C338 /* MTSubAIEngineAnalysis.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubAIEngineAnalysis.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubAIEngineAnalysis/MTSubAIEngineAnalysis.h; sourceTree = "<group>"; };
		BDCD23F8B684EAB18F645009B911A36C /* MTAiEngineTimer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineTimer.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineTimer.h; sourceTree = "<group>"; };
		BF2F29C87491FBD567933D33E4C41FCD /* MTAnchorGenerationModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAnchorGenerationModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTAnchorGenerationModule/MTAnchorGenerationModuleOption.h; sourceTree = "<group>"; };
		C0E8BA95D8CFC9F0B9B7D6C927F1D07E /* MTOrnamentResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTOrnamentResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTOrnamentModule/MTOrnamentResult.h; sourceTree = "<group>"; };
		C0EF44FE64FCA4ACA4E3F37488DA9BEE /* LayoutConstraintItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutConstraintItem.swift; path = Sources/LayoutConstraintItem.swift; sourceTree = "<group>"; };
		C122954309D5CB8CFCCB6A48B40AA7DB /* MTAiKiev3DMakeOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiKiev3DMakeOption.h; path = iOS/MTAiInterface.framework/Headers/MTKiev3DMakeModule/MTAiKiev3DMakeOption.h; sourceTree = "<group>"; };
		C1B202FE029049A92EF54E512581EAD0 /* MTAiNevusDetectionOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiNevusDetectionOption.h; path = iOS/MTAiInterface.framework/Headers/MTNevusDetectionModule/MTAiNevusDetectionOption.h; sourceTree = "<group>"; };
		C24AA8787423E815C56BE4DFC6DA09BD /* MTSubRTDenseHair.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubRTDenseHair.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubRTDenseHair/MTSubRTDenseHair.h; sourceTree = "<group>"; };
		C29CAEAFC79460E9E98523F07A60CF92 /* MTAiHandAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHandAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTHandModule/Attribute/MTAiHandAttribute.h; sourceTree = "<group>"; };
		C2CF0166B362036EBAF529DAE60D3772 /* Pods-MTAISDK-DemoTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-MTAISDK-DemoTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		C40A4E215087F265BEDBFF89979A8EE5 /* MTAiRemoveWatermark.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiRemoveWatermark.h; path = iOS/MTAiInterface.framework/Headers/MTRemoveWatermarkModule/MTAiRemoveWatermark.h; sourceTree = "<group>"; };
		C4177B87EB636B476C4689164FBCB9A8 /* MTSkinToneMappingModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinToneMappingModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinToneMappingModule/MTSkinToneMappingModuleOption.h; sourceTree = "<group>"; };
		C44867D548296DBF5E685D2F04849646 /* MTAiCsketch.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiCsketch.h; path = iOS/MTAiInterface.framework/Headers/MTCsketchModule/MTAiCsketch.h; sourceTree = "<group>"; };
		C488BDE7347CADE214418A7E2C2C3A93 /* MTAiTeethOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiTeethOption.h; path = iOS/MTAiInterface.framework/Headers/MTTeethModule/MTAiTeethOption.h; sourceTree = "<group>"; };
		C4DFA91F19EF65FC74ED00D3AA75AE02 /* Pods-MTAISDK-Demo-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-MTAISDK-Demo-resources.sh"; sourceTree = "<group>"; };
		C4FA718EC852843967B34054752134AA /* MTSubVideoOptimize.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubVideoOptimize.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubVideoOptimize/MTSubVideoOptimize.h; sourceTree = "<group>"; };
		C4FB1D2BCC86CE7185F15C03FB73834E /* MTSubDLAlgorithmDefined.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubDLAlgorithmDefined.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/libDLBeautyBase/MTSubDLAlgorithmDefined.h; sourceTree = "<group>"; };
		C55CB4B638EDD3F6C4389576E8CEC51E /* MTInstanceSegmentResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTInstanceSegmentResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTInstanceSegmentModule/MTInstanceSegmentResult.h; sourceTree = "<group>"; };
		C5BA8BF25783DA8601A27FF64F6DB374 /* MTSubHairTransferWrapper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubHairTransferWrapper.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubHairTransferWrapper/MTSubHairTransferWrapper.h; sourceTree = "<group>"; };
		C72EF82F2ACC07CB1CB62CEAFA20411F /* MTHairStraightResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairStraightResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairStraightModule/MTHairStraightResult.h; sourceTree = "<group>"; };
		C75BEAD0AD3ECDB9ABF2D454B151A938 /* MTAiNevusDetectionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiNevusDetectionResult.h; path = iOS/MTAiInterface.framework/Headers/MTNevusDetectionModule/MTAiNevusDetectionResult.h; sourceTree = "<group>"; };
		C78043E3D6AF2046722E735E43B5ECE7 /* MTSubImageEnhancer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubImageEnhancer.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubImageEnhancer/MTSubImageEnhancer.h; sourceTree = "<group>"; };
		C80050480E09E1D6277B8046BEEB6256 /* MTAiAIKitResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAIKitResult.h; path = iOS/MTAiInterface.framework/Headers/MTAIKitModule/MTAiAIKitResult.h; sourceTree = "<group>"; };
		C80CFF56D88444F5B98BDB0D309F2E42 /* MTAiEyeSegmentOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEyeSegmentOption.h; path = iOS/MTAiInterface.framework/Headers/MTEyeSegmentModule/MTAiEyeSegmentOption.h; sourceTree = "<group>"; };
		C8F911498295C41579C0FB6EAF435819 /* Pods-MTAISDK-DemoTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-MTAISDK-DemoTests-dummy.m"; sourceTree = "<group>"; };
		C95081E67E315408DF227F63B30A02B1 /* MTAiEngineMacro.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineMacro.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineMacro.h; sourceTree = "<group>"; };
		C9819CE59454A5A3F22DE7BD8AB9CEED /* MTSkinARResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinARResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinARModule/MTSkinARResult.h; sourceTree = "<group>"; };
		C9EE985AC8056D83612F7641435D8F83 /* MTAiSkinARResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinARResult.h; path = iOS/MTAiInterface.framework/Headers/MTSkinARModule/MTAiSkinARResult.h; sourceTree = "<group>"; };
		CB0C0F2255366398F3BEF4E2DE8BD585 /* MTVideoStabilizationModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTVideoStabilizationModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTVideoStabilizationModule/MTVideoStabilizationModuleOption.h; sourceTree = "<group>"; };
		CB2053334DEFCEDDF64724C1C5070C98 /* MTAiErrorCallback.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiErrorCallback.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/callback/MTAiErrorCallback.h; sourceTree = "<group>"; };
		CC42B8C42B96BBFFBC1AE7069167A67A /* MTSegmentModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSegmentModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSegmentModule/MTSegmentModuleOption.h; sourceTree = "<group>"; };
		CC6831BC6DBBAC9EF163F29225885E13 /* MTAiSkin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkin.h; path = iOS/MTAiInterface.framework/Headers/MTSkinModule/MTAiSkin.h; sourceTree = "<group>"; };
		CD72DCE7DA31E27179F76EFBD66CB8F5 /* MTAiFoodStyleResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFoodStyleResult.h; path = iOS/MTAiInterface.framework/Headers/MTFoodStyleModule/MTAiFoodStyleResult.h; sourceTree = "<group>"; };
		CE412F2F9D858B20716983A68361322F /* MTAiOrnamentAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiOrnamentAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTOrnamentModule/Attribute/MTAiOrnamentAttribute.h; sourceTree = "<group>"; };
		CEB293796ED145864B622CDC4F9AAAC8 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests-frameworks.sh"; sourceTree = "<group>"; };
		CEE357DB4BF2CFBFFBCD453693461759 /* MTAiSceneryBoundaryLineResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSceneryBoundaryLineResult.h; path = iOS/MTAiInterface.framework/Headers/MTSceneryBoundaryLineModule/MTAiSceneryBoundaryLineResult.h; sourceTree = "<group>"; };
		CEEC0C2F4D47D0405D8669238E5765A8 /* MTVideoRecognitionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTVideoRecognitionResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTVideoRecognitionModule/MTVideoRecognitionResult.h; sourceTree = "<group>"; };
		CEEF65EDB651DFEF0BC42CFA6560CE86 /* MTAiCsketchResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiCsketchResult.h; path = iOS/MTAiInterface.framework/Headers/MTCsketchModule/MTAiCsketchResult.h; sourceTree = "<group>"; };
		CEF691D0D91CD240B94B7256C7BB3943 /* MTSubDLAlgorithmUtilCoreML.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubDLAlgorithmUtilCoreML.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/libDLBeautyBase/MTSubDLAlgorithmUtilCoreML.h; sourceTree = "<group>"; };
		CF1ECEE391486D8F1DF197A5AAFE8A53 /* MTEvePreDetectModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEvePreDetectModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEvePreDetectModule/MTEvePreDetectModuleOption.h; sourceTree = "<group>"; };
		CFE741CD336FE75813614F4E5DCFB2A9 /* MTAiEyelidRealtimeOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEyelidRealtimeOption.h; path = iOS/MTAiInterface.framework/Headers/MTEyelidRealtimeModule/MTAiEyelidRealtimeOption.h; sourceTree = "<group>"; };
		D04663977EC45CA4A73961A3A70E6029 /* MTAiAnchorGenerationResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAnchorGenerationResult.h; path = iOS/MTAiInterface.framework/Headers/MTAnchorGenerationModule/MTAiAnchorGenerationResult.h; sourceTree = "<group>"; };
		D0AB795694E0C83FE894F511050EC39F /* MTAiHairGrouthResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairGrouthResult.h; path = iOS/MTAiInterface.framework/Headers/MTHairGrouthModule/MTAiHairGrouthResult.h; sourceTree = "<group>"; };
		D1AE850AC3E258A26C183EB59163D599 /* MTAiBodyOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiBodyOption.h; path = iOS/MTAiInterface.framework/Headers/MTBodyModule/MTAiBodyOption.h; sourceTree = "<group>"; };
		D1FB8E88AF76ECA0C30E81F633E1F7AF /* Pods-MTAISDK-Demo-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-MTAISDK-Demo-dummy.m"; sourceTree = "<group>"; };
		D25A2EA5EBF834C94DA40019B819D23C /* MTAiMakeupOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiMakeupOption.h; path = iOS/MTAiInterface.framework/Headers/MTMakeupModule/MTAiMakeupOption.h; sourceTree = "<group>"; };
		D2F49A7102742DDC00A73CC20BD866C4 /* MTEveAutoSkinColorModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEveAutoSkinColorModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEveAutoSkinColorModule/MTEveAutoSkinColorModuleOption.h; sourceTree = "<group>"; };
		D3ADD832168F42ABE29861918CA25B79 /* MTHandModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHandModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHandModule/MTHandModuleOption.h; sourceTree = "<group>"; };
		D3E88329504AEF6365EF5FEA3F8315CD /* MTAiDoubleChinFixOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiDoubleChinFixOption.h; path = iOS/MTAiInterface.framework/Headers/MTDoubleChinFixModule/MTAiDoubleChinFixOption.h; sourceTree = "<group>"; };
		D4F3EBB3FD715B1AA0B4DE96F0ACF4EC /* LayoutConstraint.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutConstraint.swift; path = Sources/LayoutConstraint.swift; sourceTree = "<group>"; };
		D52DD4B023960880F97FE4F409E35298 /* MTSubColorTransfer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorTransfer.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubColorToning/MTSubColorTransfer/MTSubColorTransfer.h; sourceTree = "<group>"; };
		D535817BC2E5C0CEF28383944DD6F844 /* SnapKit-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SnapKit-dummy.m"; sourceTree = "<group>"; };
		D545D006DC635522BECD83FEE6DCD82F /* MTAiModel */ = {isa = PBXFileReference; includeInIndex = 1; name = MTAiModel; path = iOS/ModelModules/MTAiModelImageRecognition/manis/MTAiModel; sourceTree = "<group>"; };
		D5C1A426FF0086CF194304E72D092782 /* MTSubColorToningType.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorToningType.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubColorToning/MTSubColorToningType.h; sourceTree = "<group>"; };
		D7008B42F737C29D76491B07C86305BF /* MTAiSkinResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinResult.h; path = iOS/MTAiInterface.framework/Headers/MTSkinModule/MTAiSkinResult.h; sourceTree = "<group>"; };
		D705535338D374F57D23625366E7449F /* MTAiEyelidImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEyelidImage.h; path = iOS/MTAiInterface.framework/Headers/MTEyelidImageModule/MTAiEyelidImage.h; sourceTree = "<group>"; };
		D92C363353E47E179D761821C1AFE118 /* MTAiImageRecognition.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiImageRecognition.h; path = iOS/MTAiInterface.framework/Headers/MTImageRecognitionModule/MTAiImageRecognition.h; sourceTree = "<group>"; };
		D933A4C398FA422D360C026AAE69EB47 /* MTEyeSegmentModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEyeSegmentModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEyeSegmentModule/MTEyeSegmentModuleOption.h; sourceTree = "<group>"; };
		D9BCD4E2E12EEBD3F47C50A5498769D4 /* MTAiHairFluffyOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairFluffyOption.h; path = iOS/MTAiInterface.framework/Headers/MTHairFluffyModule/MTAiHairFluffyOption.h; sourceTree = "<group>"; };
		D9DCA2FF747F0470870B9DA19CC58FD4 /* ConstraintMultiplierTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMultiplierTarget.swift; path = Sources/ConstraintMultiplierTarget.swift; sourceTree = "<group>"; };
		DA174F12DC4B70ACE1DF5B27B2EC1766 /* MTAiAnimal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAnimal.h; path = iOS/MTAiInterface.framework/Headers/MTAnimalModule/MTAiAnimal.h; sourceTree = "<group>"; };
		DA2665C2C782EE469E2A41A263B05914 /* MTAiEvePreDetect.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEvePreDetect.h; path = iOS/MTAiInterface.framework/Headers/MTEvePreDetectModule/MTAiEvePreDetect.h; sourceTree = "<group>"; };
		DA346B9532EE32EDEBDD6017CFBF9B77 /* MTAiHairDyeOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairDyeOption.h; path = iOS/MTAiInterface.framework/Headers/MTHairDyeModule/MTAiHairDyeOption.h; sourceTree = "<group>"; };
		DA7889C854A108F5AD2A691349159058 /* MTMaterialTrackingResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTMaterialTrackingResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTMaterialTrackingModule/MTMaterialTrackingResult.h; sourceTree = "<group>"; };
		DB042F0C71BD861C0E707F5F2795E178 /* MTSceneryBoundaryLineModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSceneryBoundaryLineModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSceneryBoundaryLineModule/MTSceneryBoundaryLineModuleOption.h; sourceTree = "<group>"; };
		DB1547C2617026F9F99489276228C1F2 /* MTWrinkleDetectionModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTWrinkleDetectionModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTWrinkleDetectionModule/MTWrinkleDetectionModuleOption.h; sourceTree = "<group>"; };
		DB399E478F7B99279EB59836852E323E /* MTEyelidRealtimeModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEyelidRealtimeModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEyelidRealtimeModule/MTEyelidRealtimeModuleOption.h; sourceTree = "<group>"; };
		DD5A6EFEC764B65D36415B3E1F0F70BC /* MTAiEngineOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineOption.h; path = iOS/MTAiInterface.framework/Headers/MTAiEngineOption.h; sourceTree = "<group>"; };
		DD84FA6FC45144AA8F291D3992537245 /* AiKitTool.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AiKitTool.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTAIKitModule/AiKitTool.h; sourceTree = "<group>"; };
		DD877F08789D7B6037E1E38194999691 /* MTHairDyeModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairDyeModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairDyeModule/MTHairDyeModuleOption.h; sourceTree = "<group>"; };
		DDFF2291783F286328990604D2018E00 /* mvgif.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = mvgif.release.xcconfig; sourceTree = "<group>"; };
		DE4EC308E7E1431E504C09DE8F186EA3 /* MTAiVideoStabilization.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoStabilization.h; path = iOS/MTAiInterface.framework/Headers/MTVideoStabilizationModule/MTAiVideoStabilization.h; sourceTree = "<group>"; };
		E093DC67182FBCC48D10280AC617F8D1 /* MTSubVideoRecognition.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubVideoRecognition.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/VideoRecognition/MTSubVideoRecognition.h; sourceTree = "<group>"; };
		E0A2F41E7D9E1E84D200C33DBB6EDAAA /* SnapKit.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SnapKit.release.xcconfig; sourceTree = "<group>"; };
		E0E40C5C5FDFC63A4BA8FF707BC0B32A /* MTAiFoodResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFoodResult.h; path = iOS/MTAiInterface.framework/Headers/MTFoodModule/MTAiFoodResult.h; sourceTree = "<group>"; };
		E19340609C74BD1342C90452BD5B4FF6 /* Constraint.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Constraint.swift; path = Sources/Constraint.swift; sourceTree = "<group>"; };
		E254DCFEA0DDBB3F03E34D7DB2C20D88 /* MTSubColorToningEW.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorToningEW.h; path = iOS/MTAiInterface.framework/Headers/MT3rdpartyModule/MTSubColorToning/MTSubColorToningEW/MTSubColorToningEW.h; sourceTree = "<group>"; };
		E2635463178B3E8802CE18E0466F0A5E /* ConstraintConstantTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintConstantTarget.swift; path = Sources/ConstraintConstantTarget.swift; sourceTree = "<group>"; };
		E31376FCA4F439DED09B0F6627399548 /* Typealiases.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Typealiases.swift; path = Sources/Typealiases.swift; sourceTree = "<group>"; };
		E47F5634CF5969C0D3494A1CB3FAD128 /* MTAiDenseHairOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiDenseHairOption.h; path = iOS/MTAiInterface.framework/Headers/MTDenseHairModule/MTAiDenseHairOption.h; sourceTree = "<group>"; };
		E5E82F9748C4DFDBCE8E8FE007A8A632 /* MTAiFaceBlitOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceBlitOption.h; path = iOS/MTAiInterface.framework/Headers/MTFaceBlitModule/MTAiFaceBlitOption.h; sourceTree = "<group>"; };
		E670B086F90F33A33ADC575BC2AC1C3F /* mtstitch_MTAiEngineDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = mtstitch_MTAiEngineDefine.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/3rdparty/mtstitch/mtstitch_MTAiEngineDefine.h; sourceTree = "<group>"; };
		E6CDDD0B8B15CB011B201C5AEB3692E0 /* MTHairFluffyModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairFluffyModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairFluffyModule/MTHairFluffyModuleOption.h; sourceTree = "<group>"; };
		E7976C49E405B405CA4AA1323C0021C5 /* MTAiEngineImage.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineImage.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineImage.h; sourceTree = "<group>"; };
		E853A8AFA1054FE3C04DD497DB42E5E8 /* Pods-MTAISDK-Demo-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-MTAISDK-Demo-acknowledgements.plist"; sourceTree = "<group>"; };
		E871CCB546ED4645E65F36375992B00A /* MTHairCurlyResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairCurlyResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairCurlyModule/MTHairCurlyResult.h; sourceTree = "<group>"; };
		EA8C96F78CC2A3E8D678F60FB09266AC /* MTAiImageDetectionOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiImageDetectionOption.h; path = iOS/MTAiInterface.framework/Headers/MTImageDetectionModule/MTAiImageDetectionOption.h; sourceTree = "<group>"; };
		EB4F44C6985510F6403081234D8B37E7 /* MTAiEngineDefine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineDefine.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineDefine.h; sourceTree = "<group>"; };
		EC31BE468120B421718CAADF1723FC92 /* ConstraintMakerFinalizable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerFinalizable.swift; path = Sources/ConstraintMakerFinalizable.swift; sourceTree = "<group>"; };
		ECF278ABA149D36EBE61BC5B5E67063C /* MTSubColorToning.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubColorToning.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubColorToning/MTSubColorToning.h; sourceTree = "<group>"; };
		ED07DBCCD97D85C0177BAF39E529AB0B /* MTAiDL3DAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiDL3DAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTDL3DModule/Attribute/MTAiDL3DAttribute.h; sourceTree = "<group>"; };
		ED7094A2CB75C3AAAF8785D0E0728C31 /* MTAiFoodStyle.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFoodStyle.h; path = iOS/MTAiInterface.framework/Headers/MTFoodStyleModule/MTAiFoodStyle.h; sourceTree = "<group>"; };
		ED7B4FDDAC9948B0D9E068075288B150 /* MTShoulderModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTShoulderModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTShoulderModule/MTShoulderModuleOption.h; sourceTree = "<group>"; };
		EE09CE272B68064B9C6C4ADF55598E7E /* MTAiEngineLog_new.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineLog_new.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineLog_new.h; sourceTree = "<group>"; };
		EF9E94ADB8A23A37329A82FB5D08E847 /* MTAiToKidOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiToKidOption.h; path = iOS/MTAiInterface.framework/Headers/MTToKidModule/MTAiToKidOption.h; sourceTree = "<group>"; };
		EFBD4718F9093485075BC9C5EB62325C /* MTBodyResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTBodyResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTBodyModule/MTBodyResult.h; sourceTree = "<group>"; };
		F09555A3CB2F9B608F7B4E17B5851B55 /* MTAiEngineConfig.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineConfig.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineConfig.h; sourceTree = "<group>"; };
		F0BDCB8EDF79BFE7C10FE5ECC2DEEB32 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests-Info.plist"; sourceTree = "<group>"; };
		F1698073B29F8CF3BA3818F26AE04B66 /* MTAiAnimalOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiAnimalOption.h; path = iOS/MTAiInterface.framework/Headers/MTAnimalModule/MTAiAnimalOption.h; sourceTree = "<group>"; };
		F1D63C42147F3D45D8395F70C01DF798 /* ConstraintItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintItem.swift; path = Sources/ConstraintItem.swift; sourceTree = "<group>"; };
		F1EABA59F43766CBC3057003B7B94E08 /* MTPortraitInpaintingModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTPortraitInpaintingModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTPortraitInpaintingModule/MTPortraitInpaintingModuleOption.h; sourceTree = "<group>"; };
		F1F213BDAB2A378595794AA10D7F803C /* MTEvePreDetectResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEvePreDetectResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEvePreDetectModule/MTEvePreDetectResult.h; sourceTree = "<group>"; };
		F224FE1B8735442498465D523C24AB38 /* Pods-MTAISDK-Demo-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-MTAISDK-Demo-Info.plist"; sourceTree = "<group>"; };
		F2292DECF642EF4AD762AFA41330BEC4 /* MTAiSkinAttribute.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinAttribute.h; path = iOS/MTAiInterface.framework/Headers/MTSkinModule/Attribute/MTAiSkinAttribute.h; sourceTree = "<group>"; };
		F232A0CCBD9985CC0E6E7441BA5A7748 /* MTSubMaskFeather.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubMaskFeather.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/IntelligentFusion/MTSubMaskFeather.h; sourceTree = "<group>"; };
		F26A1543B9298FF1B6061E3D5B4416CC /* MTFaceHDResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceHDResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceHDModule/MTFaceHDResult.h; sourceTree = "<group>"; };
		F29D294FAACC2307817F3B0E971A3847 /* MTEyelidImageResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEyelidImageResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEyelidImageModule/MTEyelidImageResult.h; sourceTree = "<group>"; };
		F2A625A0DC98B5CA8384C0497DDDF032 /* ConstraintDescription.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDescription.swift; path = Sources/ConstraintDescription.swift; sourceTree = "<group>"; };
		F2A7E6B88412EA56BC3C451DC9E468A8 /* MTAiOrnament.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiOrnament.h; path = iOS/MTAiInterface.framework/Headers/MTOrnamentModule/MTAiOrnament.h; sourceTree = "<group>"; };
		F2DEC824721CEFCA6F022E4671AE4765 /* MTAiHairOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairOption.h; path = iOS/MTAiInterface.framework/Headers/MTHairModule/MTAiHairOption.h; sourceTree = "<group>"; };
		F319068F7C61A737EFE6BEEF1D509313 /* MTAiSkinAR.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinAR.h; path = iOS/MTAiInterface.framework/Headers/MTSkinARModule/MTAiSkinAR.h; sourceTree = "<group>"; };
		F4A044B99D25BD5DFA6FBF3595C1B013 /* MTSkinBCCModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSkinBCCModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTSkinBCCModule/MTSkinBCCModuleOption.h; sourceTree = "<group>"; };
		F4B7047257A72C793708CCCCBA70F2F0 /* MTFaceAnalysisXResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceAnalysisXResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceAnalysisXModule/MTFaceAnalysisXResult.h; sourceTree = "<group>"; };
		F4F1AEBA88470134E4DA6B478D294D6C /* MTAiWrinkleDetection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiWrinkleDetection.h; path = iOS/MTAiInterface.framework/Headers/MTWrinkleDetectionModule/MTAiWrinkleDetection.h; sourceTree = "<group>"; };
		F503F63F658042D51044A029F07E4438 /* coreml.metallib */ = {isa = PBXFileReference; includeInIndex = 1; name = coreml.metallib; path = iOS/universal/metallib/coreml.metallib; sourceTree = "<group>"; };
		F525AD3078A80DAC93FA4896F98AFDF0 /* MTNoseBlendResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTNoseBlendResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTNoseBlendModule/MTNoseBlendResult.h; sourceTree = "<group>"; };
		F5651966DA7094A28C9FE7A927E730E2 /* MTNevusDetectionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTNevusDetectionResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTNevusDetectionModule/MTNevusDetectionResult.h; sourceTree = "<group>"; };
		F5700B2A3A77A2F4585FBBC3949F08C5 /* MTAiSymExport.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSymExport.h; path = iOS/MTAiInterface.framework/Headers/MTAiSymExport.h; sourceTree = "<group>"; };
		F652E624A5EC398C6C44A0738F81EAB7 /* MTAiFaceBlitResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiFaceBlitResult.h; path = iOS/MTAiInterface.framework/Headers/MTFaceBlitModule/MTAiFaceBlitResult.h; sourceTree = "<group>"; };
		F6541847678CCFC0DAC0893B68D4489A /* MTRemoveWatermarkModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTRemoveWatermarkModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTRemoveWatermarkModule/MTRemoveWatermarkModuleOption.h; sourceTree = "<group>"; };
		F72DB25B9F434375DB7D199C4874E813 /* MTToKidModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTToKidModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTToKidModule/MTToKidModuleOption.h; sourceTree = "<group>"; };
		F7FE9CDCDE0843E05B20E947D97361E1 /* MTHairDyeResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTHairDyeResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTHairDyeModule/MTHairDyeResult.h; sourceTree = "<group>"; };
		F89563697C59253D124A6C61DE621EB6 /* MTAiVideoOptimizer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiVideoOptimizer.h; path = iOS/MTAiInterface.framework/Headers/MTVideoOptimizerModule/MTAiVideoOptimizer.h; sourceTree = "<group>"; };
		F96ACBA645B093B90BBA05C114B9A045 /* MT3DFaceModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MT3DFaceModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3DFaceModule/MT3DFaceModuleOption.h; sourceTree = "<group>"; };
		F994B2A428500D02E9ACC464C74E1B1A /* MTAiRTTeethRetouchResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiRTTeethRetouchResult.h; path = iOS/MTAiInterface.framework/Headers/MTRTTeethRetouchModule/MTAiRTTeethRetouchResult.h; sourceTree = "<group>"; };
		F997C0B5C499376E94CAAD9A2E209819 /* MTAiEveAutoSkinColorResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEveAutoSkinColorResult.h; path = iOS/MTAiInterface.framework/Headers/MTEveAutoSkinColorModule/MTAiEveAutoSkinColorResult.h; sourceTree = "<group>"; };
		F9B37F42F153EA9D5EB435651D5E7F09 /* MTFaceHDModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTFaceHDModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTFaceHDModule/MTFaceHDModuleOption.h; sourceTree = "<group>"; };
		F9EB42FDED006E3B42AA6A89BE57AFBE /* MTAiMakeupResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiMakeupResult.h; path = iOS/MTAiInterface.framework/Headers/MTMakeupModule/MTAiMakeupResult.h; sourceTree = "<group>"; };
		FA2D4BBBFECC70F2B068549346C0E7EE /* MTAiHairDyeResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiHairDyeResult.h; path = iOS/MTAiInterface.framework/Headers/MTHairDyeModule/MTAiHairDyeResult.h; sourceTree = "<group>"; };
		FB263902FADE2F0B84EAD832732789B7 /* MTAiWrinkleDetectionResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiWrinkleDetectionResult.h; path = iOS/MTAiInterface.framework/Headers/MTWrinkleDetectionModule/MTAiWrinkleDetectionResult.h; sourceTree = "<group>"; };
		FB619904A5A836D98DC08FD8CCDB7312 /* MTSubFaceRefinerEngine.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubFaceRefinerEngine.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rdpartyModule/MTSubFaceRefinerEngine/MTSubFaceRefinerEngine.h; sourceTree = "<group>"; };
		FB7E3B0D8562784D3025134D1AEF76F0 /* MTAnimalResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAnimalResult.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTAnimalModule/MTAnimalResult.h; sourceTree = "<group>"; };
		FD0D308618CFE3D9950D9E95D27092D5 /* MTSubMelt.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubMelt.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/IntelligentFusion/MTSubMelt.h; sourceTree = "<group>"; };
		FD651442CCD234FC23C12A8AB3D4F37E /* LevelMapRelation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = LevelMapRelation.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTImageRecognitionModule/LevelMapRelation.h; sourceTree = "<group>"; };
		FDDB39A9F19859C5AC93A1ECB51A1BCE /* MTAiModel */ = {isa = PBXFileReference; includeInIndex = 1; name = MTAiModel; path = iOS/ModelModules/MTAiModelFaceDetector/manis/MTAiModel; sourceTree = "<group>"; };
		FE0B455AA7398A27708CA1232561854C /* MTAiEngineImageUtils.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiEngineImageUtils.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/Common/MTAiEngineImageUtils.h; sourceTree = "<group>"; };
		FE17901C183A4A0707345228A485227E /* MTAiImageDetection.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiImageDetection.h; path = iOS/MTAiInterface.framework/Headers/MTImageDetectionModule/MTAiImageDetection.h; sourceTree = "<group>"; };
		FE1A47668D051ACA09C39F8AD7D11D44 /* MTEveQualityModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTEveQualityModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTEveQualityModule/MTEveQualityModuleOption.h; sourceTree = "<group>"; };
		FF3BD2245DF95EEC0E4B16A97FB6AB75 /* MTAiSkinBCCResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTAiSkinBCCResult.h; path = iOS/MTAiInterface.framework/Headers/MTSkinBCCModule/MTAiSkinBCCResult.h; sourceTree = "<group>"; };
		FF68C3F6EEBDB0A067EDBE36F515E140 /* MTMaterialTrackingModuleOption.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTMaterialTrackingModuleOption.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MTMaterialTrackingModule/MTMaterialTrackingModuleOption.h; sourceTree = "<group>"; };
		FFF048720C72CA57282384F6422C5D14 /* MTSubDLBeautyBase.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MTSubDLBeautyBase.h; path = iOS/MTAiInterface.framework/Headers/CppHeaders/mtai/MT3rtpartyModule/libDLBeautyBase/MTSubDLBeautyBase.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2865E5A4F984F993E6F6AD24411D1D1F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97F15922353F3026FEE8720BE25382C9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		33428AC36668E3ED52DB70316F843FB8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				205EB01AED14BB574DD54EAFE26E4786 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		83F4283922547A7E1AF31EF6297CF7F2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F0D264D9EA7066F7724B67B79AA24366 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D2D3997219A6766AAA57BA94AEDE1BF7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B448131017162474260D7691FE72391 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E075A9E9B1E24AD178BB765D865452CE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		042649772B1AD04A7DE0A8AC16CC8AB9 /* iOS */ = {
			isa = PBXGroup;
			children = (
				7F74777EEE8EF63514F9147AE160D76C /* Frameworks */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		07A6076D0FD9F51CDD1642CDBC9940F8 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				71014FBE0B12AD850EB3AAFAB95B4C9E /* libmanis.debug.xcconfig */,
				A9F64031C165E652ED4AFCBF751809E2 /* libmanis.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/libmanis";
			sourceTree = "<group>";
		};
		0C914E487CD62F74B1BFC569D99D213A /* libmanis */ = {
			isa = PBXGroup;
			children = (
				3B776A75405ADAE2F9953E2170213496 /* framework */,
				703E994F3D7352DEB18FE8427C83D565 /* metal */,
				07A6076D0FD9F51CDD1642CDBC9940F8 /* Support Files */,
			);
			name = libmanis;
			path = libmanis;
			sourceTree = "<group>";
		};
		259AF25AFBBE563922888D1593287256 /* Pods-MTAISDK-Demo */ = {
			isa = PBXGroup;
			children = (
				9633B78B6DC1BD044B7BC8BBC582720A /* Pods-MTAISDK-Demo.modulemap */,
				2CB8B7E65817BF9089BA076D38474E87 /* Pods-MTAISDK-Demo-acknowledgements.markdown */,
				E853A8AFA1054FE3C04DD497DB42E5E8 /* Pods-MTAISDK-Demo-acknowledgements.plist */,
				D1FB8E88AF76ECA0C30E81F633E1F7AF /* Pods-MTAISDK-Demo-dummy.m */,
				AAF624EDB6705B789FAABF369D4ECD17 /* Pods-MTAISDK-Demo-frameworks.sh */,
				F224FE1B8735442498465D523C24AB38 /* Pods-MTAISDK-Demo-Info.plist */,
				C4DFA91F19EF65FC74ED00D3AA75AE02 /* Pods-MTAISDK-Demo-resources.sh */,
				00B92D6586C55C658AAA068F8771C7C3 /* Pods-MTAISDK-Demo-umbrella.h */,
				894D941CB569EF6C59BCADC50D34EE2A /* Pods-MTAISDK-Demo.debug.xcconfig */,
				2A75056332507A3D28753B74BED70748 /* Pods-MTAISDK-Demo.release.xcconfig */,
			);
			name = "Pods-MTAISDK-Demo";
			path = "Target Support Files/Pods-MTAISDK-Demo";
			sourceTree = "<group>";
		};
		2E3E7FD919D42C8BFA3B00D716D49FAB /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests */ = {
			isa = PBXGroup;
			children = (
				B5D7C5E325A8A13B80F29F11EF06C920 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.modulemap */,
				4DA825A23EDA5037B67C0005242B8999 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-acknowledgements.markdown */,
				A29A0D416F45998B3E35BE682773F9D0 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-acknowledgements.plist */,
				98C17B3F0975AEB8522068BB1671D0B0 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-dummy.m */,
				CEB293796ED145864B622CDC4F9AAAC8 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-frameworks.sh */,
				F0BDCB8EDF79BFE7C10FE5ECC2DEEB32 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-Info.plist */,
				15FD2DB774088207D1FEFB49293633FD /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-resources.sh */,
				616888DF0091F596E6EB52564B055E79 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-umbrella.h */,
				5A6C36ADEE6025C328CC6B0DC07E7A43 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig */,
				7E4FF76C4C4C837F3231717070F6214D /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig */,
			);
			name = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests";
			path = "Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests";
			sourceTree = "<group>";
		};
		355934A9325E6F68FAAF22D7FBC5ECCF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5E0367893484F71BD8EC95FA840B6B5F /* mtlabrecord.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		3B776A75405ADAE2F9953E2170213496 /* framework */ = {
			isa = PBXGroup;
			children = (
				9D4F4E070870275D977E3D01C08AA97B /* Frameworks */,
			);
			name = framework;
			sourceTree = "<group>";
		};
		4188F5C7C7D1C8F3931439AFC0C6634E /* ImageRecognition */ = {
			isa = PBXGroup;
			children = (
				610995CD9439C7BC000B66E8CA97577A /* Resources */,
			);
			name = ImageRecognition;
			sourceTree = "<group>";
		};
		4D3A64825FCA5A0B6448AE4AD4C8E924 /* Resources */ = {
			isa = PBXGroup;
			children = (
				B1FD03EB51DE25FD35D1EAD5BCCA1441 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		50CA851B179B58C8031D537A3FAFB45E /* Pods */ = {
			isa = PBXGroup;
			children = (
				0C914E487CD62F74B1BFC569D99D213A /* libmanis */,
				5E38C297E64B70AF7D96115E2045DC0E /* libmtaiinterface */,
				B5ADF95611840E94931499FA6A57827C /* mtlabrecord */,
				8B3646E1D4A8B3FAC815BBE60FEEFE3F /* mvgif */,
				91BAE7E8A37715D82D6FF084C6C6420C /* SnapKit */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		5E38C297E64B70AF7D96115E2045DC0E /* libmtaiinterface */ = {
			isa = PBXGroup;
			children = (
				CFBD86A261560FF8FBF255F4C481FA01 /* FaceDetector */,
				D3C0D4FC76FF1660D0DA801B0DAA97CD /* framework */,
				4188F5C7C7D1C8F3931439AFC0C6634E /* ImageRecognition */,
				FE6575A11DBF84F569063ED7C04EF6A8 /* Support Files */,
			);
			name = libmtaiinterface;
			path = libmtaiinterface;
			sourceTree = "<group>";
		};
		5EA7096F27E200BDCCFF55D05B2EB62F /* Pods-MTAISDK-DemoTests */ = {
			isa = PBXGroup;
			children = (
				A81675293C6F08A973E63799D5699DBA /* Pods-MTAISDK-DemoTests.modulemap */,
				C2CF0166B362036EBAF529DAE60D3772 /* Pods-MTAISDK-DemoTests-acknowledgements.markdown */,
				772E2F0EDF3313FE5ECE337C2B3A2333 /* Pods-MTAISDK-DemoTests-acknowledgements.plist */,
				C8F911498295C41579C0FB6EAF435819 /* Pods-MTAISDK-DemoTests-dummy.m */,
				8C9534A25639AB68B09059BB201586B9 /* Pods-MTAISDK-DemoTests-Info.plist */,
				5973054DE54CC0CE28B157136B6535AD /* Pods-MTAISDK-DemoTests-umbrella.h */,
				98E40FDBCF6F4761B703F642C0BE0516 /* Pods-MTAISDK-DemoTests.debug.xcconfig */,
				4BFE5C5116E5EFF071F73ADE6A3A63CA /* Pods-MTAISDK-DemoTests.release.xcconfig */,
			);
			name = "Pods-MTAISDK-DemoTests";
			path = "Target Support Files/Pods-MTAISDK-DemoTests";
			sourceTree = "<group>";
		};
		610995CD9439C7BC000B66E8CA97577A /* Resources */ = {
			isa = PBXGroup;
			children = (
				D545D006DC635522BECD83FEE6DCD82F /* MTAiModel */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		703E994F3D7352DEB18FE8427C83D565 /* metal */ = {
			isa = PBXGroup;
			children = (
				827321B84B52FA569376AA95178C79DF /* Resources */,
			);
			name = metal;
			sourceTree = "<group>";
		};
		7C94582C2896BA6AD70CD2705AFB0DC9 /* Resources */ = {
			isa = PBXGroup;
			children = (
				FDDB39A9F19859C5AC93A1ECB51A1BCE /* MTAiModel */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		7F74777EEE8EF63514F9147AE160D76C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1E6472FB5DAD265ECF3D9849360F3A2F /* mvgif.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		827321B84B52FA569376AA95178C79DF /* Resources */ = {
			isa = PBXGroup;
			children = (
				F503F63F658042D51044A029F07E4438 /* coreml.metallib */,
				16C18D39B8EC9E88F20B0997FFEA8151 /* Manis.metallib */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		8B3646E1D4A8B3FAC815BBE60FEEFE3F /* mvgif */ = {
			isa = PBXGroup;
			children = (
				042649772B1AD04A7DE0A8AC16CC8AB9 /* iOS */,
				ABC18E17932C753C6BA2FAC3D75804B1 /* Support Files */,
			);
			name = mvgif;
			path = mvgif;
			sourceTree = "<group>";
		};
		91BAE7E8A37715D82D6FF084C6C6420C /* SnapKit */ = {
			isa = PBXGroup;
			children = (
				E19340609C74BD1342C90452BD5B4FF6 /* Constraint.swift */,
				A843C54D2CD3C6332A2FD8B675EDBDC9 /* ConstraintAttributes.swift */,
				8DD19394C90076DA2D462DA402D67A4F /* ConstraintConfig.swift */,
				E2635463178B3E8802CE18E0466F0A5E /* ConstraintConstantTarget.swift */,
				F2A625A0DC98B5CA8384C0497DDDF032 /* ConstraintDescription.swift */,
				9522031A21249BA4A0EE770C6B6ED571 /* ConstraintDirectionalInsets.swift */,
				0108DD77CD42DC5050040074A3EB06B7 /* ConstraintDirectionalInsetTarget.swift */,
				20602C81DBE436846BAF8C6125F7E007 /* ConstraintDSL.swift */,
				BB1019D96BD52835E7B6D1DE2809110E /* ConstraintInsets.swift */,
				0AF5ED1324592889C66CC0F4552DFD95 /* ConstraintInsetTarget.swift */,
				F1D63C42147F3D45D8395F70C01DF798 /* ConstraintItem.swift */,
				51411C81BFADDF8F784E8A5B97ABE4CD /* ConstraintLayoutGuide.swift */,
				692C7432002D55943BF8AC866AF7333C /* ConstraintLayoutGuide+Extensions.swift */,
				0C08E9263742D27691289F4A576B81C2 /* ConstraintLayoutGuideDSL.swift */,
				27A72570BCD51660E8E5215816D09838 /* ConstraintLayoutSupport.swift */,
				3B24C6502C5CBB0D966D40FA3BCACEE1 /* ConstraintLayoutSupportDSL.swift */,
				5B0AEA3EB83116EC3E7231CF132D8DA9 /* ConstraintMaker.swift */,
				0F38AD669A4A13134403CDEEB9937ED4 /* ConstraintMakerEditable.swift */,
				7F5620037A88373341C572871C5FFB62 /* ConstraintMakerExtendable.swift */,
				EC31BE468120B421718CAADF1723FC92 /* ConstraintMakerFinalizable.swift */,
				17B84108AD8FEEE4161BD4425B5931DA /* ConstraintMakerPrioritizable.swift */,
				A387F144056BD1593B18022A938F6093 /* ConstraintMakerRelatable.swift */,
				12C8B2779825E81B49315DEA943A10E9 /* ConstraintMakerRelatable+Extensions.swift */,
				D9DCA2FF747F0470870B9DA19CC58FD4 /* ConstraintMultiplierTarget.swift */,
				3AA908141A906DC0A050BB7A4ED557EB /* ConstraintOffsetTarget.swift */,
				5C2D883A320F44D6046ED546D66BCF13 /* ConstraintPriority.swift */,
				7E12191FFF32C8211C0ABBADFC92FF71 /* ConstraintPriorityTarget.swift */,
				1E4896E072CDC3DF3427F09F24BF0E40 /* ConstraintRelatableTarget.swift */,
				90D141B8F92646B088015629442E5A0C /* ConstraintRelation.swift */,
				012E18F31B4434528EC4B70D3A7CBD9F /* ConstraintView.swift */,
				78C75B1847B7793F457F7C95DD9CAD1F /* ConstraintView+Extensions.swift */,
				A3C4BE83192B1881F2D444215B22458C /* ConstraintViewDSL.swift */,
				587E1AFA22841ED54F0B405083DAFB58 /* Debugging.swift */,
				D4F3EBB3FD715B1AA0B4DE96F0ACF4EC /* LayoutConstraint.swift */,
				C0EF44FE64FCA4ACA4E3F37488DA9BEE /* LayoutConstraintItem.swift */,
				E31376FCA4F439DED09B0F6627399548 /* Typealiases.swift */,
				164DBE52F60515A603635DA50C9EC1C5 /* UILayoutSupport+Extensions.swift */,
				4D3A64825FCA5A0B6448AE4AD4C8E924 /* Resources */,
				C27D2B1465224EB3253BB78D99ACAB0F /* Support Files */,
			);
			name = SnapKit;
			path = SnapKit;
			sourceTree = "<group>";
		};
		99911056C63D60323FCCF1DA08802E95 /* Products */ = {
			isa = PBXGroup;
			children = (
				07968C4F4D0AB4C6A603E6A2BE7835A1 /* Pods-MTAISDK-Demo */,
				A07B6C17DD730ADC236315AF7A127516 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests */,
				2E0C2F6B52B37E811FA40ABD102AA294 /* Pods-MTAISDK-DemoTests */,
				979486118B3E90C08386079D57962701 /* SnapKit */,
				B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9D4F4E070870275D977E3D01C08AA97B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9D166578015013BF1069BFF4CEDEAC01 /* Manis.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		ABC18E17932C753C6BA2FAC3D75804B1 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				9F1B2DEFDBEDB361FF31ABC8DE2FF038 /* mvgif.debug.xcconfig */,
				DDFF2291783F286328990604D2018E00 /* mvgif.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/mvgif";
			sourceTree = "<group>";
		};
		B1E3F1DE40DEFF8EE2A3986D54269D2F /* shared */ = {
			isa = PBXGroup;
			children = (
				355934A9325E6F68FAAF22D7FBC5ECCF /* Frameworks */,
			);
			name = shared;
			sourceTree = "<group>";
		};
		B5ADF95611840E94931499FA6A57827C /* mtlabrecord */ = {
			isa = PBXGroup;
			children = (
				B1E3F1DE40DEFF8EE2A3986D54269D2F /* shared */,
				EFBFF613671EC86C7E740FD0EEAE972D /* Support Files */,
			);
			name = mtlabrecord;
			path = mtlabrecord;
			sourceTree = "<group>";
		};
		C27D2B1465224EB3253BB78D99ACAB0F /* Support Files */ = {
			isa = PBXGroup;
			children = (
				B3CF28136520A7D08BE3F587C9CE96AC /* ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist */,
				011CCA9CA551C9A8682E02EBA4C8E750 /* SnapKit.modulemap */,
				D535817BC2E5C0CEF28383944DD6F844 /* SnapKit-dummy.m */,
				5F25B435AAAB9E592FF5FEB4B706FC5A /* SnapKit-Info.plist */,
				43EF74B1D521DDCBEB9D1775488C873C /* SnapKit-prefix.pch */,
				B393D052A1E7E9C9560C94ED07FA7C66 /* SnapKit-umbrella.h */,
				8386B1D734AC8907DE7478743E63E290 /* SnapKit.debug.xcconfig */,
				E0A2F41E7D9E1E84D200C33DBB6EDAAA /* SnapKit.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SnapKit";
			sourceTree = "<group>";
		};
		C604CF0DCA4672D98DF6726DB78CAC88 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				259AF25AFBBE563922888D1593287256 /* Pods-MTAISDK-Demo */,
				2E3E7FD919D42C8BFA3B00D716D49FAB /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests */,
				5EA7096F27E200BDCCFF55D05B2EB62F /* Pods-MTAISDK-DemoTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */,
				50CA851B179B58C8031D537A3FAFB45E /* Pods */,
				99911056C63D60323FCCF1DA08802E95 /* Products */,
				C604CF0DCA4672D98DF6726DB78CAC88 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		CFBD86A261560FF8FBF255F4C481FA01 /* FaceDetector */ = {
			isa = PBXGroup;
			children = (
				7C94582C2896BA6AD70CD2705AFB0DC9 /* Resources */,
			);
			name = FaceDetector;
			sourceTree = "<group>";
		};
		D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E4801F62A6B08CD9B5410329F1A18FDE /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D3C0D4FC76FF1660D0DA801B0DAA97CD /* framework */ = {
			isa = PBXGroup;
			children = (
				DD84FA6FC45144AA8F291D3992537245 /* AiKitTool.h */,
				3A44FC37A848EBCB5B71C69DE617EA3C /* InceptionBeautyDefine.h */,
				99422FAA4205FD46E6BBC569B936BF47 /* InceptionBeautyDoubleChinFixUtil.h */,
				4D669C5B1A5DB701931F5285ABFA8803 /* InceptionBeautyRejuvenationUtil.h */,
				823BA689FCEB87FFD974402E733A417E /* InceptionBeautyRejuvenationUtilGL.h */,
				BB81036B19FCE9CDDBF4168AC41C3B0D /* InceptionBeautyUtil.h */,
				7EF5CD654DD588916C8BE48B38388414 /* InceptionBeautyUtilCoreML.h */,
				60F6123D3C637CAE6254383416427E5B /* InceptionBeautyUtilGL.h */,
				FD651442CCD234FC23C12A8AB3D4F37E /* LevelMapRelation.h */,
				0D9B34405E8FB09DF6CF7099093CEC5D /* MeituAiEngine.h */,
				7764259E9AE834FEF68FA316CA52F00B /* MeituAiEngine.h */,
				F96ACBA645B093B90BBA05C114B9A045 /* MT3DFaceModuleOption.h */,
				956237C360444609A65CCBACEECAD876 /* MT3DFaceResult.h */,
				48F1ED2B029AC222D147E01330B28368 /* MTAi3DFace.h */,
				7BF618B62F9EAF4B11E446B6E5D50C1E /* MTAi3DFaceAttribute.h */,
				48A9F47AF9111CF246D3848531DE461A /* MTAi3DFaceOption.h */,
				B02A7E4CEDFA89130959B261EADA40A2 /* MTAi3DFaceResult.h */,
				31CCB145D9EF3C9D1EFD03BE6F645766 /* MTAiAIKitOption.h */,
				C80050480E09E1D6277B8046BEEB6256 /* MTAiAIKitResult.h */,
				10B5147A75EA09BB3FDA0F53B2626B21 /* MTAiAnchorGeneration.h */,
				876FA3F8ECFE99BFEFAE1BD1FD3159D3 /* MTAiAnchorGenerationOption.h */,
				D04663977EC45CA4A73961A3A70E6029 /* MTAiAnchorGenerationResult.h */,
				DA174F12DC4B70ACE1DF5B27B2EC1766 /* MTAiAnimal.h */,
				0C7C57D0AB7BA11B0D57AD922C65520B /* MTAiAnimalAttribute.h */,
				F1698073B29F8CF3BA3818F26AE04B66 /* MTAiAnimalOption.h */,
				716EAC225749BD3D6ACF25232D38115D /* MTAiAnimalResult.h */,
				1359D6A0F11942057776E7E305FEC84E /* MTAiBody.h */,
				474AA5202ACAFE47BE04030A636129C5 /* MTAiBodyInOne.h */,
				ACB584D159DD5DB73BDFB1EF372E228E /* MTAiBodyInOneOption.h */,
				83A4B6EC4777A2AF55BF45EBEB3167EB /* MTAiBodyInOneResult.h */,
				D1AE850AC3E258A26C183EB59163D599 /* MTAiBodyOption.h */,
				9C342374D82F33AEADCE345C8CAACB6A /* MTAiBodyResult.h */,
				3F79E174DBDA947AAA2FAD2E3B8FB4AD /* MTAiCgStyle.h */,
				B581CCC4077CC21651940C57AE7097C7 /* MTAiCgStyleOption.h */,
				9215EFEEC9AEC43F17D99122CBD03F5D /* MTAiCgStyleResult.h */,
				C44867D548296DBF5E685D2F04849646 /* MTAiCsketch.h */,
				7802B05B071AC13CDB30837DA9EA119A /* MTAiCsketchOption.h */,
				CEEF65EDB651DFEF0BC42CFA6560CE86 /* MTAiCsketchResult.h */,
				E47F5634CF5969C0D3494A1CB3FAD128 /* MTAiDenseHairOption.h */,
				179B2B015CB0F7CE0E93139EABB105F4 /* MTAiDenseHairResult.h */,
				47AEB2945A74B3DDF31611E69A6B1E4A /* MTAiDL3D.h */,
				ED07DBCCD97D85C0177BAF39E529AB0B /* MTAiDL3DAttribute.h */,
				1AABBB5BD0ECF440F15EDC013F43740C /* MTAiDL3DOption.h */,
				81FCD542D9366D569E08FF6ED875F7D0 /* MTAiDL3DResult.h */,
				D3E88329504AEF6365EF5FEA3F8315CD /* MTAiDoubleChinFixOption.h */,
				986DC64F8BEDD6D28DB25236BB9BE0C0 /* MTAiDoubleChinFixResult.h */,
				96A1E0DAEE84D21A4AF41FB967F7D1DB /* MTAiEngineConfig.h */,
				F09555A3CB2F9B608F7B4E17B5851B55 /* MTAiEngineConfig.h */,
				EB4F44C6985510F6403081234D8B37E7 /* MTAiEngineDefine.h */,
				15F41B65D2950438D39A5CAB27E391FA /* MTAiEngineEnableOption.h */,
				A0D56C81BA6DC15D9B8CFDFFDD337EE4 /* MTAiEngineFrame.h */,
				178030E25A3578775413AD9B0812379E /* MTAiEngineGLInclude.h */,
				789AF9A9304B086097876D211D53341F /* MTAiEngineImage.h */,
				E7976C49E405B405CA4AA1323C0021C5 /* MTAiEngineImage.h */,
				60F2FCCE6C7B2D8BAF2079CA90352D63 /* MTAiEngineImage+Painter.h */,
				464E4CAE6DDEAF4DF7F68CA952AAF901 /* MTAiEngineImage+UIImage.h */,
				FE0B455AA7398A27708CA1232561854C /* MTAiEngineImageUtils.h */,
				196D358355876FCBDFF16F0F73151C60 /* MTAiEngineLog.h */,
				EE09CE272B68064B9C6C4ADF55598E7E /* MTAiEngineLog_new.h */,
				159367A4486D08E0E71136D3FFEB7C61 /* MTAiEngineLogDelegate.h */,
				3C1C6CB214DF6E61247EB2D900DC9FE5 /* MTAiEngineLogReflection.h */,
				C95081E67E315408DF227F63B30A02B1 /* MTAiEngineMacro.h */,
				3778513DBDFE0A37C770260E11953175 /* MTAiEngineOption.h */,
				DD5A6EFEC764B65D36415B3E1F0F70BC /* MTAiEngineOption.h */,
				1E7C12E2407A8AABCE0CCB0284922599 /* MTAiEnginePlatform.h */,
				164EE4515360B1D0E3C08A2ACF689197 /* MTAiEngineResult.h */,
				5EEE9D3FF14AEEF96FF14CAF0347488D /* MTAiEngineResult.h */,
				AB20F72C2BF45BCC81B921276C1704A8 /* MTAiEngineSupport.h */,
				83CF260696E7BA3E111236298FEDAA5A /* MTAiEngineTexture.h */,
				71493672BCFB04F4829B7E92ED8DF4F1 /* MTAiEngineTexture.h */,
				0156CD6A3712FAC4E62A8EC06BC8CFA2 /* MTAiEngineTextureUtils.h */,
				BDCD23F8B684EAB18F645009B911A36C /* MTAiEngineTimer.h */,
				B4BFE4A78B63ACC43EE8A41E3E04CDB3 /* MTAiEngineType.h */,
				436F6EAC5516875378932A6C47C1E828 /* MTAiEngineType.h */,
				A227DD25C672540CBB969A809D3C08D6 /* MTAiEngineUtils.h */,
				CB2053334DEFCEDDF64724C1C5070C98 /* MTAiErrorCallback.h */,
				AEA193BDDFF2A27225363685752504F8 /* MTAiErrorCallbackDetegate.h */,
				7C4C30175C370B9FED0F060F1EA47E18 /* MTAiEveAutoSkinColor.h */,
				9C66499FF0604DC461F205CE72E4FBD5 /* MTAiEveAutoSkinColorOption.h */,
				F997C0B5C499376E94CAAD9A2E209819 /* MTAiEveAutoSkinColorResult.h */,
				DA2665C2C782EE469E2A41A263B05914 /* MTAiEvePreDetect.h */,
				69FBAB24A451CD5CB69E613032F4AF4F /* MTAiEvePreDetectOption.h */,
				770F9F926AB39A1AB73D6B03F17088C4 /* MTAiEvePreDetectResult.h */,
				75ECD57CD5E7DF1BD77EA715AF101617 /* MTAiEveQuality.h */,
				43B448D73F7AE619E385EFA276203D12 /* MTAiEveQualityOption.h */,
				796B9B7B4FEF3ECEB5AEB1E613F412F8 /* MTAiEveQualityResult.h */,
				D705535338D374F57D23625366E7449F /* MTAiEyelidImage.h */,
				62C0881686EC20934785A7377D755544 /* MTAiEyelidImageOption.h */,
				42F1ED460E96E2B3CA2C771BD2EDA164 /* MTAiEyelidImageResult.h */,
				CFE741CD336FE75813614F4E5DCFB2A9 /* MTAiEyelidRealtimeOption.h */,
				5ABC1B7A5B71C15207ED99C1F6D73057 /* MTAiEyelidRealtimeResult.h */,
				58C59DBD92A48F2EC535D26020E898B7 /* MTAiEyeSegment.h */,
				C80CFF56D88444F5B98BDB0D309F2E42 /* MTAiEyeSegmentOption.h */,
				AC0D277A95D1EF7E0A50DA8A542394E6 /* MTAiEyeSegmentResult.h */,
				AD334EE6FDD7E9B7780469AB979AD06B /* MTAiFace.h */,
				61A1F5B162A3D83B8F056B6F76A45214 /* MTAiFaceAnalysisX.h */,
				A7EDDD96154AF118A19C9F0593C3AAA0 /* MTAiFaceAnalysisXOption.h */,
				78B8545E055C947361E10447AF4A1A14 /* MTAiFaceAnalysisXResult.h */,
				8CD17CB68A83EEEB4D7DB54015FAFCC5 /* MTAiFaceAttribute.h */,
				E5E82F9748C4DFDBCE8E8FE007A8A632 /* MTAiFaceBlitOption.h */,
				F652E624A5EC398C6C44A0738F81EAB7 /* MTAiFaceBlitResult.h */,
				6EAB9B6E2500B52CE7835F39E6CD8EF8 /* MTAiFaceHDFeature.h */,
				B2D14C23BD95FF009D1695EE1BA46F7C /* MTAiFaceHDOption.h */,
				14D9C6530870475BA04D057BF4AD0241 /* MTAiFaceHDResult.h */,
				A36D568F10ECE7750D89E1F131027C5C /* MTAiFaceOption.h */,
				37669ED41530C98575C7E46BA0613E74 /* MTAiFaceResult.h */,
				A0016DD4CFF17C01B62811AB6FCAC2BA /* MTAiFaceUtility.h */,
				036B26F09808629D6B188E113742F717 /* MTAiFood.h */,
				ABF11022B8FD76975BABA83002917AB0 /* MTAiFoodAttribute.h */,
				33A645A189DDFB394BDCB63B4613D84D /* MTAiFoodOption.h */,
				E0E40C5C5FDFC63A4BA8FF707BC0B32A /* MTAiFoodResult.h */,
				ED7094A2CB75C3AAAF8785D0E0728C31 /* MTAiFoodStyle.h */,
				245E6EB7C0A47B25619035F7A5336E21 /* MTAiFoodStyleOption.h */,
				CD72DCE7DA31E27179F76EFBD66CB8F5 /* MTAiFoodStyleResult.h */,
				4C3348D1920C0CD5C8B5CFF22C4D8850 /* MTAiHair.h */,
				0D7025859B9D7EBA432B841ABE10E2B4 /* MTAiHairAttribute.h */,
				70D520FB42BE8EB0F1229FDB77130055 /* MTAiHairDye.h */,
				DA346B9532EE32EDEBDD6017CFBF9B77 /* MTAiHairDyeOption.h */,
				FA2D4BBBFECC70F2B068549346C0E7EE /* MTAiHairDyeResult.h */,
				B3BDF355F1E15B2E375F20E3C1645CEA /* MTAiHairFluffy.h */,
				D9BCD4E2E12EEBD3F47C50A5498769D4 /* MTAiHairFluffyOption.h */,
				B07AC8A84E11D123B63870E139CEAF39 /* MTAiHairFluffyResult.h */,
				6CF479F75CF7260AB73603D871C3A402 /* MTAiHairGrouth.h */,
				41DAC6D8924B29C7F74305BCE93A78D4 /* MTAiHairGrouthOption.h */,
				D0AB795694E0C83FE894F511050EC39F /* MTAiHairGrouthResult.h */,
				F2DEC824721CEFCA6F022E4671AE4765 /* MTAiHairOption.h */,
				8A4F215DE83866C05A87DFBF513644CD /* MTAiHairResult.h */,
				09761B4CEC7F7346B9E38DF25BA350BA /* MTAiHairStraight.h */,
				57D916785A97C686AB1594AE3E8FE37B /* MTAiHairStraightOption.h */,
				86AEAEE27051B479A9E473AA54DEFDB9 /* MTAiHairStraightResult.h */,
				8C581F65BA98F281CD273DD0BB1FF353 /* MTAiHand.h */,
				C29CAEAFC79460E9E98523F07A60CF92 /* MTAiHandAttribute.h */,
				001D5144C990C4188AF4D9DFA5836A75 /* MTAiHandOption.h */,
				66657826CC350E3661ECBDB2ACBA7191 /* MTAiHandResult.h */,
				56480E479C38B52C0B83DD80B3190A04 /* MTAiHighDofEyelidOption.h */,
				A8434FFF0A9425FA9F47713DD508405D /* MTAiHighDofEyelidResult.h */,
				98B9838C4C93D87E4C7EDF5899FE8136 /* MTAiHuman3dBody.h */,
				B123561F9246609FA80D0F071B6D6F87 /* MTAiHuman3dHand.h */,
				40D9B903682F39EC3CEAFFD12365DBB6 /* MTAiHuman3dOption.h */,
				0C0417B27E7579F39633D343D8B09FF8 /* MTAiHuman3dResult.h */,
				605650638190F868B93E1EBAA2604B9E /* MTAiHuman3dSmpl.h */,
				FE17901C183A4A0707345228A485227E /* MTAiImageDetection.h */,
				EA8C96F78CC2A3E8D678F60FB09266AC /* MTAiImageDetectionOption.h */,
				A968DC02EB2A53A4314F9EBD63F45D75 /* MTAiImageDetectionResult.h */,
				D92C363353E47E179D761821C1AFE118 /* MTAiImageRecognition.h */,
				58DB902B22B71C44DBABE54B107C4828 /* MTAiImageRecognitionOption.h */,
				6444163469C702704A368F44C67686F1 /* MTAiImageRecognitionResult.h */,
				5DCBF293526E08F45D00439CF40FDA12 /* MTAiInstanceSeg.h */,
				9F8B597CD8F47B9692D372B77B99F978 /* MTAiInstanceSegmentOption.h */,
				6B04455540DC2284A2B84B3B1DA6CC6D /* MTAiInstanceSegmentResult.h */,
				5DD88C969112CFEBF1C65FCE6AB43814 /* MTAiKiev3DMake.h */,
				C122954309D5CB8CFCCB6A48B40AA7DB /* MTAiKiev3DMakeOption.h */,
				171ABCFB481B695F5E1546740303E247 /* MTAikiev3DMakeResult.h */,
				6F1571924C2351B540D78977C0453FE1 /* MTAIKitModuleOption.h */,
				2300BCC4EEBEECA8F40B9E397F771AFD /* MTAIKitResult.h */,
				1B932D0E06BC5DE49342AB3AF0882654 /* MTAiLandmarkOption.h */,
				5AB975F812E54087DB10BCB97B172FA2 /* MTAiLandmarkResult.h */,
				771C4041DD2FFB45F7F472FA37F8AD5F /* MTAiLicense.h */,
				0EF784CCA1F4F71822C46E62807E80C1 /* MTAiMakeupAttribute.h */,
				D25A2EA5EBF834C94DA40019B819D23C /* MTAiMakeupOption.h */,
				F9EB42FDED006E3B42AA6A89BE57AFBE /* MTAiMakeupResult.h */,
				B617EF0D906B1F459E4B91D6D84A6B4B /* MTAiMaterial.h */,
				89FD8022B5B81828CD1402886538E888 /* MTAiMaterialResult.h */,
				1E0302658CFDDE5CCDB1A70200623FE2 /* MTAiMaterialTrackingOption.h */,
				A4F381AC482DA2B5EB2D1B42FB29C29E /* MTAiModelKitReflection.h */,
				ABA648F20A2405F35D98AFCCCEBFBD54 /* MTAiNail.h */,
				8EAAA09C6573C666B532F4F76A007A17 /* MTAiNevusDetection.h */,
				C1B202FE029049A92EF54E512581EAD0 /* MTAiNevusDetectionOption.h */,
				C75BEAD0AD3ECDB9ABF2D454B151A938 /* MTAiNevusDetectionResult.h */,
				F2A7E6B88412EA56BC3C451DC9E468A8 /* MTAiOrnament.h */,
				CE412F2F9D858B20716983A68361322F /* MTAiOrnamentAttribute.h */,
				063AD3379EC3AD598F80DD40BD1D28B7 /* MTAiOrnamentOption.h */,
				32C3B8B847AEEC6D7D89AD81A07E7295 /* MTAiOrnamentResult.h */,
				A90ADDBAEDB36A3FBCBA2F4F2973DF26 /* MTAiPortraitInpainting.h */,
				B827F7A1B6742829B34488A2CFE3E202 /* MTAiPortraitInpaintingOption.h */,
				******************************** /* MTAiPortraitInpaintingResult.h */,
				C40A4E215087F265BEDBFF89979A8EE5 /* MTAiRemoveWatermark.h */,
				920F90E42F77C978F939E3F747478F00 /* MTAiRemoveWatermarkOption.h */,
				64211AC72AD7702428F048989E35E855 /* MTAiRemoveWatermarkResult.h */,
				178D086A106785DB66D8067BCDDB5BA5 /* MTAiRTTeethRetouch.h */,
				B01F9C04BEC5BBD3CB8FC894D5B88F74 /* MTAiRTTeethRetouchOption.h */,
				F994B2A428500D02E9ACC464C74E1B1A /* MTAiRTTeethRetouchResult.h */,
				326CD77FB5F28B101660D62B0D9BF76C /* MTAiSceneryBoundaryLine.h */,
				8C80EFFE9240C985B87BD56EAFB28C6B /* MTAiSceneryBoundaryLineOption.h */,
				CEE357DB4BF2CFBFFBCD453693461759 /* MTAiSceneryBoundaryLineResult.h */,
				19D75636C125B161FF172AF05CD8C8A5 /* MTAiSegment.h */,
				1C2D4FC6526B0FCC59FC11235BEF677C /* MTAiSegmentOption.h */,
				79EFA025797728B000F104A57FC16A1D /* MTAiSegmentResult.h */,
				57916654D6891DE974CAF52208935FC4 /* MTAiShoulder.h */,
				19C31FB904A5D41B6A7C941C50CFE3D1 /* MTAiShoulderOption.h */,
				64B87B2DF61FEBE119E1A9695881B928 /* MTAiShoulderResult.h */,
				CC6831BC6DBBAC9EF163F29225885E13 /* MTAiSkin.h */,
				F319068F7C61A737EFE6BEEF1D509313 /* MTAiSkinAR.h */,
				6C188C5BBAE3DE42E1D720EA05CCC0F6 /* MTAiSkinAROption.h */,
				C9EE985AC8056D83612F7641435D8F83 /* MTAiSkinARResult.h */,
				F2292DECF642EF4AD762AFA41330BEC4 /* MTAiSkinAttribute.h */,
				5AF8F058633C517A1FF54E47FAC4A781 /* MTAiSkinBCC.h */,
				49A5D25C0D01C8F4AC63A1966DA71306 /* MTAiSkinBCCOption.h */,
				FF3BD2245DF95EEC0E4B16A97FB6AB75 /* MTAiSkinBCCResult.h */,
				15825EE6BA62F33189390EABC3A194FA /* MTAiSkinMicro.h */,
				33AD9578FA3B2704B5CDAC09E8DA42E1 /* MTAiSkinMicroOption.h */,
				57D558EFE3E37AEAB3C06D4D289DA2CF /* MTAiSkinMicroResult.h */,
				47E0146A1C699A83A56034B4BF94188C /* MTAiSkinOption.h */,
				D7008B42F737C29D76491B07C86305BF /* MTAiSkinResult.h */,
				3A1C149CD1E27755E4FB5840A569EE4C /* MTAiSkinToneMapping.h */,
				4C84506C061BBF608F67C41487F1CBAB /* MTAiSkinToneMappingOption.h */,
				0B5FCC199F24FA4100CED46D22F1CCF8 /* MTAiSkinToneMappingResult.h */,
				F5700B2A3A77A2F4585FBBC3949F08C5 /* MTAiSymExport.h */,
				C488BDE7347CADE214418A7E2C2C3A93 /* MTAiTeethOption.h */,
				626F1C305B50370489286F63E746CE5B /* MTAiTeethResult.h */,
				73B199322A707F7B76D4421FF35B5885 /* MTAiToKidFeature.h */,
				BBABBD09629F464E32673355341B0221 /* MTAiToKidFeatureResult.h */,
				EF9E94ADB8A23A37329A82FB5D08E847 /* MTAiToKidOption.h */,
				F89563697C59253D124A6C61DE621EB6 /* MTAiVideoOptimizer.h */,
				1678A2DD03AB1412D323B612AA697985 /* MTAiVideoOptimizerOption.h */,
				B962BE394E0B7880D1A82F306139A52A /* MTAiVideoOptimizerResult.h */,
				A24D941BE68C8939EC817693A1F260C6 /* MTAiVideoRecognition.h */,
				5732BC06945CC6C30DCA22FF67899B72 /* MTAiVideoRecognitionOption.h */,
				871435DDB09220290E8DD0BB8341931C /* MTAiVideoRecognitionResult.h */,
				DE4EC308E7E1431E504C09DE8F186EA3 /* MTAiVideoStabilization.h */,
				759B37D4FD1442C9C6DE988539707C38 /* MTAiVideoStabilizationOption.h */,
				BB8192C8345812F082D75D185652DEE6 /* MTAiVideoStabilizationResult.h */,
				F4F1AEBA88470134E4DA6B478D294D6C /* MTAiWrinkleDetection.h */,
				0B2B97A931B2272C9FFF823D2BC14952 /* MTAiWrinkleDetectionOption.h */,
				FB263902FADE2F0B84EAD832732789B7 /* MTAiWrinkleDetectionResult.h */,
				BF2F29C87491FBD567933D33E4C41FCD /* MTAnchorGenerationModuleOption.h */,
				73CC16074AFF78B3D3644D7AF8089E19 /* MTAnchorGenerationResult.h */,
				A87C3ED296E5A27B432395F784C365D4 /* MTAnimalModuleOption.h */,
				FB7E3B0D8562784D3025134D1AEF76F0 /* MTAnimalResult.h */,
				217D1290BAFEBFBE20FFF2EC043E7ED9 /* MTApmUtils.h */,
				81CFAD1CB96C07B59C5BB818457EA337 /* MTBodyInOneModuleOption.h */,
				99DEC70867D29B4267582C4AED6905C2 /* MTBodyInOneResult.h */,
				9912FC610C976E5461B69CBDE9066F91 /* MTBodyModuleOption.h */,
				EFBD4718F9093485075BC9C5EB62325C /* MTBodyResult.h */,
				2A8DD038146DC553CB97CB7FCE945ADE /* MTCgStyleModuleOption.h */,
				45CFDFA432F39BF3E4E688D51F311C8C /* MTCgStyleResult.h */,
				9009942F3230913D8DA4E0DBE36E396A /* MTCsketchModuleOption.h */,
				834EA6EC9E6ECB46B143ADB9A9EADE77 /* MTCsketchResult.h */,
				67AE46A5E3FE5D6F9A145804C82A150A /* MTDenseHairModuleOption.h */,
				3036D0B5EF6AE3C85C79B16E3FD78206 /* MTDenseHairResult.h */,
				3F624352DC4C6B9F9998FE5DE4BE1427 /* MTDL3DModuleOption.h */,
				84C0D1B38C05A2EAB7F1AD72F25FFFAA /* MTDL3DResult.h */,
				75BEA0D0B777D31C938A59DCB8B366D5 /* MTDoubleChinFixModuleOption.h */,
				5551D21660348845A2384F5B74035665 /* MTDoubleChinFixResult.h */,
				D2F49A7102742DDC00A73CC20BD866C4 /* MTEveAutoSkinColorModuleOption.h */,
				9650C5F3398FEB6332833894733F179E /* MTEveAutoSkinColorResult.h */,
				CF1ECEE391486D8F1DF197A5AAFE8A53 /* MTEvePreDetectModuleOption.h */,
				F1F213BDAB2A378595794AA10D7F803C /* MTEvePreDetectResult.h */,
				FE1A47668D051ACA09C39F8AD7D11D44 /* MTEveQualityModuleOption.h */,
				7EC4ED89CB3D20EB1182FCF58F47988F /* MTEveQualityResult.h */,
				6979F4CC8A3069671DB36A85BE5C976C /* MTEveSkinModuleOption.h */,
				724470B10A446E2C232754B52C1057EE /* MTEveSkinResult.h */,
				3149C43670A8094936669DD1934A280C /* MTEyelidImageModuleOption.h */,
				F29D294FAACC2307817F3B0E971A3847 /* MTEyelidImageResult.h */,
				DB399E478F7B99279EB59836852E323E /* MTEyelidRealtimeModuleOption.h */,
				42485051DCA6A84BFDF2D052A7BD4CD7 /* MTEyelidRealtimeResult.h */,
				D933A4C398FA422D360C026AAE69EB47 /* MTEyeSegmentModuleOption.h */,
				682E9815C66D408ACA1D18DC321E90EF /* MTEyeSegmentResult.h */,
				754A023B633FA7DA59AC3279CE9209FA /* MTFaceAnalysisXModuleOption.h */,
				F4B7047257A72C793708CCCCBA70F2F0 /* MTFaceAnalysisXResult.h */,
				67604714ACCD307595663392C964481C /* MTFaceBlitModuleOption.h */,
				92E4F327419F771CFA5ECAAA09B3D87D /* MTFaceBlitResult.h */,
				F9B37F42F153EA9D5EB435651D5E7F09 /* MTFaceHDModuleOption.h */,
				F26A1543B9298FF1B6061E3D5B4416CC /* MTFaceHDResult.h */,
				2375E2BEE8BB70AF37ABB777B38FF8FF /* MTFaceModuleOption.h */,
				42C682D99D1B37031853708D51B6AF88 /* MTFaceResult.h */,
				92D7EF0D77E417E28193769B9BFA302F /* MTFaceUtility.h */,
				62540477461C3BA53EB9468D79AA2A5E /* MTFoodModuleOption.h */,
				6E02B401B75BA86BC492B0352A3CD77F /* MTFoodResult.h */,
				15C3D1CDD4319E6C64D36308295DF804 /* MTFoodStyleModuleOption.h */,
				190912AB5F57BA1D558233514E9E1C4E /* MTFoodStyleResult.h */,
				3B3B9F08639325065AFFA00976F3C733 /* MTFrame.h */,
				95A8FAA1852010F25B6292FBF0D3A229 /* MTHairCurlyModuleOption.h */,
				E871CCB546ED4645E65F36375992B00A /* MTHairCurlyResult.h */,
				DD877F08789D7B6037E1E38194999691 /* MTHairDyeModuleOption.h */,
				F7FE9CDCDE0843E05B20E947D97361E1 /* MTHairDyeResult.h */,
				E6CDDD0B8B15CB011B201C5AEB3692E0 /* MTHairFluffyModuleOption.h */,
				665177B79034E8AF5BCD3BB290DF264A /* MTHairFluffyResult.h */,
				802BED11ED27B7D219393B0D4F8313A8 /* MTHairGrouthModuleOption.h */,
				636611703D6177AECC616089B16C50F2 /* MTHairGrouthResult.h */,
				14DDA0D06CDF7A9F6508266788A10B44 /* MTHairModuleOption.h */,
				9D6D0926CFB76C4FC3B3DE9F1785C77B /* MTHairResult.h */,
				84096218E72A2C041C7C09935CF99AEC /* MTHairStraightModuleOption.h */,
				C72EF82F2ACC07CB1CB62CEAFA20411F /* MTHairStraightResult.h */,
				D3ADD832168F42ABE29861918CA25B79 /* MTHandModuleOption.h */,
				BB5F00D044256126F8689290113E6BC9 /* MTHandResult.h */,
				35537B002D9C7EC873DD829BD59A23A7 /* MTHighDofEyelidModuleOption.h */,
				14509E5038F428D72F54B102D44FE43B /* MTHighDofEyelidResult.h */,
				68E20D2ED2761482625D7B5C2FBFA6F0 /* MTHuman3dModuleOption.h */,
				727C8E31A68BCD21D0D26916258EF75E /* MTHuman3dResult.h */,
				6A2ADCEBB47568FD54816164D3D1C25A /* MTImageDetectionModuleOption.h */,
				4A7537FA87775343DE7E141E19601D2F /* MTImageDetectionResult.h */,
				B5D921E97027FB45BDA2500CC1BB620E /* MTImageRecognitionModuleOption.h */,
				97E6DFCFBA11A776D444F15BC73C557B /* MTImageRecognitionResult.h */,
				90BE91979A7730FDF12F69B20FC96831 /* MTInstanceSegmentModuleOption.h */,
				C55CB4B638EDD3F6C4389576E8CEC51E /* MTInstanceSegmentResult.h */,
				8D575EBECC197C2906480C767B9361B1 /* MTKiev3DMakeModuleOption.h */,
				459A913A4AD105197A407419752FA697 /* MTKiev3DMakeResult.h */,
				A4957FB26719B2D9D9D2F9A8A27CA3BC /* MTLandmarkModuleOption.h */,
				18340AC2A6E2402B65EC2C6128850792 /* MTLandmarkResult.h */,
				02E55D26CFE50916985D8E86BAE9CCE5 /* MTMakeupModuleOption.h */,
				B49F0A82229F92F737BA0252C40E812F /* MTMakeupResult.h */,
				FF68C3F6EEBDB0A067EDBE36F515E140 /* MTMaterialTrackingModuleOption.h */,
				DA7889C854A108F5AD2A691349159058 /* MTMaterialTrackingResult.h */,
				08B6DF856CF3A0BBE28C55B83062FE92 /* MTNevusDetectionModuleOption.h */,
				F5651966DA7094A28C9FE7A927E730E2 /* MTNevusDetectionResult.h */,
				2234919422550AE8AEB678F75F818DE1 /* MTNoseBlendModuleOption.h */,
				F525AD3078A80DAC93FA4896F98AFDF0 /* MTNoseBlendResult.h */,
				00285EB3599220B5EB96B612CD04A883 /* MTOrnamentModuleOption.h */,
				C0E8BA95D8CFC9F0B9B7D6C927F1D07E /* MTOrnamentResult.h */,
				585CAC44BD2EC485C15A33CBE40547C3 /* MTPackageName.h */,
				6152983F59F4DD10E13C957F72311C04 /* MTPortraitDetectionModuleOption.h */,
				2265594E2C0EB2307E41BA2FBE3E9E7C /* MTPortraitDetectionResult.h */,
				F1EABA59F43766CBC3057003B7B94E08 /* MTPortraitInpaintingModuleOption.h */,
				0750AA723EEAB61F944EC465BA40968C /* MTPortraitInpaintingResult.h */,
				F6541847678CCFC0DAC0893B68D4489A /* MTRemoveWatermarkModuleOption.h */,
				5D55FFD0F11B032B030E38AB0A90EABE /* MTRemoveWatermarkResult.h */,
				03FBFA5177C02C673250BD42C7EAD426 /* MTRestoreTeethModuleOption.h */,
				5D2C5D0B9F1A00295902DCA41F37F7AA /* MTRestoreTeethResult.h */,
				024CE261211368FA1A7A4CF66323F71D /* MTRestoreTeethUtility.h */,
				548DFBF21279A938AE713E9FA5893DDA /* MTRTTeethRetouchModuleOption.h */,
				032DA0EDCC9EE590222CE1DDB0F52EBA /* MTRTTeethRetouchResult.h */,
				DB042F0C71BD861C0E707F5F2795E178 /* MTSceneryBoundaryLineModuleOption.h */,
				00D30660E334EBC9C10C41ED3812C996 /* MTSceneryBoundaryLineResult.h */,
				CC42B8C42B96BBFFBC1AE7069167A67A /* MTSegmentModuleOption.h */,
				83790908BE03557F48848EACF0099467 /* MTSegmentResult.h */,
				ED7B4FDDAC9948B0D9E068075288B150 /* MTShoulderModuleOption.h */,
				352D1BB4BB54B3CDD4316401F3E45C28 /* MTShoulderResult.h */,
				0F568AFFFF21C005AF6A928423D858FD /* MTSkinARModuleOption.h */,
				C9819CE59454A5A3F22DE7BD8AB9CEED /* MTSkinARResult.h */,
				F4A044B99D25BD5DFA6FBF3595C1B013 /* MTSkinBCCModuleOption.h */,
				9D6DBD5057495139CB3707D8DCDDF055 /* MTSkinBCCResult.h */,
				A62C0C6993C00974ADCEA65220DC1CCD /* MTSkinMicroModuleOption.h */,
				1020031E4B9DF801A4CF2CAB94DA0C44 /* MTSkinMicroResult.h */,
				422DDA698093A8550F603D7DA217238C /* MTSkinModuleOption.h */,
				******************************** /* MTSkinResult.h */,
				C4177B87EB636B476C4689164FBCB9A8 /* MTSkinToneMappingModuleOption.h */,
				80F77E02068C26DE80A601702EBD09B6 /* MTSkinToneMappingResult.h */,
				7219AAE08973446ED9CD6C6344D79223 /* MTSkinV1238.h */,
				6BE52F8495DE734CD54C351D59560EBD /* MTSmileModuleOption.h */,
				6A261181EEE7DACF251DA4E30E8B34D9 /* MTSmileResult.h */,
				09ABF3B8119FD3F4F00135C751E96EE2 /* mtstitch_caption.h */,
				6B89E62F225AC8F94E99B74CF7C972F2 /* mtstitch_del_repeat.h */,
				E670B086F90F33A33ADC575BC2AC1C3F /* mtstitch_MTAiEngineDefine.h */,
				9A4ACC99E1F380BB4D880CE3376629CD /* MTSubAIBeauty.h */,
				A4BE01112630DB4AE08001E2719C3C5F /* MTSubAIBeauty.h */,
				BC174B964F2DFE4A88C359637A67C338 /* MTSubAIEngineAnalysis.h */,
				02286C688478F6BCB57C30210EB3D117 /* MTSubAIEngineAnalysis.h */,
				8E2D894B028512C0F75CF321F536DEF1 /* MTSubAiModelKit.h */,
				3B160BA3C7634CEA52937C152FB3F654 /* MTSubAiPhotoTimelapseAdaptor.h */,
				3C7283F93F12ABD00082645B6FA964BC /* MTSubAiVideoRecognitionAdaptor.h */,
				31FAE423D5771AB8AFCFD8DE1E1D2D3E /* MTSubBlendEffect.h */,
				4AF5F960AAD70E7604E28987E0016AAE /* MTSubColorACUtilGL.h */,
				359B03AF4BB3A05932B9748FD4C65BE7 /* MTSubColorACUtilGL.h */,
				72EF7A3639A2A7CC792BAD260F15D20F /* MTSubColorChecker.h */,
				ECF278ABA149D36EBE61BC5B5E67063C /* MTSubColorToning.h */,
				4488060A266116B419C842ABEA1CB92C /* MTSubColorToning.h */,
				42478C16B94B5DE3D505402CA0017E4B /* MTSubColorToningEW.h */,
				E254DCFEA0DDBB3F03E34D7DB2C20D88 /* MTSubColorToningEW.h */,
				0F8FF42D2BDED09B3E146BEE4AB074BF /* MTSubColorToningType.h */,
				D5C1A426FF0086CF194304E72D092782 /* MTSubColorToningType.h */,
				D52DD4B023960880F97FE4F409E35298 /* MTSubColorTransfer.h */,
				C4FB1D2BCC86CE7185F15C03FB73834E /* MTSubDLAlgorithmDefined.h */,
				98E600BC095C2691ED65F07200E66887 /* MTSubDLAlgorithmUtil.h */,
				CEF691D0D91CD240B94B7256C7BB3943 /* MTSubDLAlgorithmUtilCoreML.h */,
				8737203005DC45C5936EF06EFB5C9196 /* MTSubDLAlgorithmUtilGL.h */,
				FFF048720C72CA57282384F6422C5D14 /* MTSubDLBeautyBase.h */,
				9E1549033AC14F1CEEA28AA00D325DBD /* MTSubDLTextureInpainting.h */,
				38688426D871410D0D5EB59883CFC088 /* MTSubDLTextureInpainting.h */,
				32EE5715EB5CDF739115C245E828425C /* MTSubDoubleChinFix.h */,
				3CC5C0B54A36F01E2AC87CBAF707EE81 /* MTSubEyeLifting.h */,
				84570D8CCE0E69A15126B9E6D6BF9022 /* MTSubEyeLifting.h */,
				422193BE3220F1F4C2F3D771C73FED87 /* MTSubFaceAnalysis.h */,
				014773D80D4A24CED2CE2DCC89418028 /* MTSubFaceAnalysis.h */,
				FB619904A5A836D98DC08FD8CCDB7312 /* MTSubFaceRefinerEngine.h */,
				3EF5A5F2AA9C5F1D5085DC90A54B8F4D /* MTSubFaceRefinerEngine.h */,
				318E7FCBE64F5C04EDC5CDD26B544D6C /* MTSubFrameSelect.h */,
				75B561B2E1BD709FDA0BBF1FDAF4019E /* MTSubFrameSelect.h */,
				563C469D831CF988ABDAA4094ECF125D /* MTSubGazeManipulation.h */,
				A9176B2B961BCD888C9365606A781C0D /* MTSubHairTransfer.h */,
				C5BA8BF25783DA8601A27FF64F6DB374 /* MTSubHairTransferWrapper.h */,
				C78043E3D6AF2046722E735E43B5ECE7 /* MTSubImageEnhancer.h */,
				46742ACF9E9F416549F386C9568111E1 /* MTSubInceptionBeautyUtil.h */,
				A4CE480598B4BE551460793C30463C50 /* MTSubInceptionBeautyUtilCoreML.h */,
				86447333FA37BF01E8D352422810A1C4 /* MTSubInceptionBeautyUtilGL.h */,
				2F8489773D7E3CBCA6DE4A9D4877487B /* MTSubIntelligentFusion.h */,
				97678617EB9DEB261179BC8BE14D54C0 /* MTSubLowPoly.h */,
				F232A0CCBD9985CC0E6E7441BA5A7748 /* MTSubMaskFeather.h */,
				FD0D308618CFE3D9950D9E95D27092D5 /* MTSubMelt.h */,
				474760CC7BA1C27DD48F25C87852E2E8 /* MTSubOpenEye.h */,
				498D1059B5B35D7E06DB319B43819856 /* MTSubOpenEye.h */,
				53B2982DCA16034C036D0AFC5ED41243 /* MTSubPhotoTimelapse.h */,
				C24AA8787423E815C56BE4DFC6DA09BD /* MTSubRTDenseHair.h */,
				6AAF683C445D331CC003E2DB9FE2FD43 /* MTSubSegment.h */,
				9D9D310D9B76C88B884C1780548D1CDC /* MTSubTempIntelligentFusion.h */,
				87E6171F30F60C9B03D0636A36A0A5C2 /* MTSubVideoOptimize.h */,
				C4FA718EC852843967B34054752134AA /* MTSubVideoOptimize.h */,
				E093DC67182FBCC48D10280AC617F8D1 /* MTSubVideoRecognition.h */,
				84E0D547B19287C9895194FEF7FB703D /* MTTeethModuleOption.h */,
				0D377B69EB7D10649236E52A8BE1214E /* MTTeethResult.h */,
				F72DB25B9F434375DB7D199C4874E813 /* MTToKidModuleOption.h */,
				434EDD7D9783EA82DA26AC5FF8D5D256 /* MTToKidResult.h */,
				90B92BF7928A7602FC7A82CC56CECF02 /* MTValueMap.h */,
				058D2F10F87B59747BF6D7E716A42C66 /* MTVector.h */,
				8E28E3E3A758DBD27C00C567C946D808 /* MTVideoOptimizerModuleOption.h */,
				49366AC1E35D61CCB1B8B7DBEFB83074 /* MTVideoOptimizerResult.h */,
				28D2DECFBE03B74485CE59E4EBFB70E9 /* MTVideoRecognitionModuleOption.h */,
				CEEC0C2F4D47D0405D8669238E5765A8 /* MTVideoRecognitionResult.h */,
				CB0C0F2255366398F3BEF4E2DE8BD585 /* MTVideoStabilizationModuleOption.h */,
				11C7272C9A7CB9F8E5280CC0EA87DEA6 /* MTVideoStabilizationResult.h */,
				DB1547C2617026F9F99489276228C1F2 /* MTWrinkleDetectionModuleOption.h */,
				30D57168482462E36BFB09C70D1ECC83 /* MTWrinkleDetectionResult.h */,
				EECD7EC3758F76B49E6178F0397DB3CD /* Frameworks */,
			);
			name = framework;
			sourceTree = "<group>";
		};
		E4801F62A6B08CD9B5410329F1A18FDE /* iOS */ = {
			isa = PBXGroup;
			children = (
				384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		EECD7EC3758F76B49E6178F0397DB3CD /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				76EE384BFEB9D955C6865D246F85EEE2 /* MTAiInterface.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		EFBFF613671EC86C7E740FD0EEAE972D /* Support Files */ = {
			isa = PBXGroup;
			children = (
				62E81994604749F98DA10745F5B61736 /* mtlabrecord.debug.xcconfig */,
				3D51315A901AFD4473A402D0A3D8F4D1 /* mtlabrecord.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/mtlabrecord";
			sourceTree = "<group>";
		};
		FE6575A11DBF84F569063ED7C04EF6A8 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				AE0E9EA950D7430E90C174FB13F793A3 /* libmtaiinterface.debug.xcconfig */,
				4DAB926AAE845A85D7F03B82E90CAA01 /* libmtaiinterface.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/libmtaiinterface";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		14B4808C66128A57BF4EFF1475F05574 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B9E48DFF50D0C3CEFD641F391588334 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		54DF43FB9D47F985D41CFA7ADCBECFCF /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8AFD2354D62DC9C6257412C298FC0CA2 /* Pods-MTAISDK-DemoTests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B1500FE995B9224E0AF0B42CE93C03B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				57C4F6EFB30DDD14E960AC2D6B34F904 /* SnapKit-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A2763B61064DAA61346DB7F565F4442F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				85D325033EA4387D788C9CEB3074B59C /* Pods-MTAISDK-Demo-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		09A4CA6090EF04F652EAB0074A1330E6 /* Pods-MTAISDK-Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5959CEC6753BB527B5A5811BFABAFF5E /* Build configuration list for PBXNativeTarget "Pods-MTAISDK-Demo" */;
			buildPhases = (
				A2763B61064DAA61346DB7F565F4442F /* Headers */,
				02BA1CF9212C94942D279CDF90DBD63C /* Sources */,
				83F4283922547A7E1AF31EF6297CF7F2 /* Frameworks */,
				1240DB0112A7B60C94E189EF73C589DC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				BFAC8862EB036BB3478D5F0ACB847415 /* PBXTargetDependency */,
				68D94E150054F406CF3216456049D176 /* PBXTargetDependency */,
				9614045A01A8E7BD3155FFBE5C034A7B /* PBXTargetDependency */,
				4D5C7FB5214C0910F8FABBCD5DE0F793 /* PBXTargetDependency */,
				27C68BDBA4D0CF263CE8AB6101C448B4 /* PBXTargetDependency */,
			);
			name = "Pods-MTAISDK-Demo";
			productName = Pods_MTAISDK_Demo;
			productReference = 07968C4F4D0AB4C6A603E6A2BE7835A1 /* Pods-MTAISDK-Demo */;
			productType = "com.apple.product-type.framework";
		};
		19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 80CE967CAA1D35721519F892BAF7A19B /* Build configuration list for PBXNativeTarget "SnapKit" */;
			buildPhases = (
				5B1500FE995B9224E0AF0B42CE93C03B /* Headers */,
				F7AC6792C89443C7B212A06E810BAB97 /* Sources */,
				33428AC36668E3ED52DB70316F843FB8 /* Frameworks */,
				1DEDF411E550D85A1218E1655456A9CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DA19ABEDC05979ECB66E5D37D56695DF /* PBXTargetDependency */,
			);
			name = SnapKit;
			productName = SnapKit;
			productReference = 979486118B3E90C08386079D57962701 /* SnapKit */;
			productType = "com.apple.product-type.framework";
		};
		415C714A0D4B61CD74D351092E8C635D /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 33C26EA035D75B2A65689C67423BF165 /* Build configuration list for PBXNativeTarget "Pods-MTAISDK-Demo-MTAISDK-DemoUITests" */;
			buildPhases = (
				14B4808C66128A57BF4EFF1475F05574 /* Headers */,
				38469FA5CC1495DBB9C271D0CF70EF07 /* Sources */,
				2865E5A4F984F993E6F6AD24411D1D1F /* Frameworks */,
				661156ABC108508D06499CD02D5822A2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9B19DF66E4CB23AD2AC404A07C4995A2 /* PBXTargetDependency */,
				ABBDF68F5DFDF89CCE33BB61128E3D3B /* PBXTargetDependency */,
				1065C0BFA95FBF1A14836D9309550599 /* PBXTargetDependency */,
				41275D7BB2EDA7CDE4577F97EC9FA90F /* PBXTargetDependency */,
				8D3005F5FC9BD43FC424C47863A45DA4 /* PBXTargetDependency */,
			);
			name = "Pods-MTAISDK-Demo-MTAISDK-DemoUITests";
			productName = Pods_MTAISDK_Demo_MTAISDK_DemoUITests;
			productReference = A07B6C17DD730ADC236315AF7A127516 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests */;
			productType = "com.apple.product-type.framework";
		};
		55B08DB9C31ABAAAC610A06FE908E7C4 /* Pods-MTAISDK-DemoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3440EF73B840E593F48E1AD0892F0A03 /* Build configuration list for PBXNativeTarget "Pods-MTAISDK-DemoTests" */;
			buildPhases = (
				54DF43FB9D47F985D41CFA7ADCBECFCF /* Headers */,
				14FC52CD4E8C95BD1591C9059FA09FC3 /* Sources */,
				D2D3997219A6766AAA57BA94AEDE1BF7 /* Frameworks */,
				95F8018713891F93EB9861BFEE6E7246 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				61708CA61CBA51E26FC33FDB7EC37FA2 /* PBXTargetDependency */,
			);
			name = "Pods-MTAISDK-DemoTests";
			productName = Pods_MTAISDK_DemoTests;
			productReference = 2E0C2F6B52B37E811FA40ABD102AA294 /* Pods-MTAISDK-DemoTests */;
			productType = "com.apple.product-type.framework";
		};
		8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C32629933A9948C168CF311983491243 /* Build configuration list for PBXNativeTarget "SnapKit-SnapKit_Privacy" */;
			buildPhases = (
				4416AE8E40632D6B222576D18D7D746A /* Sources */,
				E075A9E9B1E24AD178BB765D865452CE /* Frameworks */,
				11226FFD146009842E7E8D75211AEF03 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SnapKit-SnapKit_Privacy";
			productName = SnapKit_Privacy;
			productReference = B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 99911056C63D60323FCCF1DA08802E95 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4B6589A87574893065F6CEA7FD28C290 /* libmanis */,
				0A4E93A920C41658659C81ADE6AD435B /* libmtaiinterface */,
				FC329C2B3FCA1F25298C3AFF669647F5 /* mtlabrecord */,
				731483C741D3522F4DC8242EC19A6C3C /* mvgif */,
				09A4CA6090EF04F652EAB0074A1330E6 /* Pods-MTAISDK-Demo */,
				415C714A0D4B61CD74D351092E8C635D /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests */,
				55B08DB9C31ABAAAC610A06FE908E7C4 /* Pods-MTAISDK-DemoTests */,
				19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */,
				8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		11226FFD146009842E7E8D75211AEF03 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6709186EC6C112BEAEC6C2B2FDC3CA70 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1240DB0112A7B60C94E189EF73C589DC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1DEDF411E550D85A1218E1655456A9CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9E0045B41BFE697DB4ADE151228024D2 /* SnapKit-SnapKit_Privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		661156ABC108508D06499CD02D5822A2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		95F8018713891F93EB9861BFEE6E7246 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		02BA1CF9212C94942D279CDF90DBD63C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				140B85DB7B9728CDADE24B89FBF13046 /* Pods-MTAISDK-Demo-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14FC52CD4E8C95BD1591C9059FA09FC3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EE2FE29CFC992EB13EC00CE57C9796F0 /* Pods-MTAISDK-DemoTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		38469FA5CC1495DBB9C271D0CF70EF07 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D33ABF05CD58F7AFDD595E2F09465D4 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4416AE8E40632D6B222576D18D7D746A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7AC6792C89443C7B212A06E810BAB97 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6E39129FC8643A70C276801FEF4C280D /* Constraint.swift in Sources */,
				0CA7A132ABE7018DE9295456732F38BB /* ConstraintAttributes.swift in Sources */,
				F9EBA65892D78A31C068D727D84BCB88 /* ConstraintConfig.swift in Sources */,
				CE593943A9E7CF83822CF60304BCAD43 /* ConstraintConstantTarget.swift in Sources */,
				7AF516B98D45391B909D507D0244104C /* ConstraintDescription.swift in Sources */,
				1194E62AA3F6F506799B1A43B16942B5 /* ConstraintDirectionalInsets.swift in Sources */,
				E37671A03B4C17A1CF3766A6125833BB /* ConstraintDirectionalInsetTarget.swift in Sources */,
				868A9F524A7985BDA1EA124D9BF4CA63 /* ConstraintDSL.swift in Sources */,
				AF760C78F1C7E11BF7CB9E9B29903530 /* ConstraintInsets.swift in Sources */,
				2B2EB369550CE92CEEFCBFD3D32B8A3F /* ConstraintInsetTarget.swift in Sources */,
				5922A6A0AE7152CF436356B3556F1835 /* ConstraintItem.swift in Sources */,
				59F34874DA4ABB2F5C4E09EA6865936B /* ConstraintLayoutGuide.swift in Sources */,
				E3D779DEE753C0B0D33BA8E73A980265 /* ConstraintLayoutGuide+Extensions.swift in Sources */,
				3D3B646B4988314275B40E97BEB16C7F /* ConstraintLayoutGuideDSL.swift in Sources */,
				B0875E3AB8718E7DFE5C53497C02A15E /* ConstraintLayoutSupport.swift in Sources */,
				064D909CD827405E8DCC309DB1B7775A /* ConstraintLayoutSupportDSL.swift in Sources */,
				4F4DEB687C0E4834A5B291DEE0651D6A /* ConstraintMaker.swift in Sources */,
				C14F10B663FE2898EACAB90C202B3F50 /* ConstraintMakerEditable.swift in Sources */,
				D4218DA55B2BA45937589200CC0DF1FB /* ConstraintMakerExtendable.swift in Sources */,
				B903049E7C1BED7918DAB208754107C7 /* ConstraintMakerFinalizable.swift in Sources */,
				AABEF13464BA7F4621BD94736C1D057C /* ConstraintMakerPrioritizable.swift in Sources */,
				BDA5C7CC91E86448237CF40954FAC5AF /* ConstraintMakerRelatable.swift in Sources */,
				8BABA32F7B94A25D8E9208C0A8D90B2E /* ConstraintMakerRelatable+Extensions.swift in Sources */,
				883EDEE1C699497CF2A77C3B8A32A790 /* ConstraintMultiplierTarget.swift in Sources */,
				3577F172FA68CBAE47CFEE6FE25C5404 /* ConstraintOffsetTarget.swift in Sources */,
				09E1F569A93FAD4B9149E30B9301F44A /* ConstraintPriority.swift in Sources */,
				DBA4803F4765E1650B8C6841157F5D73 /* ConstraintPriorityTarget.swift in Sources */,
				C07CB3E9A4D1BF00F841E4285629A2B2 /* ConstraintRelatableTarget.swift in Sources */,
				ECC5C2ADC2682F9171FEA22AF10DCE53 /* ConstraintRelation.swift in Sources */,
				86CAB01D950C8BC35EDE0BDC01A2500B /* ConstraintView.swift in Sources */,
				0DE5DB9C6227B3416778D8417DD95EA9 /* ConstraintView+Extensions.swift in Sources */,
				7D42390CDB4FA147504B03DA2A174A0C /* ConstraintViewDSL.swift in Sources */,
				AE224EDB6D044C0FE86B086E950FC2F9 /* Debugging.swift in Sources */,
				BF1AE4D97E813B95C43EA4A298B973D1 /* LayoutConstraint.swift in Sources */,
				C6F45595676957ADBEC18EB3F23EAEC4 /* LayoutConstraintItem.swift in Sources */,
				BA2FB695DEB0D179253EEB8DFCE3578B /* SnapKit-dummy.m in Sources */,
				C6A4302ACE006C4E2CDD481287E2916B /* Typealiases.swift in Sources */,
				F6F33E8B268F3D41075374D95B8088DC /* UILayoutSupport+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1065C0BFA95FBF1A14836D9309550599 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libmtaiinterface;
			target = 0A4E93A920C41658659C81ADE6AD435B /* libmtaiinterface */;
			targetProxy = 076D8B95EE711C8E591BEE9AE24194CA /* PBXContainerItemProxy */;
		};
		27C68BDBA4D0CF263CE8AB6101C448B4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = mvgif;
			target = 731483C741D3522F4DC8242EC19A6C3C /* mvgif */;
			targetProxy = 88712F38BB5311B03DA646813FE1AA89 /* PBXContainerItemProxy */;
		};
		41275D7BB2EDA7CDE4577F97EC9FA90F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = mtlabrecord;
			target = FC329C2B3FCA1F25298C3AFF669647F5 /* mtlabrecord */;
			targetProxy = 01286C1675C76CDBF362F0FCBF8B610D /* PBXContainerItemProxy */;
		};
		4740C4CD6FDEDA027FB5915F6B3CBB9C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = mtlabrecord;
			target = FC329C2B3FCA1F25298C3AFF669647F5 /* mtlabrecord */;
			targetProxy = 694CD45162556CB918CA15B0A00DF92C /* PBXContainerItemProxy */;
		};
		4D5C7FB5214C0910F8FABBCD5DE0F793 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = mtlabrecord;
			target = FC329C2B3FCA1F25298C3AFF669647F5 /* mtlabrecord */;
			targetProxy = 848BFCBCF54D90408DE4D3EBF4A451AA /* PBXContainerItemProxy */;
		};
		61708CA61CBA51E26FC33FDB7EC37FA2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-MTAISDK-Demo";
			target = 09A4CA6090EF04F652EAB0074A1330E6 /* Pods-MTAISDK-Demo */;
			targetProxy = 4F4B621A1D5B9038C3BFEEF8405659D8 /* PBXContainerItemProxy */;
		};
		68D94E150054F406CF3216456049D176 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libmanis;
			target = 4B6589A87574893065F6CEA7FD28C290 /* libmanis */;
			targetProxy = F57C4BEC00E9C0622AA8DCB6A961590B /* PBXContainerItemProxy */;
		};
		8D3005F5FC9BD43FC424C47863A45DA4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = mvgif;
			target = 731483C741D3522F4DC8242EC19A6C3C /* mvgif */;
			targetProxy = 21AE02CACC50BC427AB6A41499C842FA /* PBXContainerItemProxy */;
		};
		9614045A01A8E7BD3155FFBE5C034A7B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libmtaiinterface;
			target = 0A4E93A920C41658659C81ADE6AD435B /* libmtaiinterface */;
			targetProxy = 651CFBE09F73F2446C448A16CDD570AB /* PBXContainerItemProxy */;
		};
		9B19DF66E4CB23AD2AC404A07C4995A2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SnapKit;
			target = 19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */;
			targetProxy = 7597D4D973970FFA89E5B677E9BA8E9F /* PBXContainerItemProxy */;
		};
		ABBDF68F5DFDF89CCE33BB61128E3D3B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libmanis;
			target = 4B6589A87574893065F6CEA7FD28C290 /* libmanis */;
			targetProxy = 09069672CA29661511849425278080FC /* PBXContainerItemProxy */;
		};
		BFAC8862EB036BB3478D5F0ACB847415 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SnapKit;
			target = 19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */;
			targetProxy = 2EC6993B1501E6DC61AB19E0079C2407 /* PBXContainerItemProxy */;
		};
		DA19ABEDC05979ECB66E5D37D56695DF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SnapKit-SnapKit_Privacy";
			target = 8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */;
			targetProxy = E3521A03BBC4742B91E5CB3A6A05359F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0BA22FEB7B15F5B2E728B3533A9CA52B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71014FBE0B12AD850EB3AAFAB95B4C9E /* libmanis.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		0BD26647DB569F32E0254505C123B8CE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A9F64031C165E652ED4AFCBF751809E2 /* libmanis.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		18FA012DA6165EBB1B4AD6359ACA0DE4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9F1B2DEFDBEDB361FF31ABC8DE2FF038 /* mvgif.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1B257AFCDED3645D5C2EAB4367094D81 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8386B1D734AC8907DE7478743E63E290 /* SnapKit.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SnapKit";
				IBSC_MODULE = SnapKit;
				INFOPLIST_FILE = "Target Support Files/SnapKit/ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = SnapKit_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		2B9E26EAE2CD392AD762421F663075A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2DF6B4EF3AEC85460B1B9B24A33B7262 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E0A2F41E7D9E1E84D200C33DBB6EDAAA /* SnapKit.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SnapKit/SnapKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SnapKit/SnapKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SnapKit/SnapKit.modulemap";
				PRODUCT_MODULE_NAME = SnapKit;
				PRODUCT_NAME = SnapKit;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		309C560CA5DB7B21068A16AFB15AC521 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8386B1D734AC8907DE7478743E63E290 /* SnapKit.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SnapKit/SnapKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SnapKit/SnapKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SnapKit/SnapKit.modulemap";
				PRODUCT_MODULE_NAME = SnapKit;
				PRODUCT_NAME = SnapKit;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		3247AB8658DAD36A82D61EDE980A2718 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E0A2F41E7D9E1E84D200C33DBB6EDAAA /* SnapKit.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SnapKit";
				IBSC_MODULE = SnapKit;
				INFOPLIST_FILE = "Target Support Files/SnapKit/ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = SnapKit_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		4721E670B82AFA3795ECCE6DEAF88793 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2A75056332507A3D28753B74BED70748 /* Pods-MTAISDK-Demo.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		635A0E6F19E77C0ACF46BECD8188701D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5A6C36ADEE6025C328CC6B0DC07E7A43 /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		67796B618CDCFA33A3789A458C9E1230 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 98E40FDBCF6F4761B703F642C0BE0516 /* Pods-MTAISDK-DemoTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-MTAISDK-DemoTests/Pods-MTAISDK-DemoTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-MTAISDK-DemoTests/Pods-MTAISDK-DemoTests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		6AFA8E20CD73D5D0D7033080C5C1FE17 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7E4FF76C4C4C837F3231717070F6214D /* Pods-MTAISDK-Demo-MTAISDK-DemoUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-MTAISDK-Demo-MTAISDK-DemoUITests/Pods-MTAISDK-Demo-MTAISDK-DemoUITests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		9262FE594715E60168E3B084A41BD29B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AE0E9EA950D7430E90C174FB13F793A3 /* libmtaiinterface.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		BA5410C62562A77717E2A233B316B508 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4DAB926AAE845A85D7F03B82E90CAA01 /* libmtaiinterface.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		BFC0C3A0E5F4B0D2F9C4ED429BD66A15 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DDFF2291783F286328990604D2018E00 /* mvgif.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CA6A531454793AF0287E6BDDAE5627F6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3D51315A901AFD4473A402D0A3D8F4D1 /* mtlabrecord.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DD0B2117A211158DD40903C1F2C71793 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 62E81994604749F98DA10745F5B61736 /* mtlabrecord.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E195E1ACCE0C8DE5F89CB776BEF0048D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4BFE5C5116E5EFF071F73ADE6A3A63CA /* Pods-MTAISDK-DemoTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-MTAISDK-DemoTests/Pods-MTAISDK-DemoTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-MTAISDK-DemoTests/Pods-MTAISDK-DemoTests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		E3D4CB1F6B8386FA39D1E8C1C43C7283 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 894D941CB569EF6C59BCADC50D34EE2A /* Pods-MTAISDK-Demo.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-MTAISDK-Demo/Pods-MTAISDK-Demo.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		33C26EA035D75B2A65689C67423BF165 /* Build configuration list for PBXNativeTarget "Pods-MTAISDK-Demo-MTAISDK-DemoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				635A0E6F19E77C0ACF46BECD8188701D /* Debug */,
				6AFA8E20CD73D5D0D7033080C5C1FE17 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3440EF73B840E593F48E1AD0892F0A03 /* Build configuration list for PBXNativeTarget "Pods-MTAISDK-DemoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				67796B618CDCFA33A3789A458C9E1230 /* Debug */,
				E195E1ACCE0C8DE5F89CB776BEF0048D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B9E26EAE2CD392AD762421F663075A1 /* Debug */,
				63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5959CEC6753BB527B5A5811BFABAFF5E /* Build configuration list for PBXNativeTarget "Pods-MTAISDK-Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E3D4CB1F6B8386FA39D1E8C1C43C7283 /* Debug */,
				4721E670B82AFA3795ECCE6DEAF88793 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5E855EDBF0C2C3FC151E86F2C4654011 /* Build configuration list for PBXAggregateTarget "libmtaiinterface" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9262FE594715E60168E3B084A41BD29B /* Debug */,
				BA5410C62562A77717E2A233B316B508 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7EF458808BD9FFADFFCB3C8B8EECF084 /* Build configuration list for PBXAggregateTarget "mvgif" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18FA012DA6165EBB1B4AD6359ACA0DE4 /* Debug */,
				BFC0C3A0E5F4B0D2F9C4ED429BD66A15 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		80CE967CAA1D35721519F892BAF7A19B /* Build configuration list for PBXNativeTarget "SnapKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				309C560CA5DB7B21068A16AFB15AC521 /* Debug */,
				2DF6B4EF3AEC85460B1B9B24A33B7262 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C32629933A9948C168CF311983491243 /* Build configuration list for PBXNativeTarget "SnapKit-SnapKit_Privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B257AFCDED3645D5C2EAB4367094D81 /* Debug */,
				3247AB8658DAD36A82D61EDE980A2718 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C4BB81FAD17BADAA0C9A723B98751F2B /* Build configuration list for PBXAggregateTarget "mtlabrecord" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DD0B2117A211158DD40903C1F2C71793 /* Debug */,
				CA6A531454793AF0287E6BDDAE5627F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E2220B36984985DDDCADD2E186846FFE /* Build configuration list for PBXAggregateTarget "libmanis" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0BA22FEB7B15F5B2E728B3533A9CA52B /* Debug */,
				0BD26647DB569F32E0254505C123B8CE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
