/*
 * @Descripttion: 
 * @version: 
 * @Author: huangyq
 * @Date: 2022-02-14 15:26:43
 * @LastEditors: huangyq
 * @LastEditTime: 2022-04-07 10:45:52
 */
#ifndef MANISENGINE_SCRIPT_H_
#define MANISENGINE_SCRIPT_H_

#include <iostream>
#include <string>
#include "ref.h"

namespace manisEngine
{

class ScriptController;

class MANIS_EXPORT Script : public Ref
{
    friend class ScriptController;

public:

    enum Scope
    {
        GLOBAL,
        PROTECTED
    };

    const char* getPath() const;

    Scope getScope() const;

    bool reload();

private:

    Script(ScriptController *sc);
    Script(const Script& copy);
    ~Script();

    std::string _tag;
    bool _tag_is_path = false;
    Scope _scope;
    int _env;
    ScriptController *_sc;
};

}

#endif
