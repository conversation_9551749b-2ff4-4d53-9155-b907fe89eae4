#ifndef __ASYNC_INITIALIZER_HPP__
#define __ASYNC_INITIALIZER_HPP__
#include "manis_def.hpp"

namespace manis {

/**
 * @description: create a local device program cache and stash it into local path for saving init time.
 */
class MANIS_EXPORT DeviceProgramCache {
public:
    enum StatusCode {
        NOT_STARTED,
        RUNNING,
        INTERRUPTED,
        COMPLETED,
    };

public:
    static DeviceProgramCache* Get(DeviceType device);
    virtual bool AsyncInit(const char* cache_path, const char* cache_config = nullptr) = 0;
    virtual StatusCode GetStatus() = 0;
    virtual StatusCode Wait() = 0;
    virtual void Cancel() = 0;

public:
    virtual size_t GetTotalCacheCount() = 0;
    virtual size_t GetRequiredCacheCount() = 0;
    virtual size_t GetSuccessCacheCount() = 0;
};

} // namespace manis

#endif // __ASYNC_INITIALIZER_HPP__
