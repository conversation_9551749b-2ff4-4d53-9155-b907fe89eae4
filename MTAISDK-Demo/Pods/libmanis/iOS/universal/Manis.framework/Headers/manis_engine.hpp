/*
 * @Descripttion: 
 * @version: 
 * @Author: huangyq
 * @Date: 2021-10-19 15:57:41
 * @LastEditors: huangyq
 * @LastEditTime: 2022-02-18 13:48:03
 */
#ifndef ManisEngine_hpp
#define ManisEngine_hpp

#include <iostream>
#include <map>

// manis
#include "net.hpp"
#include "executor.hpp"
#include "tensor.hpp"
#include "tensor_util.hpp"

namespace manisEngine
{

class MANIS_EXPORT ManisEngine
{
public:
    ManisEngine();
    ~ManisEngine();
    void CaptureNetOwner(manis::Net *net){_net = net;}
    void CaptureExecutorOwner(manis::Executor *executor){_executor = executor;}
    void CaptureInputTensorOwner(int index, manis::Tensor *tensor){_input_tensor_map[index] = tensor;}
    void CaptureOutputTensorOwner(int index, manis::Tensor *tensor){_output_tensor_map[index] = tensor;}

    manis::Net* GetNet(){return _net;}
    manis::Executor* GetExecutor(){return _executor;}

public:
    manis::Net *_net;
    manis::Executor *_executor;

    std::map<int, manis::Tensor*> _input_tensor_map;
    std::map<int, manis::Tensor*> _output_tensor_map;
    std::map<std::string, manis::Tensor*> _input_tensor_name_map;
    std::map<std::string, manis::Tensor*> _output_tensor_name_map;
};

}

#endif /* ManisEngine_hpp */
