/*
 * @Descripttion: 
 * @version: 
 * @Author: huangyq
 * @Date: 2021-09-29 18:57:42
 * @LastEditors: huangyq
 * @LastEditTime: 2021-11-05 11:22:51
 */
#include "script_controller.h"

namespace manisEngine
{

//meitu T is obj
template<typename T> bool ScriptController::executeFunctionAndCheckObj(const char* func, const char* class1name, const char* args, T** out, ...)
{
    va_list list;
    va_start(list, out);
    bool success = executeFunctionAndCheckObj<T>(nullptr, func, class1name, args, out, &list);
    va_end(list);
    return success;
}

//meitu T is obj
template<typename T> bool ScriptController::executeFunctionAndCheckObj(Script* script, const char* func, const char* class1name, const char* args, T** out, va_list* list)
{
    int top = getTop();
    bool success = executeFunctionHelper(1, func, args, list, script);
    if (out && success) {
        *out = (T*)getObjectPointerFromStack(class1name);
    } 
    setTop(top);
    return success;
}

//meitu T is obj point
template<typename T> bool ScriptController::executeFunction(const char* func, T* out)
{
    return executeFunction<T>((Script*)NULL, func, out);
}

//meitu T is obj point
template<typename T> bool ScriptController::executeFunction(Script* script, const char* func, T* out)
{
    // Userdata / object type expected - all other return types have template specializations.
    // Non-userdata types will return NULL.
    int top = getTop();
    bool success = executeFunctionHelper(1, func, NULL, NULL, script);
    if (out && success)
        *out = (T)toUserData();
    setTop(top);
    return success;
}

//meitu T is obj point
template<typename T> bool ScriptController::executeFunction(const char* func, const char* args, T* out, ...)
{
    va_list list;
    va_start(list, out);
    bool success = executeFunction<T>((Script*)NULL, func, args, out, list);
    va_end(list);
    return success;
}

//meitu T is obj point
template<typename T> bool ScriptController::executeFunction(Script* script, const char* func, const char* args, T* out, ...)
{
    va_list list;
    va_start(list, out);
    bool success = executeFunction<T>(script, func, args, out, list);
    va_end(list);
    return success;
}

//meitu T is obj point
template<typename T> bool ScriptController::executeFunction(const char* func, const char* args, T* out, va_list* list)
{
    // Userdata / object type expected - all other return types have template specializations.
    // Non-userdata types will return NULL.
    int top = getTop();
    bool success = executeFunctionHelper(1, func, args, list, (Script*)NULL);
    if (out && success)
        *out = (T)toUserData();
    setTop(top);
    return success;
}

//meitu T is obj point
template<typename T> bool ScriptController::executeFunction(Script* script, const char* func, const char* args, T* out, va_list* list)
{
    // Userdata / object type expected - all other return types have template specializations.
    // Non-userdata types will return NULL.
    int top = getTop();
    bool success = executeFunctionHelper(1, func, args, list, script);
    if (out && success)
        *out = (T)toUserData();
    setTop(top);
    return success;
}

}
