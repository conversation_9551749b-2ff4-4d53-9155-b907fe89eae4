/*
 * @Descripttion: 
 * @version: 
 * @Author: huangyq
 * @Date: 2021-09-29 18:57:42
 * @LastEditors: huangyq
 * @LastEditTime: 2022-02-18 11:29:19
 */
#ifndef MANISENGINE_REF_H_
#define MANISENGINE_REF_H_

#include "manis_export.hpp"

namespace manisEngine
{

class MANIS_EXPORT Ref
{
public:

    void addRef();

    void release();

    unsigned int getRefCount() const;

protected:

    Ref();
    Ref(const Ref& copy);
    virtual ~Ref();

private:

    unsigned int _refCount;

    // Memory leak diagnostic data (only included when GP_USE_MEM_LEAK_DETECTION is defined)
#ifdef GP_USE_MEM_LEAK_DETECTION
    static void printLeaks();
    void* __record;
#endif
};

}

#endif
