/*
 * @Descripttion: 
 * @version: 
 * @Author: huangyq
 * @Date: 2020-11-20 10:39:09
 * @LastEditors: huangyq
 * @LastEditTime: 2022-10-21 17:05:13
 */
#ifndef COREML_TENSOR_UTIL_H
#define COREML_TENSOR_UTIL_H

#include <stdio.h>
#include <stdlib.h>
#include <functional>

#include "manis_export.hpp"
#include "tensor.hpp"

namespace manis {

enum CVFlag {
    CVFlag_WARP_INVERSE_MAP = 0x1, //逆矩阵

    CVFlag_LAST
};

struct CoreMLPoint {
    float x;
    float y;
};

struct CoreMLSize {
    float width;
    float height;
};

struct CoreMLRect {
    CoreMLPoint origin;
    CoreMLSize size;
};
typedef struct CoreMLRect CoreMLRect;


class MANIS_EXPORT CoreMLImage {
public:
    CoreMLImage();
    ~CoreMLImage();

    /// @brief 仿射矩阵变换(不支持错切、不使用平移参数)后的cvpixelbuffer
    /// @param cvpixelbuffer ios CoreVedio框架内对象
    /// @param flag 1、使用逆矩阵 0、不用逆矩阵
    /// @param target_width  返回旋转后的cvpixelbuffer宽
    /// @param target_height 返回旋转后的cvpixelbuffer高
    /// @param rect 输入输出,设置rect.size,返回rotate后对应的rect.origin值
    bool FromCVPixelBufferRotate(void *cvpixelbuffer, float &target_width, float &target_height, int flag, const float* matrix, CoreMLRect& rect);

    /// @brief FromCVPixelBufferRotate逆操作
    bool FromCVPixelBufferReRotate(void *cvpixelbuffer, float target_width, float target_height, int flag, const float* matrix);

    /// @brief 裁剪
    /// @param cvpixelbuffer ios CoreVedio框架内对象
    /// @param rect ios的坐标原点位置与裁剪宽高
    /// @param rect_from_ios false: rect 采用opencv的坐标原点位置与裁剪宽高
    bool FromCVPixelBufferCrop(void *cvpixelbuffer, CoreMLRect rect, bool rect_from_ios = true);

    /// @brief FromCVPixelBufferCrop逆操作
    bool FromCVPixelBufferReCrop(void *cvpixelbuffer, uint32_t target_width, uint32_t target_height, CoreMLRect rect);

    /// @brief FromCVPixelBufferCrop逆操作 + romCVPixelBufferRotate逆操作
    /// @param target_width  转换后的宽（原图宽）
    /// @param target_height 转换后的高（原图高）
    bool FromCVPixelBufferReRotateFromCrop(void *rotate_pb, uint32_t target_width, uint32_t target_height, void *crop_pb, CoreMLRect rect, int flag, const float* matrix);

    /// @brief 仿射矩阵变换
    /// @param cvpixelbuffer ios CoreVedio框架内对象
    /// @param target_width  转换后的宽
    /// @param target_height 转换后的高
    /// @param flag 1、使用逆矩阵 0、不用逆矩阵
    /// @param matrix 仿射矩阵
    bool FromCVPixelBufferWarpResize(void *cvpixelbuffer, uint32_t target_width, uint32_t target_height, int flag, const float* matrix);
    
    /// @brief 缩放变换
    bool FromCVPixelBufferResize(void *cvpixelbuffer, uint32_t target_width, uint32_t target_height);
    
    /// @brief 先裁剪后缩放
    bool FromCVPixelBufferResizeAndCrop(void *cvpixelbuffer, uint32_t target_width, uint32_t target_height, CoreMLRect rect);

    void* GetCVPixelBuffer();
    Tensor ToTensor();
    
    /// @brief 绑定GL纹理
    bool BindOpenGLESTexture(void* context, void *cvpixelbuffer, uint32_t target_width, uint32_t target_height);
    /// @brief 获取GL纹理ID
    int GetCVOpenGLESTextureName();
    /// @brief 获取GL纹理类型(GL_TEXTURE_2D)
    int GetCVOpenGLESTextureTarget();

    /// @brief 创建CVPixelBuffer，并绑定GL纹理
    bool CreateCVPixelBufferAndBindOpenGLESTexture(void* context, uint32_t target_width, uint32_t target_height);
    /// @brief 创建CVPixelBuffer：L008类型 转 BGRA类型，用于GL纹理绑定
    bool CreateCVPixelBufferFromGrayScale(void *cvpixelbuffer);

    /// @brief 拷贝到cpu内存
    void GetBaseAddress(void *cvpixelbuffer, unsigned char *mem, size_t len);

    /// @brief 创建CVPixelBuffer
    bool CreateCVPixelBuffer(uint32_t target_width, uint32_t target_height);
    /// @brief 处理cvpixelbuffer的cpu内存
    static void DealCVPixelBuffer(void* pcvpixelbuffer, std::function<void(size_t width, size_t height, void*)> callback);


private:
    CoreMLImage(const CoreMLImage&);
    CoreMLImage& operator=(const CoreMLImage&);

    class Impl;
    Impl* impl_;
};

}
#endif //COREML_TENSOR_UTIL_H