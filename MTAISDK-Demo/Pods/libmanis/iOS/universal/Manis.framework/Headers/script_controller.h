#ifndef MANISENGINE_SCRIPTCONTROLLER_H_
#define MANISENGINE_SCRIPTCONTROLLER_H_

#include "manis_export.hpp"
#include "script.h"

namespace manisEngine
{

class MANIS_EXPORT ScriptController
{
public:
    ScriptController();
    ~ScriptController();

    void* toUserData();
    int getTop();
    void setTop(int top);
    
    Script* loadScript(const char* path, Script::Scope scope = Script::GLOBAL, bool forceReload = false);
    Script* loadScript(const char* tag, const char* mem, int size, Script::Scope scope, bool forceReload);
    bool loadScript(Script* script, const char* mem, int size);
    void unloadScript(Script* script);

    //meitu: T is obj
    template<typename T> bool executeFunctionAndCheckObj(const char* func, const char* class1name, const char* args, T** out, ...);
    template<typename T> bool executeFunctionAndCheckObj(Script* script, const char* func, const char* class1name, const char* args, T** out, va_list* list);

    template<typename T> bool executeFunction(const char* func, T* out);
    template<typename T> bool executeFunction(Script* script, const char* func, T* out);
    template<typename T> bool executeFunction(const char* func, const char* args, T* out, ...);
    template<typename T> bool executeFunction(Script* script, const char* func, const char* args, T* out, ...);
    template<typename T> bool executeFunction(const char* func, const char* args, T* out, va_list* list);
    template<typename T> bool executeFunction(Script* script, const char* func, const char* args, T* out, va_list* list);

    bool getBool(const char* name, bool defaultValue = false, Script* script = NULL);

    char getChar(const char* name, char defaultValue = 0, Script* script = NULL);

    short getShort(const char* name, short defaultValue = 0, Script* script = NULL);

    int getInt(const char* name, int defaultValue = 0, Script* script = NULL);

    long getLong(const char* name, long defaultValue = 0, Script* script = NULL);

    unsigned char getUnsignedChar(const char* name, unsigned char defaultValue = 0, Script* script = NULL);

    unsigned short getUnsignedShort(const char* name, unsigned short defaultValue = 0, Script* script = NULL);

    unsigned int getUnsignedInt(const char* name, unsigned int defaultValue = 0, Script* script = NULL);

    unsigned long getUnsignedLong(const char* name, unsigned long defaultValue = 0, Script* script = NULL);

    float getFloat(const char* name, float defaultValue = 0, Script* script = NULL);

    double getDouble(const char* name, double defaultValue = 0, Script* script = NULL);

    const char* getString(const char* name, Script* script = NULL);

    void* getObjectPointer(const char* type, const char* name, Script* script = NULL);
    
    void* getObjectPointerFromStack(const char* type);

    void setBool(const char* name, bool v, Script* script = NULL);

    void setChar(const char* name, char v, Script* script = NULL);

    void setShort(const char* name, short v, Script* script = NULL);

    void setInt(const char* name, int v, Script* script = NULL);

    void setLong(const char* name, long v, Script* script = NULL);

    void setUnsignedChar(const char* name, unsigned char v, Script* script = NULL);

    void setUnsignedShort(const char* name, unsigned short v, Script* script = NULL);

    void setUnsignedInt(const char* name, unsigned int v, Script* script = NULL);

    void setUnsignedLong(const char* name, unsigned long v, Script* script = NULL);

    void setFloat(const char* name, float v, Script* script = NULL);

    void setDouble(const char* name, double v, Script* script = NULL);

    void setString(const char* name, const char* v, Script* script = NULL);

    void setObjectPointer(const char* type, const char* name, void* v, Script* script = NULL);

    bool functionExists(const char* name, const Script* script = NULL) const;

    Script* getCurrentScript() const;

    bool initialize(const char * path);

    void finalize();

    bool executeFunctionHelper(int resultCount, const char* func, const char* args, va_list* list, Script* script = NULL);

    void pushScript(Script* script);

    void popScript();

    class Impl;
    Impl *_impl;
};

/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<void>(const char* func, void* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<bool>(const char* func, bool* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<char>(const char* func, char* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<short>(const char* func, short* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<int>(const char* func, int* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<long>(const char* func, long* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned char>(const char* func, unsigned char* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned short>(const char* func, unsigned short* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned int>(const char* func, unsigned int* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned long>(const char* func, unsigned long* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<float>(const char* func, float* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<double>(const char* func, double* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<std::string>(const char* func, std::string* out);

/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<void>(Script* script, const char* func, void* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<bool>(Script* script, const char* func, bool* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<char>(Script* script, const char* func, char* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<short>(Script* script, const char* func, short* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<int>(Script* script, const char* func, int* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<long>(Script* script, const char* func, long* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned char>(Script* script, const char* func, unsigned char* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned short>(Script* script, const char* func, unsigned short* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned int>(Script* script, const char* func, unsigned int* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned long>(Script* script, const char* func, unsigned long* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<float>(Script* script, const char* func, float* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<double>(Script* script, const char* func, double* out);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<std::string>(Script* script, const char* func, std::string* out);

/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<void>(const char* func, const char* args, void* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<bool>(const char* func, const char* args, bool* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<char>(const char* func, const char* args, char* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<short>(const char* func, const char* args, short* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<int>(const char* func, const char* args, int* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<long>(const char* func, const char* args, long* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned char>(const char* func, const char* args, unsigned char* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned short>(const char* func, const char* args, unsigned short* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned int>(const char* func, const char* args, unsigned int* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned long>(const char* func, const char* args, unsigned long* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<float>(const char* func, const char* args, float* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<double>(const char* func, const char* args, double* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<std::string>(const char* func, const char* args, std::string* out, ...);

/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<void>(Script* script, const char* func, const char* args, void* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<bool>(Script* script, const char* func, const char* args, bool* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<char>(Script* script, const char* func, const char* args, char* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<short>(Script* script, const char* func, const char* args, short* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<int>(Script* script, const char* func, const char* args, int* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<long>(Script* script, const char* func, const char* args, long* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned char>(Script* script, const char* func, const char* args, unsigned char* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned short>(Script* script, const char* func, const char* args, unsigned short* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned int>(Script* script, const char* func, const char* args, unsigned int* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned long>(Script* script, const char* func, const char* args, unsigned long* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<float>(Script* script, const char* func, const char* args, float* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<double>(Script* script, const char* func, const char* args, double* out, ...);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<std::string>(Script* script, const char* func, const char* args, std::string* out, ...);

/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<void>(const char* func, const char* args, void* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<bool>(const char* func, const char* args, bool* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<char>(const char* func, const char* args, char* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<short>(const char* func, const char* args, short* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<int>(const char* func, const char* args, int* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<long>(const char* func, const char* args, long* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned char>(const char* func, const char* args, unsigned char* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned short>(const char* func, const char* args, unsigned short* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned int>(const char* func, const char* args, unsigned int* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned long>(const char* func, const char* args, unsigned long* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<float>(const char* func, const char* args, float* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<double>(const char* func, const char* args, double* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<std::string>(const char* func, const char* args, std::string* out, va_list* list);

/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<void>(Script* script, const char* func, const char* args, void* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<bool>(Script* script, const char* func, const char* args, bool* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<char>(Script* script, const char* func, const char* args, char* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<short>(Script* script, const char* func, const char* args, short* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<int>(Script* script, const char* func, const char* args, int* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<long>(Script* script, const char* func, const char* args, long* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned char>(Script* script, const char* func, const char* args, unsigned char* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned short>(Script* script, const char* func, const char* args, unsigned short* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned int>(Script* script, const char* func, const char* args, unsigned int* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<unsigned long>(Script* script, const char* func, const char* args, unsigned long* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<float>(Script* script, const char* func, const char* args, float* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<double>(Script* script, const char* func, const char* args, double* out, va_list* list);
/** Template specialization. */
template<> bool MANIS_EXPORT ScriptController::executeFunction<std::string>(Script* script, const char* func, const char* args, std::string* out, va_list* list);

}

#include "script_controller.inl"

#endif
