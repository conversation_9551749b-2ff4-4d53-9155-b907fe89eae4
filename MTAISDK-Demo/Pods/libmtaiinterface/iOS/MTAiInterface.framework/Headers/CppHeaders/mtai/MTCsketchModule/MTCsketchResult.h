#pragma once
#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"
namespace mtai
{
    struct MTAIENGINE_API MTCsketchFeature {
        void Print() const ;

        MTAiEngineImage image;
        MTVector<MTPoint2f> facePoints;
        MTVector<MTVector<MTPoint2f>> bodyContours ; // 身体路径
        MTVector<MTVector<MTPoint2f>> hairContours ; // 头发路径
        float hairContourWeight ;                    // 头发路径权重
        float bodyContourWeight ;                    // 身体路径权重
    };

    struct MTAIENGINE_API MTCsketchResult
    {
        void Print() const ;

        bool normalize = true;                      ///< 是否归一化数据
        int orientation = 1;                        ///< 数据方向
        MTSize_<int> size = MTSize_<int>(1, 1);     ///< 数据size
        MTCsketchFeature  features;        ///< 线稿

        float                   runTime     = 0.0;                  ///< 运行耗时 单位ms
    };

    /**
     * 线稿结果转化
     * 需要在dst中先填充参数
     *
     * @param src   输入
     * @param dst   输出
     * @return      结果
     */
    MTAIENGINE_API
    MTAiEngineRet ConvertCsketchResult(const MTCsketchResult &src, MTCsketchResult &dst);
}
