/*****************************************************************
* CPU接口(基类)
* Copyright (c) 2021 MEITU. All rights reserved.
*
* @version: 0.0.1.0
*
* @author:  zm8
*
* @email:  <EMAIL>
*
* @date: 2022-08-09
*
* @note: 目前实现功能：匀肤。
*
* @usage: 对外接口。枚举包含模型类型、设备类型；包括CPU加载模型和调用接口。
*
* @change:
*
******************************************************************/
#pragma once
#ifndef InceptionBeautyDoubleChinFixUtil_h
#define InceptionBeautyDoubleChinFixUtil_h


#include "InceptionBeautyDefine.h"



namespace mtai {
namespace mtdlbeauty
{

    class DL_INCEPTION_BEAUTY_EXPORT InceptionBeautyDoubleChinFixUtil
    {
    public:
        enum InceptionBeautyDoubleChinFixProcType
        {
            IB_DOUBLECHIN_FIX_BEST = 0
        };
        
    public:
        InceptionBeautyDoubleChinFixUtil();
        virtual ~InceptionBeautyDoubleChinFixUtil();
        
        virtual void Init();
        
        //不允许使用拷贝构造函数和等于操作
        InceptionBeautyDoubleChinFixUtil(InceptionBeautyDoubleChinFixUtil& rhs) = delete;
        InceptionBeautyDoubleChinFixUtil& operator=(InceptionBeautyDoubleChinFixUtil& rhs) = delete;
        
        
        /*
        @param pModelPath:      模型路径
        @param nProcType:       处理类型:                      对应模型：
                                IB_DOUBLECHIN_FIX_BEST      ->  dionysos.bin
        @param nDeviceType:     设备类型:
                                DEVICE_CPU
                                DEVICE_CUDA
        */
        virtual bool LoadModels(const char* pModelPath,
                                const int nProcType = IB_DOUBLECHIN_FIX_BEST,
                                const char* pDeviceType = "DEVICE_CPU");
        
        
        /*
        @param pModelData:      模型数据流
        @param nModelDataSize:  模型数据流尺寸
        @param nProcType:       处理类型:                      对应模型：
                                 IB_DOUBLECHIN_FIX_BEST      ->  dionysos.bin
        @param nDeviceType:     设备类型:
                                 DEVICE_CPU
                                 DEVICE_CUDA
        */
        virtual bool LoadModelsData(const char* pModelData, const long lModelDataSize,
                                    const char* pDeviceType = "DEVICE_CPU");
        
        
        /*
        @brief: 调用函数，模型前向和其他流程都跑cpu
        @param pImage:       输入图(rgba格式）
        @param pMask:        皮肤mask，单通道，分辨率与输入图相同
        @param nWidth:       输入图宽
        @param nHeight:      输入图高
        @param pfYawls:      人像脸部姿态
        @param pfFacePoints: 输入图人脸点
        @param nFace:        输入图人脸数
        @param nFacePoints:  输入单个人脸点个数
        @param 返回值： 成功返回true，其他false
        */
        virtual bool Run(unsigned char* pImage, unsigned char* pMask, const int nWidth, const int nHeight,
                         const float* pfYawls, const float* pfFacePoints, const int nFace, const int nFacePoints);
        
        virtual const char * GetVersion();
    protected:
        
        void* m_pInceptionBeautyDL;
        
    };
}// end namespace mtdlbeauty
}// end namespace mtai
#endif /* InceptionBeautyUtil_h */
