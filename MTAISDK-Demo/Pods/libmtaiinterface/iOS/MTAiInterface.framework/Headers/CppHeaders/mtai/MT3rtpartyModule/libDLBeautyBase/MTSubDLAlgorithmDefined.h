#ifndef _MTLIBAI_SUB_DL_ALGORITHM_DEFINED_H_
#define _MTLIBAI_SUB_DL_ALGORITHM_DEFINED_H_

#ifndef GLuint
typedef unsigned int GLuint;
#endif

#ifndef GLint
typedef int GLint;
#endif

#ifndef GLenum
typedef unsigned int GLenum;
#endif


#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_CPU        "DEVICE_CPU"
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_CPU_C4     "DEVICE_CPU_C4"
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_OPENGL     "DEVICE_OPENGL"    //模型前向和其他流程都跑gl3.0
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_OPENCL     "DEVICE_OPENCL"
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_CUDA       "DEVICE_CUDA"
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_HEXAGON    "DEVICE_HEXAGON"
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_METAL      "DEVICE_METAL"     //仅ios可用metal，
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_GLCS       "DEVICE_GLCS"     //仅ios可用metal，
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_HIAI_NPU   "DEVICE_HIAI_NPU"
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_OPENVINO   "DEVICE_OPENVINO"  ////服务端cpu使用
#define MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_COREML     "DEVICE_COREML"  ////服务端cpu使用

#define MTLABAI_SUB_DL_ALGORITHM_STRING_DATA_TYPE_FLOAT        "DATA_TYPE_FLOAT"


extern "C" {
    enum mtlabai_sub_DL_algorithm_proc_type
    {
        MTLABAI_SUB_DL_ALGORITHM_RM_BEST = 8,
        MTLABAI_SUB_DL_ALGORITHM_RM_FAST = 9
    };

    enum mtlabai_sub_DL_algorithm_device_type
    {
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_CPU        = 0,
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_CPU_C4     = 1,
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_OPENGL     = 2,  //模型前向和其他流程都跑gl3.0
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_OPENCL     = 3,
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_CUDA       = 4,
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_HEXAGON    = 5,
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_METAL      = 6,  //仅ios可用metal，
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_GLCS       = 7,  //仅ios可用metal，
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_HIAI_NPU   = 8,
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_OPENVINO   = 9,   ////服务端cpu使用
        MTLABAI_SUB_DL_ALGORITHM_DEVICE_TYPE_COREML     = 10
    };

    enum mtlabai_sub_DL_algorithm_data_type
    {
        MTLABAI_SUB_DL_ALGORITHM_DATA_TYPE_FLOAT        = 0
    };
}

#endif