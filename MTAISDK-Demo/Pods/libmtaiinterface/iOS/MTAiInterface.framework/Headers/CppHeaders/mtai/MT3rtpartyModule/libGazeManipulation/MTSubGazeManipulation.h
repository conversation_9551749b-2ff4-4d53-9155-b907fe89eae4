#ifndef _MT_SUB_GAZE_MANIPULATION_H_
#define _MT_SUB_GAZE_MANIPULATION_H_

#include <cstddef>

#include <mtai/Common/MTAiEngineMacro.h>

extern "C" {

    typedef struct mtlabai_sub_gaze_manipulation_handle_t mtlabai_sub_gaze_manipulation_handle_t;

    enum mtlabai_sub_gaze_manipulation_device {
        MTLABAI_SUB_GAZE_MANIPULATION_AUTO,      //!< manis::DEVICE_AUTO
        MTLABAI_SUB_GAZE_MANIPULATION_CPU,       //!< manis::DEVICE_CPU
        MTLABAI_SUB_GAZE_MANIPULATION_OPENGL,    //!< manis::DEVICE_OPENGL
        MTLABAI_SUB_GAZE_MANIPULATION_OPENCL,    //!< manis::DEVICE_OPENCL
        MTLABAI_SUB_GAZE_MANIPULATION_CUDA,      //!< manis::DEVICE_CUDA
        MTLABAI_SUB_GAZE_MANIPULATION_HEXAGON,   //!< manis::DEVICE_HEXAGON
        MTLABAI_SUB_GAZE_MANIPULATION_METAL,     //!< manis::DEVICE_METAL
        MTLABAI_SUB_GAZE_MANIPULATION_WEBGL,     //!< manis::DEVICE_WEBGL
        MTLABAI_SUB_GAZE_MANIPULATION_GLCS,      //!< manis::DEVICE_GLCS
        MTLABAI_SUB_GAZE_MANIPULATION_HIAI_NPU,  //!< manis::DEVICE_HIAI_NPU
        MTLABAI_SUB_GAZE_MANIPULATION_COREML,    //!< manis::DEVICE_COREML
        MTLABAI_SUB_GAZE_MANIPULATION_OPENVINO,  //!< manis::DEVICE_OPENVINO
        MTLABAI_SUB_GAZE_MANIPULATION_QNN,       //!< manis::DEVICE_QNN
    };

    /**
     * \ingroup mtlabai_sub_gaze_manipulation_prepare_parameter
     * \brief 预处理参数
     */
    typedef struct mtlabai_sub_gaze_manipulation_prepare_parameter_ {
        int num_of_faces                 = 0;        //!< 人脸个数
        const float** faces_points       = nullptr;  //!< 人脸点，需要130归一化后的人脸点，格式为：{{X0,Y0,X1,Y1,...,X129,Y129},{X0,Y0,X1,Y1,...,X129,Y129},...}
        const float** faces_visibilities = nullptr;  //!< 人脸点的可见性，格式为{{V0,V1,...,V129},{V0,V1,...,V129},...}
    } mtlabai_sub_gaze_manipulation_prepare_parameter;

    /**
     * \ingroup mtlabai_sub_gaze_manipulation_pupil_parameter
     * \brief 眼球参数
     */
    typedef struct mtlabai_sub_gaze_manipulation_pupil_parameter_ {
        int position      = -1;     //!< 眼球位置，取值范围为`[0,100]`, 如果值为`-1`，则恢复该眼瞳到初始状态
        float radius_bias = 0;      //!< 眼球半径偏置值，取值范围为`[-5,5]`，默认值为0
        bool paste        = false;  //!< 贴眼瞳，默认值是false
    } mtlabai_sub_gaze_manipulation_pupil_parameter;

    /**
     * \ingroup mtlabai_sub_gaze_manipulation_process_parameter
     * \brief 处理参数
     */
    typedef struct mtlabai_sub_gaze_manipulation_process_parameter_ {
        int face_id = -1;      //!< 人脸mtlabai_sub_gaze_manipulation_id，和\ref M_tlab::mtlabai_sub_gaze_manipulation_galaxy::mtlabai_sub_gaze_manipulation_gaze_manipulation::mtlabai_sub_gaze_manipulation_prepare_parameter 中的人脸对应
        mtlabai_sub_gaze_manipulation_pupil_parameter left;   //!< 左眼眼球
        mtlabai_sub_gaze_manipulation_pupil_parameter right;  //!< 右眼眼球
    } mtlabai_sub_gaze_manipulation_process_parameter;

    /**
     * \ingroup mtlabai_sub_gaze_manipulation_gaze_manipulation
     * \brief 眼瞳信息
     */
    typedef struct mtlabai_sub_gaze_manipulation_pupil_info_ {
        int position                 = -1;       //!< 眼球位置，取值范围为`[0,100]`, 如果值为`-1`，则恢复该眼瞳到初始状态
        float radius                 = 0;        //!< 眼瞳半径，用于获取眼部信息，外部调用时不可修改
        float radius_bias            = 0;        //!< 眼球半径偏置值，取值范围为`[-5,5]`，默认值为0
        int num_of_coordinates       = 0;        //!< 可操作坐标数量
        const int* pupil_coordinates = nullptr;  //!< 可操作作为位置，格式为：{X0,Y0,X1,Y1,...,XN,YN}；如果num_of_coordinates==0，则该参数为nullptr
    } mtlabai_sub_gaze_manipulation_pupil_info;


    /**
     * \brief 初始化眼球编辑处理对象
     * \return 执行状态
     */
    MTAIENGINE_API mtlabai_sub_gaze_manipulation_handle_t *mtlabai_sub_gaze_manipulation_init();

    /**
     * \brief 释放眼球编辑处理对象
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_release(mtlabai_sub_gaze_manipulation_handle_t **handle);

    /**
     * \brief 从路径中加载眼睛生成模型
     * \param[in] model_path 模型路径
     * \param[in] mode 模型前向模式
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_set_eye_generate_model_for_path(mtlabai_sub_gaze_manipulation_handle_t *handle, const char* model_path, mtlabai_sub_gaze_manipulation_device mode);

    /**
     * \brief 从内存中加载眼睛生成模型
     * \param[in] bytes 模型文件的字节数
     * \param[in] data 模型文件的字节流
     * \param[in] mode 模型前向模式
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_set_eye_generate_model_for_data(mtlabai_sub_gaze_manipulation_handle_t *handle, size_t bytes, uint8_t* data, mtlabai_sub_gaze_manipulation_device mode);

    /**
     * \brief 从路径中加载眼睛分割模型
     * \param[in] model_path 模型路径
     * \param[in] mode 模型前向模式
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_set_eye_segment_model_for_path(mtlabai_sub_gaze_manipulation_handle_t *handle, const char* model_path, mtlabai_sub_gaze_manipulation_device mode);

    /**
     * \brief 从内存中加载眼睛分割模型
     * \param[in] bytes 模型文件的字节数
     * \param[in] data 模型文件的字节流
     * \param[in] mode 模型前向模式
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_set_eye_segment_model_for_data(mtlabai_sub_gaze_manipulation_handle_t *handle, size_t bytes, uint8_t* data, mtlabai_sub_gaze_manipulation_device mode);

    /**
     * \brief 通过ai引擎加载模型
     * \param[in] mode 模型前向模式
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_load_models_for_AiDispatch(mtlabai_sub_gaze_manipulation_handle_t *handle, mtlabai_sub_gaze_manipulation_device mode);

    /**
     * \brief 预处理
     * \param[in] src 待处理图像的内存地址，RGBA格式
     * \param[in] stride 待处理图像的stride(mtlabai_sub_gaze_manipulation_handle_t *handle, pitch，行字节数)
     * \param[in] cols 待处理图像的宽度
     * \param[in] rows 待处理图像的高度
     * \param[in] parameter 预处理参数，详细参考\ref M_tlab::mtlabai_sub_gaze_manipulation_galaxy::mtlabai_sub_gaze_manipulation_gaze_manipulation::mtlabai_sub_gaze_manipulation_pupil_parameter
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_prepare(mtlabai_sub_gaze_manipulation_handle_t *handle, const uint8_t* src, size_t stride, int cols, int rows, mtlabai_sub_gaze_manipulation_prepare_parameter& parameter);

    /**
     * \brief 执行算法
     * \param[in] num_of_parameters 参数数量
     * \param[in] parameters 需要编辑的眼球参数
     * \param[in,out] dst 输出图像的内存地址，RGBA格式
     * \param[in] stride 输出图像的stride(mtlabai_sub_gaze_manipulation_handle_t *handle, pitch，行字节数)
     * \param[in] in_place 如果为true，那么只在dst的眼睛区域上修改，否则做全图修改
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_process(mtlabai_sub_gaze_manipulation_handle_t *handle, size_t num_of_parameters, mtlabai_sub_gaze_manipulation_process_parameter* parameters, uint8_t* dst, size_t stride, bool in_place = false);

    /**
     * \brief 设置眼部混合mask
     * \param[in] src mask的内存地址，单通道图像；当地址为空时，则将编辑后的眼瞳直接绘制到结果上
     * \param[in] stride mask的stride(mtlabai_sub_gaze_manipulation_handle_t *handle, pitch，行字节数)
     * \param[in] cols mask的宽度
     * \param[in] rows mask的高度
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_set_blend_mask(mtlabai_sub_gaze_manipulation_handle_t *handle, const uint8_t* src, size_t stride, int cols, int rows);

    /**
     * \brief 通过人脸mtlabai_sub_gaze_manipulation_id获取左眼球参数
     * \param[in] face_id 人脸mtlabai_sub_gaze_manipulation_id
     * \param[out] info 眼球参数
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_get_left_pupil_info_by_face_id(mtlabai_sub_gaze_manipulation_handle_t *handle, int face_id, mtlabai_sub_gaze_manipulation_pupil_info& info);

    /**
     * \brief 通过人脸mtlabai_sub_gaze_manipulation_id获取眼球参数
     * \param[in] face_id 人脸mtlabai_sub_gaze_manipulation_id
     * \param[out] info 眼球参数
     * \return 执行状态
     */
    MTAIENGINE_API bool mtlabai_sub_gaze_manipulation_get_right_pupil_info_by_face_id(mtlabai_sub_gaze_manipulation_handle_t *handle, int face_id, mtlabai_sub_gaze_manipulation_pupil_info& info);

    /**
     * \brief 获取图像的宽度
     * \return 图像的宽度
     */
    MTAIENGINE_API int mtlabai_sub_gaze_manipulation_cols(mtlabai_sub_gaze_manipulation_handle_t *handle);

    /**
     * \brief 获取图像的高度
     * \return 图像的高度
     */
    MTAIENGINE_API int mtlabai_sub_gaze_manipulation_rows(mtlabai_sub_gaze_manipulation_handle_t *handle);

    /**
     * \brief 获取版本信息
     * \return 版本信息
     */
    MTAIENGINE_API const char *mtlabai_sub_gaze_manipulation_version(mtlabai_sub_gaze_manipulation_handle_t *handle);


}


#endif