#pragma once
#include <cmath>
#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"

namespace mtai
{
    ////////////////////////////////////////////////////////////////////////////////////////
    //								EXIF方向示例
    //     1        2       3      4         5            6           7          8
    //
    //    888888  888888      88  88      8888888888  88                  88  8888888888
    //    88          88      88  88      88  88      88  88          88  88      88  88
    //    8888      8888    8888  8888    88          8888888888  8888888888          88
    //    88          88      88  88
    //    88          88  888888  888888
    ////////////////////////////////////////////////////////////////////////////////////////
    enum MTAnchorGenStatus{
        SUCCEED = 0,                    // 生成锚点成功
        TOO_LARGE_HUMAN = 1,            // 人物面积过大
        TOO_LARGE_ANCHOR = 2,           // 锚点半径过大
        TOO_SIMPLE_BACKGROUND = 3       // 背景过于简单
    };

    struct MTAIENGINE_API MTAnchorPoint{
        MTAnchorPoint();
        MTAnchorPoint(const MTAnchorPoint &x);
        const MTAnchorPoint &operator=(const MTAnchorPoint &x);
        ~MTAnchorPoint();
        void Print() const ;

        MTPoint2f      point;         ///< 锚点
        float          radius;        ///< 锚点半径

    };

    struct MTAIENGINE_API MTAnchorMatch{
        MTAnchorMatch();
        MTAnchorMatch(const MTAnchorMatch &x);
        const MTAnchorMatch &operator=(const MTAnchorMatch &x);
        ~MTAnchorMatch();
        void Print() const ;

        bool      match;         ///< 匹配
        float     score;         ///< 置信度

    };

    struct MTAIENGINE_API MTAnchorGenerationResult
    {
        MTAnchorGenerationResult();
        MTAnchorGenerationResult(const MTAnchorGenerationResult &x);
        const MTAnchorGenerationResult &operator=(const MTAnchorGenerationResult &x);
        ~MTAnchorGenerationResult();
        void Print() const ;

        bool                                normalize = true;              ///< 是否归一化数据
        int                                 orientation = 1;               ///< 数据方向
        MTSize_<int>                        size = MTSize_<int>(1, 1);     ///< 数据size
        MTAnchorGenStatus                   anchor_gen_status;             ///< 锚点生成情况
        MTVector<MTAnchorPoint>             anchor_point;                  ///< 锚点生成数据
        MTVector<MTAnchorMatch>             anchor_match;                  ///< 锚点匹配数据

        float                   runTime     = 0.0;                  ///< 运行耗时 单位ms
    };

}
