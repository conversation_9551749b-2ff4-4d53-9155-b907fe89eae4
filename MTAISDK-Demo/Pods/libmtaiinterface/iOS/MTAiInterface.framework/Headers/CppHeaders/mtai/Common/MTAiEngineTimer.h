//
// Created by l<PERSON><PERSON><PERSON><PERSON> on 2019/12/19.
//
#pragma once 
#include <mtai/Common/MTValueMap.h>
#include <mtai/Common/MTAiEnginePlatform.h>
#ifndef MTAIENGINE_PLATFORM_WINDOWS
#include <sys/time.h>
#else
#include <time.h>
#ifdef WIN32_LEAN_AND_MEAN
#include <WinSock2.h>
#endif // WIN32_LEAN_AND_MEAN
#include <windows.h>
#endif

namespace mtai 
{
template<class VAL_TYPE>
class MTValueMapNew;


#ifdef MTAIENGINE_PLATFORM_WINDOWS
	int gettimeofday(struct timeval *tv, void *tz);
#endif

//注：为了尽量减少MTAiEngineTimer自身对性能的影响，内部没有加互斥锁。所以不要在多线程中使用，不安全
class MTAiEngineTimer
{
    public:
        explicit MTAiEngineTimer(const char* tag);

        ~MTAiEngineTimer();

    private:
        struct TimerDataCatch {
            TimerDataCatch(float t, int c) {
                total_ = t;
                count_ = c;
            }
            TimerDataCatch() {
                total_ = 0.0;
                count_ = 0;
            }

            float total_;
            int count_;
        };
        
        static MTValueMapNew<TimerDataCatch> m_CTimerMap;
        const char* m_cTag;
        long m_lSec;
        long m_lUsec;
};

}