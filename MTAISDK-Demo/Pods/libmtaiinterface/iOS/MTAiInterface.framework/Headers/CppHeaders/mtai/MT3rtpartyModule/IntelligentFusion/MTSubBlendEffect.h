#ifndef _MT_SUB_BLEND_EFFECT_H_
#define _MT_SUB_BLEND_EFFECT_H_

#include <cstddef>

#include <mtai/Common/MTAiEngineMacro.h>


#ifndef uint8_t
    typedef unsigned char uint8_t;
#endif

#ifndef uint32_t
typedef unsigned int uint32_t;
#endif


extern "C" {
    typedef struct mtlabai_sub_blend_effect_handle_t mtlabai_sub_blend_effect_handle_t;

    /**
    * \brief 构造效果混合算法处理对象
    * \param[in] cols 处理宽度
    * \param[in] rows 处理高度
    */
    MTAIENGINE_API mtlabai_sub_blend_effect_handle_t *mtlabai_sub_blend_effect_handle_create(int cols, int rows);

    /**
    * \brief 释放handle
    *
    */
    MTAIENGINE_API void mtlabai_sub_blend_effect_handle_release(mtlabai_sub_blend_effect_handle_t **handle);

    /**
    * \brief 获取处理宽度
    * \return 处理宽度
    */
    MTAIENGINE_API int mtlabai_sub_blend_effect_cols(mtlabai_sub_blend_effect_handle_t *handle);

    /**
    * \brief 获取处理高度
    * \return 处理高度
    */
    MTAIENGINE_API int mtlabai_sub_blend_effect_rows(mtlabai_sub_blend_effect_handle_t *handle);

    /**
    * \brief 设置混合比例
    * \param[in] ratio 混合比例，取值范围为\f$[0,1]\f$
    * \return 设置混合比例是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_blend_effect_set_mix_ratio(mtlabai_sub_blend_effect_handle_t *handle, float ratio);

    /**
    * \brief 获取混合比例
    * \param[out] ratio 混合比例
    * \return 获取混合比例是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_blend_effect_get_mix_ratio(mtlabai_sub_blend_effect_handle_t *handle, float& ratio);

    /**
    * \brief 设置羽化半径
    * \note 只有设置了权重图（Weight）该参数才会生效
    * \param[in] radius 羽化半径，\f$radius \ge 0\f$
    * \return 设置羽化半径是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_blend_effect_set_feather_radius(mtlabai_sub_blend_effect_handle_t *handle, float radius);

    /**
    * \brief 获取羽化半径
    * \param[out] radius 羽化半径
    * \return 获取羽化半径是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_blend_effect_get_feather_radius(mtlabai_sub_blend_effect_handle_t *handle, float& radius);

    /**
    * \brief 设置权重图
    * \param[in] weight_stride 权重图的stride(行字节长)
    * \param[in] weight 权重图的内存指针，单通道数据类型，当指针为nullptr时，移除权重图
    * \param[in] reference 是否将权重图的内存指针作为引用，默认为false
    * \return 设置权重图是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_blend_effect_set_weight(mtlabai_sub_blend_effect_handle_t *handle, size_t weight_stride, const uint8_t* weight, bool reference);

    /**
    * \brief 获取权重图的stride(行字节长)
    * \return 权重图的stride(行字节长)，当获取失败时，返回0
    */
    MTAIENGINE_API size_t mtlabai_sub_blend_effect_get_weight_stride(mtlabai_sub_blend_effect_handle_t *handle);

    /**
    * \brief 获取权重图的内存指针
    * \return 权重图的内存指针，当获取失败时，返回nullptr
    */
    MTAIENGINE_API const uint8_t* mtlabai_sub_blend_effect_get_weight_data(mtlabai_sub_blend_effect_handle_t *handle);

    /**
    * \brief 执行算法
    * \param[in] backdrop_stride 背图图像的stride(行字节长)
    * \param[in] backdrop 背图图像的内存地址，RGBA格式
    * \param[in] source_stride 前景图像的stride(行字节长)
    * \param[in] source 前景图像的内存地址，RGBA格式
    * \param[in] dst_stride 输出图像的stride(行字节长)
    * \param[in] dst 输出图像的内存地址，RGBA格式
    * \return 执行算法是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_blend_effect_process(mtlabai_sub_blend_effect_handle_t *handle, size_t backdrop_stride, const uint8_t* backdrop, size_t source_stride, const uint8_t* source, size_t dst_stride, uint8_t* dst);


}

#endif