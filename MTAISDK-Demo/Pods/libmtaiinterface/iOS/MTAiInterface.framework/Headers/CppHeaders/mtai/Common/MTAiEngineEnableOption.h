#ifndef MTAIENGINE_MTMODULE_ENABLE_OPTION_H
#define MTAIENGINE_MTMODULE_ENABLE_OPTION_H

#include "mtai/MTFaceModule/MTFaceModuleOption.h"
#include "mtai/MTHandModule/MTHandModuleOption.h"
#include "mtai/MTAnimalModule/MTAnimalModuleOption.h"
#include "mtai/MTBodyModule/MTBodyModuleOption.h"
#include "mtai/MTSegmentModule/MTSegmentModuleOption.h"
#include "mtai/MTMakeupModule/MTMakeupModuleOption.h"
#include "mtai/MTFoodModule/MTFoodModuleOption.h"
#include "mtai/MTSceneryBoundaryLineModule/MTSceneryBoundaryLineModuleOption.h"
#include "mtai/MTSkinModule/MTSkinModuleOption.h"
#include "mtai/MTMaterialTrackingModule/MTMaterialTrackingModuleOption.h"
#include "mtai/MTShoulderModule/MTShoulderModuleOption.h"
#include "mtai/MTInstanceSegmentModule/MTInstanceSegmentModuleOption.h"
#include "mtai/MTOrnamentModule/MTOrnamentModuleOption.h"
#include "mtai/MTCsketchModule/MTCsketchModuleOption.h"
#include "mtai/MTHairModule/MTHairModuleOption.h"
#include "mtai/MTPortraitInpaintingModule/MTPortraitInpaintingModuleOption.h"
#include "mtai/MTFaceHDModule/MTFaceHDModuleOption.h"
#include "mtai/MTToKidModule/MTToKidModuleOption.h"
#include "mtai/MTImageRecognitionModule/MTImageRecognitionModuleOption.h"
#include "mtai/MTAnchorGenerationModule/MTAnchorGenerationModuleOption.h"
#include "mtai/MTSkinMicroModule/MTSkinMicroModuleOption.h"
#include "mtai/MTLandmarkModule/MTLandmarkModuleOption.h"
#include "mtai/MTRemoveWatermarkModule/MTRemoveWatermarkModuleOption.h"
#include "mtai/MTImageDetectionModule/MTImageDetectionModuleOption.h"
#include "mtai/MTDL3DModule/MTDL3DModuleOption.h"
#include "mtai/MTTeethModule/MTTeethModuleOption.h"
#include "mtai/MTEveSkinModule/MTEveSkinModuleOption.h"
#include "mtai/MTSkinBCCModule/MTSkinBCCModuleOption.h"
#include "mtai/MT3DFaceModule/MT3DFaceModuleOption.h"
#include "mtai/MTBodyInOneModule/MTBodyInOneModuleOption.h"
#include "mtai/MTWrinkleDetectionModule/MTWrinkleDetectionModuleOption.h"
#include "mtai/MTDenseHairModule/MTDenseHairModuleOption.h"
#include "mtai/MTCgStyleModule/MTCgStyleModuleOption.h"
#include "mtai/MTFoodStyleModule/MTFoodStyleModuleOption.h"
#include "mtai/MTSmileModule/MTSmileModuleOption.h"
#include "mtai/MTEveQualityModule/MTEveQualityModuleOption.h"
#include "mtai/MTFaceAnalysisXModule/MTFaceAnalysisXModuleOption.h"
#include "mtai/MTKiev3DMakeModule/MTKiev3DMakeModuleOption.h"
#include "mtai/MTSkinToneMappingModule/MTSkinToneMappingModuleOption.h"
#include "mtai/MTEyeSegmentModule/MTEyeSegmentModuleOption.h"
#include "mtai/MTVideoStabilizationModule/MTVideoStabilizationModuleOption.h"
#include "mtai/MTVideoRecognitionModule/MTVideoRecognitionModuleOption.h"
#include "mtai/MTHighDofEyelidModule/MTHighDofEyelidModuleOption.h"
#include "mtai/MTEyelidRealtimeModule/MTEyelidRealtimeModuleOption.h"
#include "mtai/MTVideoOptimizerModule/MTVideoOptimizerModuleOption.h"
#include "mtai/MTFaceBlitModule/MTFaceBlitModuleOption.h"
#include "mtai/MTAIKitModule/MTAIKitModuleOption.h"
#include "mtai/MTSkinARModule/MTSkinARModuleOption.h"
#include "mtai/MTNoseBlendModule/MTNoseBlendModuleOption.h"
#include "mtai/MTHuman3dModule/MTHuman3dModuleOption.h"
#include "mtai/MTEyelidImageModule/MTEyelidImageModuleOption.h"
#include "mtai/MTNevusDetectionModule/MTNevusDetectionModuleOption.h"
#include "mtai/MTEveAutoSkinColorModule/MTEveAutoSkinColorModuleOption.h"
#include "mtai/MTEvePreDetectModule/MTEvePreDetectModuleOption.h"
#include "mtai/MTDoubleChinFixModule/MTDoubleChinFixModuleOption.h"
#include "mtai/MTHairGrouthModule/MTHairGrouthModuleOption.h"
#include "mtai/MTPortraitDetectionModule/MTPortraitDetectionModuleOption.h"
#include "mtai/MTHairDyeModule/MTHairDyeModuleOption.h"
#include "mtai/MTRTTeethRetouchModule/MTRTTeethRetouchModuleOption.h"
#include "mtai/MTRestoreTeethModule/MTRestoreTeethModuleOption.h"
#include "mtai/MTHairStraightModule/MTHairStraightModuleOption.h"
#include "mtai/MTHairFluffyModule/MTHairFluffyModuleOption.h"
#include "mtai/MTHairCurlyModule/MTHairCurlyModuleOption.h"
//__END_OF_OPTION_HEADER__

struct cJSON;

namespace mtai
{
    class MTAiEngine;

    /**
     * 检测过程中检测件需要的开关选项
     *
     * 这个数据决定检测件检测过程中需要执行哪些功能的检测
     */
    
    class MTAIENGINE_API MTAiEngineEnableOption {

    public:
        
        MTAiEngineEnableOption() {
            faceEnable = new MTFaceModuleOption();
            m_vecOption.push_back(faceEnable);

            handEnable = new MTHandModuleOption();
            m_vecOption.push_back(handEnable);

            animalEnable = new MTAnimalModuleOption();
            m_vecOption.push_back(animalEnable);

            bodyEnable = new MTBodyModuleOption();
            m_vecOption.push_back(bodyEnable);

            segmentEnable = new MTSegmentModuleOption();
            m_vecOption.push_back(segmentEnable);

            makeupEnable = new MTMakeupModuleOption();
            m_vecOption.push_back(makeupEnable);

            foodEnable = new MTFoodModuleOption();
            m_vecOption.push_back(foodEnable);

            boundarylineEnable = new MTSceneryBoundaryLineModuleOption();
            m_vecOption.push_back(boundarylineEnable);

            skinEnable = new MTSkinModuleOption();
            m_vecOption.push_back(skinEnable);

            materialtrackingEnable = new MTMaterialTrackingModuleOption();
            m_vecOption.push_back(materialtrackingEnable);

            shoulderEnable = new MTShoulderModuleOption();
            m_vecOption.push_back(shoulderEnable);

            instanceSegEnable = new MTInstanceSegmentModuleOption();
            m_vecOption.push_back(instanceSegEnable);

            ornamentEnable = new MTOrnamentModuleOption();
            m_vecOption.push_back(ornamentEnable);

            csketchEnable  = new MTCsketchModuleOption();
            m_vecOption.push_back(csketchEnable);

            hairEnable = new MTHairModuleOption();
            m_vecOption.push_back(hairEnable);

            portraitInpaintingEnable = new MTPortraitInpaintingModuleOption();
            m_vecOption.push_back(portraitInpaintingEnable);

            faceHDEnable = new MTFaceHDModuleOption();
            m_vecOption.push_back(faceHDEnable);

            toKidEnable = new MTToKidModuleOption();
            m_vecOption.push_back(toKidEnable);

            imageRecognitionEnable = new MTImageRecognitionModuleOption();
            m_vecOption.push_back(imageRecognitionEnable);

            anchorGenerationEnable = new MTAnchorGenerationModuleOption();
            m_vecOption.push_back(anchorGenerationEnable);

            skinMicroEnable = new MTSkinMicroModuleOption();
            m_vecOption.push_back(skinMicroEnable);

            landmarkEnable = new MTLandmarkModuleOption();
            m_vecOption.push_back(landmarkEnable);

            removeWatermarkEnable = new MTRemoveWatermarkModuleOption();
            m_vecOption.push_back(removeWatermarkEnable);

            imageDetectionEnable = new MTImageDetectionModuleOption();
            m_vecOption.push_back(imageDetectionEnable);

            dL3DEnable = new MTDL3DModuleOption();
            m_vecOption.push_back(dL3DEnable);

            teethEnable = new MTTeethModuleOption();
            m_vecOption.push_back(teethEnable);

            eveSkinEnable = new MTEveSkinModuleOption();
            m_vecOption.push_back(eveSkinEnable);

            skinBCCEnable = new MTSkinBCCModuleOption();
            m_vecOption.push_back(skinBCCEnable);

			threeDFaceEnable = new MT3DFaceModuleOption();
            m_vecOption.push_back(threeDFaceEnable);

            bodyInOneEnable = new MTBodyInOneModuleOption();
            m_vecOption.push_back(bodyInOneEnable);

            wrinkleDetectionEnable = new MTWrinkleDetectionModuleOption();
            m_vecOption.push_back(wrinkleDetectionEnable);

            denseHairEnable = new MTDenseHairModuleOption();
            m_vecOption.push_back(denseHairEnable);

            cgStyleEnable = new MTCgStyleModuleOption();
            m_vecOption.push_back(cgStyleEnable);

            foodStyleEnable = new MTFoodStyleModuleOption();
            m_vecOption.push_back(foodStyleEnable);

            smileEnable = new MTSmileModuleOption();
            m_vecOption.push_back(smileEnable);

            eveQualityEnable = new MTEveQualityModuleOption();
            m_vecOption.push_back(eveQualityEnable);

            faceAnalysisXEnable = new MTFaceAnalysisXModuleOption();
            m_vecOption.push_back(faceAnalysisXEnable);

            kiev3DMakeEnable = new MTKiev3DMakeModuleOption();
            m_vecOption.push_back(kiev3DMakeEnable);

            skinToneMappingEnable = new MTSkinToneMappingModuleOption();
            m_vecOption.push_back(skinToneMappingEnable);

            eyeSegmentEnable = new MTEyeSegmentModuleOption();
            m_vecOption.push_back(eyeSegmentEnable);

            videoStabilizationEnable = new MTVideoStabilizationModuleOption();
            m_vecOption.push_back(videoStabilizationEnable);

            videoRecognitionEnable = new MTVideoRecognitionModuleOption();
            m_vecOption.push_back(videoRecognitionEnable);

            highDofEyelidEnable = new MTHighDofEyelidModuleOption();
            m_vecOption.push_back(highDofEyelidEnable);

            eyelidRealtimeEnable = new MTEyelidRealtimeModuleOption();
            m_vecOption.push_back(eyelidRealtimeEnable);

            videoOptimizerEnable = new MTVideoOptimizerModuleOption();
            m_vecOption.push_back(videoOptimizerEnable);

            faceBlitEnable = new MTFaceBlitModuleOption();
            m_vecOption.push_back(faceBlitEnable);

            aiKitEnable = new MTAIKitModuleOption();
            m_vecOption.push_back(aiKitEnable);

            skinAREnable = new MTSkinARModuleOption();
            m_vecOption.push_back(skinAREnable);

            noseBlendEnable = new MTNoseBlendModuleOption();
            m_vecOption.push_back(noseBlendEnable);

            human3dEnable = new MTHuman3dModuleOption();
            m_vecOption.push_back(human3dEnable);

            eyelidImageEnable = new MTEyelidImageModuleOption();
            m_vecOption.push_back(eyelidImageEnable);

            nevusDetectionEnable = new MTNevusDetectionModuleOption();
            m_vecOption.push_back(nevusDetectionEnable);

            eveAutoSkinColorEnable = new MTEveAutoSkinColorModuleOption();
            m_vecOption.push_back(eveAutoSkinColorEnable);

            evePreDetectEnable = new MTEvePreDetectModuleOption();
            m_vecOption.push_back(evePreDetectEnable);

            doubleChinFixEnable = new MTDoubleChinFixModuleOption();
            m_vecOption.push_back(doubleChinFixEnable);

            hairGrouthEnable = new MTHairGrouthModuleOption();
            m_vecOption.push_back(hairGrouthEnable);
            portraitDetectionEnable = new MTPortraitDetectionModuleOption();
            m_vecOption.push_back(portraitDetectionEnable);
            hairDyeEnable = new MTHairDyeModuleOption();
            m_vecOption.push_back(hairDyeEnable);
            rtTeethRetouchEnable = new MTRTTeethRetouchModuleOption();
            m_vecOption.push_back(rtTeethRetouchEnable);
            restoreTeethEnable = new MTRestoreTeethModuleOption();
            m_vecOption.push_back(restoreTeethEnable);
            hairStraightEnable = new MTHairStraightModuleOption();
            m_vecOption.push_back(hairStraightEnable);
            hairFluffyEnable = new MTHairFluffyModuleOption();
            m_vecOption.push_back(hairFluffyEnable);
            hairCurlyEnable = new MTHairCurlyModuleOption();
            m_vecOption.push_back(hairCurlyEnable);
//__END_OF_OPTION_NEW__
        }

        ~MTAiEngineEnableOption() {
            for (size_t i = 0; i < m_vecOption.size(); i++)
            {
                delete m_vecOption[i];
            }

            m_vecOption.clear();
        }

        //根据MuduleType，获取对应的MTAiEngineOption
        MTAiEngineOption* GetAiEngineOption(MTAiEngineType moduleType) const;

        //获取当前可运行模块的参数捕获
        std::vector<cJSON*> GetParamsCapture(std::vector<MTAiEngine*> runModuleVector) const;

        //获取当前开启模块的option的json字符串
        const MTVector<char> GetEnumJsonStrs() const;

        //获取外部参数捕获(序列化)
        cJSON* GetOutsideDataCaptureSerialized() const;

        //获取各模块的option开关
        const MTVector<uint64_t> GetAllEnableOption() const;

        //比较各模块的option开关是否有改变
        static const bool CompareAllEnableOption(const MTVector<uint64_t> vecAllEnableOption1, const MTVector<uint64_t> vecAllEnableOption2);

    public:
        MTFaceModuleOption* faceEnable;
        MTHandModuleOption* handEnable;
        MTAnimalModuleOption* animalEnable;
        MTBodyModuleOption* bodyEnable;
        MTSegmentModuleOption* segmentEnable;
        MTMakeupModuleOption* makeupEnable;
        MTFoodModuleOption* foodEnable;
        MTSceneryBoundaryLineModuleOption* boundarylineEnable;
        MTSkinModuleOption* skinEnable;
        MTMaterialTrackingModuleOption *materialtrackingEnable;
        MTShoulderModuleOption *shoulderEnable;
        MTInstanceSegmentModuleOption *instanceSegEnable;
        MTOrnamentModuleOption *ornamentEnable;
        MTCsketchModuleOption *csketchEnable;
        MTHairModuleOption *hairEnable;
        MTPortraitInpaintingModuleOption *portraitInpaintingEnable;
        MTFaceHDModuleOption *faceHDEnable;
        MTToKidModuleOption *toKidEnable;
        MTImageRecognitionModuleOption *imageRecognitionEnable;
        MTAnchorGenerationModuleOption *anchorGenerationEnable;
        MTSkinMicroModuleOption *skinMicroEnable;
        MTLandmarkModuleOption *landmarkEnable;
        MTRemoveWatermarkModuleOption *removeWatermarkEnable;
        MTImageDetectionModuleOption *imageDetectionEnable;
        MTDL3DModuleOption *dL3DEnable;
        MTTeethModuleOption *teethEnable;
        MTEveSkinModuleOption *eveSkinEnable;
        MTSkinBCCModuleOption *skinBCCEnable;
		MT3DFaceModuleOption *threeDFaceEnable;
        MTBodyInOneModuleOption *bodyInOneEnable; 
        MTWrinkleDetectionModuleOption *wrinkleDetectionEnable;
        MTDenseHairModuleOption *denseHairEnable;
        MTCgStyleModuleOption *cgStyleEnable;
        MTFoodStyleModuleOption *foodStyleEnable;
        MTSmileModuleOption *smileEnable;
        MTEveQualityModuleOption *eveQualityEnable;
        MTFaceAnalysisXModuleOption *faceAnalysisXEnable;
        MTKiev3DMakeModuleOption *kiev3DMakeEnable;
        MTSkinToneMappingModuleOption *skinToneMappingEnable;
        MTEyeSegmentModuleOption *eyeSegmentEnable;
        MTVideoStabilizationModuleOption *videoStabilizationEnable;
        MTVideoRecognitionModuleOption *videoRecognitionEnable;
        MTHighDofEyelidModuleOption *highDofEyelidEnable;
        MTEyelidRealtimeModuleOption *eyelidRealtimeEnable;
        MTVideoOptimizerModuleOption *videoOptimizerEnable;
        MTFaceBlitModuleOption *faceBlitEnable;
        MTAIKitModuleOption *aiKitEnable;
        MTSkinARModuleOption *skinAREnable;
        MTNoseBlendModuleOption *noseBlendEnable;
        MTHuman3dModuleOption *human3dEnable;
        MTEyelidImageModuleOption *eyelidImageEnable;
        MTNevusDetectionModuleOption *nevusDetectionEnable;
        MTEveAutoSkinColorModuleOption *eveAutoSkinColorEnable;
        MTEvePreDetectModuleOption *evePreDetectEnable;
        MTDoubleChinFixModuleOption *doubleChinFixEnable;
        MTHairGrouthModuleOption *hairGrouthEnable;
        MTPortraitDetectionModuleOption *portraitDetectionEnable;
        MTHairDyeModuleOption *hairDyeEnable;
        MTRTTeethRetouchModuleOption *rtTeethRetouchEnable;
        MTRestoreTeethModuleOption *restoreTeethEnable;
        MTHairStraightModuleOption *hairStraightEnable;
        MTHairFluffyModuleOption *hairFluffyEnable;
        MTHairCurlyModuleOption *hairCurlyEnable;
//__END_OF_OPTION_DECLARE__

    public:
        MTVector<MTVector<MTPoint2f> > face_points_list; // 外部传入人脸点，需要与输入图片方向一致
        MTVector<MTVector<MTPoint2f> > neck_points_list; // 外部传入脖子点，需要与输入图片方向一致
        MTVector<MTVector<MTPoint2f> > left_ear_points_list; // 外部传入的左耳朵点
        MTVector<MTVector<MTPoint2f> > right_ear_points_list; // 外部传入的右耳朵点
        MTVector<MTVector<MTPoint2f> > head_points_list; // 外部传入的头部点
        MTVector<MTVector<float> > visibility; // 外部传入人脸可见性
        MTVector<int> face_id_list; // 外部传入人脸ID
        int face_num;   // 图片人脸总个数
        int nImageWidth;  // 外部传入图像的宽
        int nImageHeight; // 外部传入图像的高
        int nImageOrientation = 1; // 外部传入图像的exif方向
        MTAiEngineImage hair_mask; // 外部传入头发分割mask
        MTAiEngineImage halfBody_mask; // 外部传入半身分割mask
        MTAiEngineImage skin_mask; //外部传入皮肤分割mask
        MTAiEngineImage faceContour_mask; //外部传入人脸轮廓分割mask
        MTAiEngineImage inpainting_mask; // 外部传入实例分割mask 背景填充依赖实例分割mask
        MTVector<int> face_age_list; // 外部传入人脸年龄
        MTVector<MTRect2f> face_rect_list; // 外部传入人脸框（注意：如果是用于人脸检测还需要传入rollAngle）
        MTVector<float> pitch_angle_list; // 外部传入俯仰角
        MTVector<float> yaw_angle_list; // 外部yaw角
        MTVector<float> roll_angle_list; // 外部传入水平转角
        MTVector<int> face_gender_list;  // 外部传入的人脸性别
        MTVector<int> face_race_list;  // 外部传入的人种
        MTRect_<int> watermark_rect; // 外部传入的水印框，非归一化
        MTVector<MTAiEngineImage> mouth_masks; // 外部传入人脸的嘴唇mask
        MTVector<MTVector<float> > mask_matrixs; // 外部传入嘴唇mask对应矩阵
        MTVector<MTAiEngineImage> facial_skin_masks; // 外部传入五官分割mask
        MTVector<MTAiEngineImage> face_masks; // 外部传入人脸的人脸mask
        MTVector<MTVector<float> > face_mask_matrixs; // 外部传入人脸mask对应矩阵

        MTAiEngineImage faceblit_ori_img;        // 外部传入风格迁移原图
        MTAiEngineImage faceblit_parsing_mask;   // 外部传入风格迁移全脸分割图
        MTVector<MTPoint2f> faceblit_points;     // 外部传入风格迁移原图人脸点
        MTVector<MTPoint3f> segmentation_points;     // 万物抠图或SAM分割点
        MTAiEngineImage segmentation_pre_mask;   //万物分割外部传入PreMask
        MTAiEngineImage instance_seg_rgba;       //实例分割RGBA图()
        MTAiEngineImage half_body_mask;          //外部传入半身分割Mask(一通道)
        MTAiEngineImage space_depth_mask;        //外部传入深度信息Mask(一通道)
        MTAiEngineImage rgba_image;             //拍后模块外部传入rgba图像
        MTAiEngineImage realtime_pre_mask;   //实时模块外部传入PreMask
        float deep_blur_k = 9999.0; // 深度虚化参数:虚化程度系数，越大则虚化程度越高，一般取值在[1.000,10.000]之间
        float deep_blur_disp_focus = 9999.0; // 深度虚化参数:对焦位置在输入视差图上的像素值，用于控制焦距的远近，范围在[0.000,1.000]之间
        float model_scale_ = 1.;            //缩放比例，默认为1,取值在(0,1]之间,eg:1为原尺寸,0.5为缩放一半
        bool is_use_block_ = true;          //是否使用分块，默认使用分块, false为关闭分块
        bool is_only_output_alpha = true;   //是否只输出alpha,默认只输出alpha
        MTAiEngineImage matting_alpha;      //matting alpha传入
        bool is_get_matting_fgr = false;    //是否获取matting的fgr
        bool is_body_relighting_postprocess = false;    //是否进行身体重塑后处理
        MTAiEngineImage body_mask;      //输入人像mask,一通道,0-255
        MTAiEngineImage depth_mask;      //输入深度估计mask,一通道,0-255
        MTAiEngineImage normal_map;      //输入法线图,四通道rgba,0-255
        bool is_background_blur_postprocess = false;    //post-process for BackgroundBlur
        bool m_bQuoteFrame = false;                     //是否引用外部的帧的Image数据

    private:
        MTVector<MTAiEngineOption*> m_vecOption;
    
    };

}

#endif // MTAIENGINE_MTMODULE_ENABLE_OPTION_H
