#ifndef _MTSUB_AIBEAUTY_H_
#define _MTSUB_AIBEAUTY_H_

#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/Common/MTAiEngineImage.h>

#include <cstddef>
#include <stdint.h>

#ifdef __cplusplus
extern "C"
#endif
{
    // 加载模型跑的计算数据类型精度
    enum MTSubAIBeautyDataType
    {
        AIBEAUTY_DATA_TYPE_FLOAT = 0,
        AIBEAUTY_DATA_TYPE_FLOAT16 = 1,
        AIBEAUTY_DATA_TYPE_BFLOAT16 = 2,
        AIBEAUTY_DATA_TYPE_INT8 = 3,
        AIBEAUTY_DATA_TYPE_UINT8 = 4,
    };

    // 加载模型跑的计算模式
    enum MTSubAIBeautyDeviceType
    {
        AIBEAUTY_DEVICE_CPU = 0,
        AIBEAUTY_DEVICE_CPU_C4 = 1,
        AIBEAUTY_DEVICE_OPENGL = 2,
        AIBEAUTY_DEVICE_OPENCL = 3,
        AIBEAUTY_DEVICE_CUDA = 4,
        AIBEAUTY_DEVICE_HEXAGON = 5,
        AIBEAUTY_DEVICE_METAL = 6,
        AIBEAUTY_DEVICE_WEBGL = 7,
        AIBEAUTY_DEVICE_GLCS = 8,
        AIBEAUTY_DEVICE_HIAI_NPU = 9,
        AIBEAUTY_DEVICE_COREML = 10,
        AIBEAUTY_DEVICE_OPENVINO = 11,
        AIBEAUTY_DEVICE_AUTO = 12,
    };

    // enum MTSubAIBeautyModelOption
    // {
    //     AIBEAUTY_SKIN_DENOISE =0,   // zootopia_skin.bin
    //     AIBEAUTY_SKIN_LOW_DENOISE,  // zootopia_skin_low.bin
    //     AIBEAUTY_DENOISE,         // zootopia.bin
    //     AIBEAUTY_FACE_ALPHA_MASK,   // mad_max1.bin
    // };

    enum MTSubAIBeautyGender {
        AIBEAUTY_GENDER_MALE        = 0,    // 男
        AIBEAUTY_GENDER_FEMALE      = 1,    // 女
        AIBEAUTY_GENDER_CHILD       = 2,    // 小孩
    };


	typedef struct mtlabai_sub_AI_beauty_handle* mtlabai_sub_AI_beauty_handle_t;


	/**
	* \brief 创建底层SDK句柄
	* \return handle 算法句柄
	*/
	MTAIENGINE_API mtlabai_sub_AI_beauty_handle_t mtlabai_sub_AI_beauty_create_handle();

	/**
	* \brief 销毁底层SDK句柄
	* \param[in] handle 算法句柄
	*/
	MTAIENGINE_API void mtlabai_sub_AI_beauty_destroy_handle(mtlabai_sub_AI_beauty_handle_t *handle);

    /**
     * pModelDir：模型根目录/AiModel/
     * deviceType：
            使用天枢模型时，该变量不生效；
            使用本地模型时，设置CORLORTONING_DEVICE_COREML使用coreml，其他内部一律转成CORLORTONING_DEVICE_CPU_C4
     * dataType 如果使用算法默认值，则设置为DATA_TYPE_FLOAT
     * asset：android平台assetmanage
    */
    MTAIENGINE_API bool mtlabai_sub_AI_beauty_load_models(mtlabai_sub_AI_beauty_handle_t handle, const char* modelDir,
                            MTSubAIBeautyDeviceType deviceType, MTSubAIBeautyDataType dataType, void *asset);

    // MTAIENGINE_API bool mtlabai_sub_AI_beauty_load_models_for_data(mtlabai_sub_AI_beauty_handle_t handle,
    //                         const char* modelData, const int dataSize,
    //                         MTSubAIBeautyDeviceType deviceType, MTSubAIBeautyDataType dataType, 
    //                         MTSubAIBeautyModelOption modelOption, void *asset);

    /**
     * 设置非皮肤区域降噪程度 [0-100]
     */
    MTAIENGINE_API bool mtlabai_sub_AI_beauty_set_intensity_denoise_alpha(mtlabai_sub_AI_beauty_handle_t handle, const int value);
    
    /**
     * 设置皮肤区域降噪程度 [0-100]
     */
    MTAIENGINE_API bool mtlabai_sub_AI_beauty_set_denoise_skin_intensity(mtlabai_sub_AI_beauty_handle_t handle, const int value);

    /**
     * 单通道皮肤mask
     */
    MTAIENGINE_API bool mtlabai_sub_AI_beauty_set_skin_mask(mtlabai_sub_AI_beauty_handle_t handle, unsigned char* pImage,
                                                            const int nWidth, const int nHeight);


    /**
     * pImage：原始图
     * nWidth：宽
     * nHeight：高
     * pnFaceGender：性别数组，和人脸点顺序对应（枚举：MTSubAIBeautyGender）
     * pfFacePoints：人脸点
     * nFace：人脸个数
     * nFacePoints：每个人脸点的个数
     */
    MTAIENGINE_API const unsigned char* mtlabai_sub_AI_beauty_run(mtlabai_sub_AI_beauty_handle_t handle,
                                                unsigned char* pRGBAImage,
                                                const int nWidth, const int nHeight,
                                                const int* pnFaceGender,                // 2代表小孩(小孩年龄由外部定义)，1表示女，0表示男
                                                const float* pfFacePoints,
                                                const int nFace,
                                                const int nFacePoints);

    /**
     * 开启算法日志
    */
    MTAIENGINE_API bool mtlabai_sub_AI_beauty_set_log(mtlabai_sub_AI_beauty_handle_t handle, bool flag);

}

#endif