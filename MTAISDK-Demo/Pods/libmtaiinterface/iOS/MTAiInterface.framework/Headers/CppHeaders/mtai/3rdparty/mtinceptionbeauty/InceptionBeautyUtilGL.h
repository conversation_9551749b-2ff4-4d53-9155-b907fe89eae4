/*****************************************************************
* GL接口
* Copyright (c) 2021 MEITU. All rights reserved.
*
* @version: 0.0.3.0
*
* @author:  gary_zou
*
* @email:  <EMAIL>
*
* @date: 2021-08-09
*
* @note: 目前实现功能：匀肤。
*
* @usage: 对外接口。包括gl加载模型和调用接口。
*
* @change:
*
******************************************************************/

#pragma once
#ifndef _ADVANCED_INCEPTION_BEAUTY_UTIL_GL_H_
#define _ADVANCED_INCEPTION_BEAUTY_UTIL_GL_H_

#include "InceptionBeautyUtil.h"

#ifndef GLuint
typedef unsigned int GLuint;
#endif

#ifndef GLint
typedef int GLint;
#endif

#ifndef GLenum
typedef unsigned int GLenum;
#endif

namespace mtcvlite {
class Mat;
}

namespace mtai {
namespace mtdlbeauty
{
    class DL_INCEPTION_BEAUTY_EXPORT InceptionBeautyUtilGL : public InceptionBeautyUtil
    {
    public:
        InceptionBeautyUtilGL();
        virtual ~InceptionBeautyUtilGL();
        
        InceptionBeautyUtilGL(InceptionBeautyUtilGL& rhs) = delete;
        InceptionBeautyUtilGL& operator=(InceptionBeautyUtilGL& rhs) = delete;
        
        virtual void Init();
        
        /*
        @param pModelPath:      模型路径
        @param nProcType:       处理类型:                      对应模型：
                                IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                                IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                                IB_BALANCE_SKIN_BEST      ->  snoopy_best
        @param nDeviceType:     设备类型:
                                IB_DEVICE_CPU(该函数禁用cpu)
                                IB_DEVICE_CUDA(该函数禁用cuda)
                                IB_DEVICE_GL
                                IB_DEVICE_GL_MODEL_CPU
        */
        virtual bool LoadModels(const char* pModelPath,
                                const int nProcType = InceptionBeautyUtil::IB_BALANCE_SKIN_PHOTO,
                                const int nDeviceType = InceptionBeautyUtil::IB_DEVICE_GL);
        
        /*
        @param pModelPath:      模型路径
        @param bPath2Fule       true:通过pModelPath可以定位到具体的模型文件，此时nProcType将失效。
                                false: pModelPath只定位到模型所在文件夹，需通过nProcType指定模型文件
        @param nProcType:       处理类型:                      对应模型：
                                IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                                IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                                IB_BALANCE_SKIN_BEST      ->  snoopy_best
        @param nDeviceType:     设备类型:
                                IB_DEVICE_CPU
                                IB_DEVICE_CUDA
                                IB_DEVICE_GL(该函数禁用gl)
                                IB_DEVICE_GL_MODEL_CPU(该函数禁用gl)
        */
        virtual bool LoadModels(const char* pModelPath,
                                const bool bPath2File,
                                const int nProcType = InceptionBeautyUtil::IB_BALANCE_SKIN_PHOTO,
                                const int nDeviceType = InceptionBeautyUtil::IB_DEVICE_GL);
        /*
        @param pModelData:      模型数据流
        @param nModelDataSize:  模型数据流尺寸
        @param nProcType:       处理类型:                      对应模型：
                                IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                                IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                                IB_BALANCE_SKIN_BEST      ->  snoopy_best
        @param nDeviceType:     设备类型:
                                IB_DEVICE_CPU(该函数禁用cpu)
                                IB_DEVICE_CUDA(该函数禁用cuda)
                                IB_DEVICE_GL
                                IB_DEVICE_GL_MODEL_CPU
        */
        virtual bool LoadModelsData(const char* pModelData, const long lModelDataSize,
                                    const int nProcType = InceptionBeautyUtil::IB_BALANCE_SKIN_PHOTO,
                                    const int nDeviceType = InceptionBeautyUtil::IB_DEVICE_GL);
        
        virtual const unsigned char* GetMatIn(int& w, int& h) const;
        virtual const unsigned char* GetMatOut(int& w, int& h) const;
        
        virtual void InitGL(char* pShaderFile = 0, const int nTextureFloatBits = 32, const bool bEnableVertexFlag = false);

        virtual void ExitGL();
        
        virtual void SetGLContext(void* pGLContext);
        
        virtual void ClearNet();

        virtual GLuint GetFbo() const;
        virtual void ReadPixels(const GLuint nTextureID, unsigned char* data, const int nWidth, const int nHeight) const;

        /*
         @brief: 调用函数，模型前向和其他流程都跑gl3.0
         @param  nInputTextureID    [in]    待处理图像纹理ID
         @param  nOutputTextureID   [out]   处理完毕图像纹理ID
         @param  nWidth             [in]    图像宽
         @param  nHeight            [in]    图像高
         @param  pfFacePoints       [in]    图像非归一化人脸点
         @param  nFace              [in]    人脸数
         @param  nFacePoints        [in]    人脸点个数
         @param  bBindTextureToFbo  [in]    是否将纹理绑定到Fbo
         @param  bFuse              [in]    是否估算人脸区域并进行alpha融合
         */
        virtual int RunGL(const GLuint nInputTextureID,
                          const GLuint nOutputTextureID,
                          const int nWidth, const int nHeight,
                          const float* pfFacePoints,
                          const int nFace, const int nFacePoints,
                          const bool bBindTextureToFbo = false,
                          const bool bFuse = false);
        
        /*
         @brief: 在纯gl模式下无法跑coreml-gl混合模式，该函数返回错误信息
         */
        virtual int RunCoreML(const GLuint nInputTextureID,
                               const GLuint nOutputTextureID,
                               void* pInputCVPixelBuffer,
                               void* pOutputCVPixelBuffer,
                               const int nWidth, const int nHeight,
                               const float* pfFacePoints,
                               const int nFace, const int nFacePoints,
                               const bool bBindTextureToFbo = false,
                               const bool bFuse = false);
        
        //mtcvlite::Mat GetGLResult1();
        //mtcvlite::Mat GetGLResult2();
        
    protected:
        

    };

}// end namespace mtdlbeauty
}// end namespace mtai
#endif // _INCEPTION_BEAUTY_UTIL_GL_H_
