#ifndef _MT_SUB_LOW_POLY_H_
#define _MT_SUB_LOW_POLY_H_

#include <cstddef>
#include <mtai/Common/MTAiEngineMacro.h>


#ifndef uint8_t
    typedef unsigned char uint8_t;
#endif

#ifndef uint32_t
typedef unsigned int uint32_t;
#endif

extern "C" {
    typedef struct mtlabai_sub_low_poly_handle_t mtlabai_sub_low_poly_handle_t;

    /**
     * \ingroup LowPoly
     * \brief 处理参数
     */
    struct mtlabai_sub_low_poly_process_parameter {
        float mtlabai_sub_low_poly_eta                = 0.02f;  //!< η比例，用来控制点密度
        int mtlabai_sub_low_poly_ed_proposal_thresh   = 36;     //!< ED控制参数
        int mtlabai_sub_low_poly_ed_anchor_interval   = 4;      //!< ED控制参数
        int mtlabai_sub_low_poly_ed_anchor_thresh     = 8;      //!< ED控制参数
        int mtlabai_sub_low_poly_minimum_outer_points = 500;    //!< 非边缘关键点的最少数量
        int mtlabai_sub_low_poly_edge_thresh          = 0;      //!< 边缘长度阈值
        float mtlabai_sub_low_poly_rdp_epsilon        = 2.f;    //!< RDP精度，用来控制边缘的简化程度
        int mtlabai_sub_low_poly_iterations           = 10;     //!< 迭代次数
        float mtlabai_sub_low_poly_scale              = 1.f;    //!< 缩放处理倍数
        uint32_t mtlabai_sub_low_poly_seed            = 0;      //!< 随机种子
    };

    /**
    * \brief 构造低多边形效果处理对象
    */
    MTAIENGINE_API mtlabai_sub_low_poly_handle_t *mtlabai_sub_low_poly_handle_create();

    /**
    * \brief 释放handle
    *
    */
    MTAIENGINE_API void mtlabai_sub_low_poly_handle_release(mtlabai_sub_low_poly_handle_t **handle);

/**
    * \brief 执行算法
    * \param[in] src_stride 待处理图像的stride(pitch，行字节长)
    * \param[in] src 待处理图像的内存地址，RGBA格式
    * \param[in] dst_stride 输出图像的stride(pitch，行字节长)
    * \param[in,out] dst 输出图像的内存地址，RGBA格式
    * \param[in] cols 待处理图像的宽度
    * \param[in] rows 待处理图像的高度
    * \return 执行状态
    */
    MTAIENGINE_API bool mtlabai_sub_low_poly_process(mtlabai_sub_low_poly_handle_t *handle, size_t src_stride, const uint8_t* src, size_t dst_stride, uint8_t* dst, int cols, int rows);

    /**
    * \brief 获取处理参数
    * \param[out] parameter 处理参数
    * \return 执行状态
    */
    MTAIENGINE_API bool mtlabai_sub_low_poly_get_process_parameter(mtlabai_sub_low_poly_handle_t *handle, mtlabai_sub_low_poly_process_parameter& parameter);

    /**
    * \brief 设置处理参数
    * \param[in] parameter 处理参数
    * \return 执行状态
    */
    MTAIENGINE_API bool mtlabai_sub_low_poly_set_process_parameter(mtlabai_sub_low_poly_handle_t *handle, const mtlabai_sub_low_poly_process_parameter& parameter);

}

#endif