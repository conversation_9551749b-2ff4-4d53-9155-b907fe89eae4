#pragma once

#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"


namespace mtai
{
	struct MTAIENGINE_API DL3DNet
	{
		/// @brief The indentity[50].
		float fIndentity[50]; //脸型系数，0到1的float，来组合
							  /// @brief The expression[47].
		float fExpression[47]; //表情系数，0到1的float，来组合
							   /// @brief The euler[3].
		float fEuler[3]; //欧拉弧度角 x_点头, y_摇头, z_歪头 方向
						 /// @brief The glmvp[16].
		float fGLMVP[16]; //GL投影矩阵
						  /// @brief The width.
		int nWidth; //图像宽
					/// @brief The height.
		int nHeight; //图像高
					 /// @brief The expression flag, 0->false 1->true
					 //"UPEyeBrown", "MouthRight", "MouthLeft", "Anger", "OMouth", "AMouth", "PuckerMouth",
					 //"PurseLips" ,"AirBlow" ,"ELClose" ,"ERColor" ,"TurnLeft" ,"TurnRight"
		int fExpressionFlag[47];  //表情标志位，提供一些表情识别,only 13, 0 and 1
								  /// @brief blendshape rigging
		float fExpressionBlendshape[47]; //表情驱动系数，用于表情驱动

										 /// @brief Rotation from template to model, column-majored.
		float fRotation[9]; //旋转矩阵 3x3
							/// @brief Translation from template to model.
		float fTranslation[3]; //世界坐标下的平移矩阵
							   /// @brief Projection from model to view port(input image), column-majored.
		float fProjection[9];  //投影矩阵
	};

	struct MTAIENGINE_API DL3DMesh
	{
		DL3DMesh();
		DL3DMesh(const DL3DMesh &x);
		const DL3DMesh &operator=(const DL3DMesh &x);
		~DL3DMesh();

		/// @brief The vertices.
		float* pVertices = nullptr; //[nVertex*3]  //顶点坐标
								/// @brief The neutral face.
		float* pNeutralFace = nullptr; //[nVertex*3] //中性脸坐标
								   /// @brief The vertices texture.
		float* pVerticesTexture = nullptr; //[nVertex*2] //纹理坐标
									   /// @brief The vertices normal.
		float* pVerticesNormal = nullptr; //[nVertex*3]  //法线坐标
									  /// @brief The vertex.
		int nVertex = 0;  //顶点个数
					  /// @brief The triangles.
		unsigned short* pTriangles = nullptr; //[nTriangle*3] //三角形
										  /// @brief The triangle. 
		int nTriangle = 0; //三角形个数
					   /// @brief The expression matrix 47 to 25
		float* fBs47To25 = 0; //47转25矩阵
								/// @brief The landmarks 86.
		int* landmarks86 = nullptr; //86个3D点索引
	};

    struct MTAIENGINE_API MTDL3D {
        void Print() const ;
		
		int nFaceID;
		DL3DNet dl3dNetResult;
		DL3DMesh dl3dMeshResult;
    };

    struct MTAIENGINE_API MTDL3DResult
    {
        void Print() const ;

        bool                    normalize   = true;                 ///< 是否归一化数据		//TODO
        int                     orientation = 1;                    ///< 数据方向
		float                   runTime     = 0.0;          		///< 运行耗时 单位ms
        MTSize_<int>            size        = MTSize_<int>(1, 1);   ///< 数据size

        MTVector<MTDL3D>    dL3Ds;                          		///< dl3d的数据
    };

    /**
     * 检测结果转化
     * 需要在dst中先填充参数
     *
     * @param src   输入
     * @param dst   输出
     * @return      结果
     */
    MTAIENGINE_API
    MTAiEngineRet ConvertDL3DResult(const MTDL3DResult &src, MTDL3DResult &dst);

}
