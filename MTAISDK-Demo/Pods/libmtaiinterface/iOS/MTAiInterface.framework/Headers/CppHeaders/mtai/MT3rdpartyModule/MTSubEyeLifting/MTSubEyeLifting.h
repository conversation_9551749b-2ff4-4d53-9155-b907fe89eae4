#ifndef _MTSUB_EYELIFTING_H_
#define _MTSUB_EYELIFTING_H_

#include <mtai/Common/MTAiEngineMacro.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C"
#endif
{
enum MTSubEyeLiftingDeviceType {
    MTSubEyeLifting_AUTO = 0,  //!< manis::DEVICE_AUTO
    MTSubEyeLifting_CPU,       //!< manis::DEVICE_CPU
    MTSubEyeLifting_OPENGL,    //!< manis::DEVICE_OPENGL
    MTSubEyeLifting_OPENCL,    //!< manis::DEVICE_OPENCL
    MTSubEyeLifting_CUDA,      //!< manis::DEVICE_CUDA
    MTSubEyeLifting_HEXAGON,   //!< manis::DEVICE_HEXAGON
    MTSubEyeLifting_METAL,     //!< manis::DEVICE_METAL
    MTSubEyeLifting_WEBGL,     //!< manis::DEVICE_WEBGL
    MTSubEyeLifting_GLCS,      //!< manis::DEVICE_GLCS
    MTSubEyeLifting_HIAI_NPU,  //!< manis::DEVICE_HIAI_NPU
    MTSubEyeLifting_COREML,    //!< manis::DEVICE_COREML
    MTSubEyeLifting_OPENVINO,  //!< manis::DEVICE_OPENVINO
    MTSubEyeLifting_QNN,       //!< manis::DEVICE_QNN
};

enum MTSubEyeLiftingModelType {
    MTSubEyeLifting_SEGMENT = 0,     //!< 上眼睑提拉的分割模型
    MTSubEyeLifting_INPAINTING = 1,  //!< 上眼睑提拉的修复模型
};

typedef struct MTSubEyeLiftingPrepareParameter {
    int num_of_faces = 0;                     //!< 人脸个数
    const float** faces_landmarks = nullptr;  //!< 原图宽高人脸点
} MTSubEyeLiftingPrepareParameter;

typedef struct MTSubEyeLiftingProcessParameter {
    int face_id = -1;              //!< 人脸Id
    float left_eye_weight = 0.f;   //!< 左眼权重
    float right_eye_weight = 0.f;  //!< 右眼权重
} MTSubEyeLiftingProcessParameter;

typedef struct mtlabai_sub_eyelifting_handle* mtlabai_sub_eyelifting_handle_t;

/**
 * \brief 第0步:创建底层SDK句柄
 * \return handle 算法句柄
 */
MTAIENGINE_API mtlabai_sub_eyelifting_handle_t mtlabai_sub_eyelifting_create_handle();

/**
 * \brief 销毁底层SDK句柄,最后调用
 * \param[in] handle 算法句柄
 * \return 执行状态 -1失败 0成功
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_release_handle(
    mtlabai_sub_eyelifting_handle_t* pHandle);

/**
 * \brief 第一步:初始化上眼睑提拉处理对象
 * \param[in] handle 算法句柄
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_init(mtlabai_sub_eyelifting_handle_t handle);

/**
 * \brief 第二步:加载上眼睑提拉模型（CoreML模型只能通过路径形式加载）
 * \param[in] handle 算法句柄
 * \param[in] modelSize 模型大小
 * \param[in] modelData 模型数据
 * \param[in] deviceType 设备类型
 * 分割模型仅支持CPU模式；修复模型支持CPU、OpenGL和CoreML模式
 * \param[in] modelType 模型类型
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_load_model_data(
    mtlabai_sub_eyelifting_handle_t handle, uint32_t modelSize, const uint8_t* modelData,
    MTSubEyeLiftingDeviceType deviceType, MTSubEyeLiftingModelType modelType);

/**
 * \brief 第二步:加载上眼睑提拉模型
 * \param[in] handle 算法句柄
 * \param[in] modelPath 模型路径
 * \param[in] deviceType 设备类型
 * \param[in] modelType 模型类型
 * 分割模型仅支持CPU模式；修复模型支持CPU、OpenGL和CoreML模式
 * \param[in] assetManager
 * assetManager 模型放在安卓的assert路径下需要设置，否则传空
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_load_model_path(
    mtlabai_sub_eyelifting_handle_t handle, const char* modelPath,
    MTSubEyeLiftingDeviceType deviceType, MTSubEyeLiftingModelType modelType,
    void* assetManager);

/**
 * \brief 第二步:通过天枢加载上眼睑提拉模型
 * \param[in] handle 算法句柄
 * \param[in] deviceType 设备类型
 * \param[in] modelType 模型类型
 * 分割模型仅支持CPU模式；修复模型支持CPU、OpenGL和CoreML模式
 * \param[in] assetManager
 * assetManager 模型放在安卓的assert路径下需要设置，否则传空
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_load_model_Aidispatch(
    mtlabai_sub_eyelifting_handle_t handle, MTSubEyeLiftingDeviceType deviceType,
    MTSubEyeLiftingModelType modelType, void* assetManager);

/**
 * \brief 第二步:加载上眼睑提拉的shader文件
 * \param[in] handle 算法句柄
 * \param[in] modelSize 模型大小
 * \param[in] modelData 模型数据
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_load_shader_data(
    mtlabai_sub_eyelifting_handle_t handle, uint32_t modelSize, const uint8_t* modelData);

/**
 * \brief 第二步:加载上眼睑提拉的shader文件
 * \param[in] handle 算法句柄
 * \param[in] shaderPath 模型路径
 * \param[in] assetManager assetManager
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_load_shader_path(
    mtlabai_sub_eyelifting_handle_t handle, const char* shaderPath, void* assetManager);

/**
 * \brief 第二步:通过天枢加载上眼睑提拉的shader文件
 * \param[in] handle 算法句柄
 * \param[in] assetManager assetManager
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_load_shader_Aidispatch(
    mtlabai_sub_eyelifting_handle_t handle, void* assetManager);

/**
 * \brief 第三步:预处理
 * \param[in] handle 算法句柄
 * \param[in] src 待处理图像的内存地址，RGBA格式
 * \param[in] srcStride 待处理图像的stride(pitch，行字节数)
 * \param[in] cols 待处理图像的宽度
 * \param[in] rows 待处理图像的高度
 * \param[in] parameter 预处理参数
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_prepare(
    mtlabai_sub_eyelifting_handle_t handle, const uint8_t* src, size_t srcStride,
    int cols, int rows, const MTSubEyeLiftingPrepareParameter parameter);

/**
 * \brief 第三步:执行算法
 * \param[in] handle 算法句柄
 * \param[in] parameters 上眼睑提拉处理参数
 * \param[in] parametersNum 上眼睑提拉处理参数个数
 * \param[in,out] dst 输出图像的内存地址，RGBA格式，从原图内存拷贝
 * \param[in] dstStride 输出图像的stride(pitch，行字节数)
 * \return 执行状态
 */
MTAIENGINE_API int mtlabai_sub_eyelifting_process(
    mtlabai_sub_eyelifting_handle_t handle,
    const MTSubEyeLiftingProcessParameter* parameters, int parametersNum, uint8_t* dst,
    size_t dstStride);
}

#endif  // _MTSUB_EYELIFTING_H_