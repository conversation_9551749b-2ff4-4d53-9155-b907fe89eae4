#pragma once
#include <mtai/Common/MTAiEngineOption.h>
#include <mtai/Common/MTVector.h>

namespace mtai
{
    //注：图片模式支持多人检测，视频模式只支持单人检测
    enum MTBodyInOneEnableEnum : uint64_t {
        MT_BODYINONE_ENABLE_NONE            = 0x0,        // 无检测
        MT_BODYINONE_ENABLE_TIME            = MT_MASK(0), // 获取运行耗时
        MT_BODYINONE_ENABLE_BOX             = MT_MASK(1), // 人体框检测
        MT_BODYINONE_ENABLE_POSE            = MT_MASK(2), // 肢体检测
        MT_BODYINONE_ENABLE_CONTOUR         = MT_MASK(3), // 轮廓点检测
        MT_BODYINONE_ENABLE_SHOULDER        = MT_MASK(4), // 肩膀点检测
        MT_BODYINONE_ENABLE_NECK            = MT_MASK(5), // 脖子点检测, 不依赖MT_BODYINONE_ENABLE_BOX
        MT_BODYINONE_ENABLE_BREAST          = MT_MASK(6), // 胸部点检测, 不依赖MT_BODYINONE_ENABLE_BOX
        MT_BODYINONE_ENABLE_RT_MULTI        = MT_MASK(7), // 实时多人

    };

    enum MTBodyInOneModelType {
        MT_BODYINONE_MODEL_TPYE_LARGE  = 0,
        MT_BODYINONE_MODEL_TPYE_MIDDLE = 1,
        MT_BODYINONE_MODEL_TPYE_SMALL  = 2,
    };

    enum MTBodyInOneModelMode{
        MT_BODYINONE_MODEL_MODE_AUTO   = 0,
        MT_BODYINONE_MODEL_MODE_COREML = 1,
        MT_BODYINONE_MODEL_MODE_NPU    = 2,
        MT_BODYINONE_MODEL_MODE_NCHWC4 = 3,
        MT_BODYINONE_MODEL_MODE_QNN    = 4,
        MT_BODYINONE_MODEL_MODE_APU    = 5,
    };

    enum MTBodyInOneAppScene{
        MT_BODYINONE_APP_SCENE_IMAGE   = 0,
        MT_BODYINONE_APP_SCENE_VIDEO   = 1,
        MT_BODYINONE_APP_SCENE_NONE    = 2,
    };

    class MTAIENGINE_API MTBodyInOneModuleOption : public MTAiEngineOption{

    public:

        // 单独设置某个开关
        void SetSigEnaOption(MTBodyInOneEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MTBodyInOneEnableEnum flag);//GetSingleEnableOptionStatus

        // 模块类型
        MTAiEngineType MuduleType() override;

        std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;

        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

    public:
        /////////////////////////以下是RegisterModule时才有效的接口(参数)/////////////////////////
        int smoothRadius           = 5;       ///< int         :   [实时5; 拍后0]      平滑半径，值越大轮廓点平滑力度越大，点越稳，但是效果滞后感越明显（拍后设置为0）
        float smoothSigma          = 15.0f;   ///< float       :   [15.0]             高斯函数的参数方差，值越大轮廓点的平滑力度越大，点越稳，但是效果滞后感越明显
        int detectPeriod           = 10;      ///< int         :   [实时10; 拍后0]     检测框间隔的帧数
        float boxRatio             = 1.1f;    ///< float       :   [1.1]              检测框扩大的比例
        int smoothKernelSize       = 5;       ///< int         :   [5]                heatmap转换坐标时的半径
        int boxSmoothLenth         = 5;       ///< int         :   [5]                检测框平滑长度。值越大检测框越稳，但是可能出现效果滞后，和当前实际检测效果不匹配
        float boxSmoothSigma       = 5.0f;    ///< float       :   [5.0]              高斯函数的方差，值越大检测框越稳，但是可能出现效果滞后，和当前实际检测效果不匹配
        float iouThres             = 0.7f;    ///< float       :   [0.7]              相邻两帧检测框重复程度
        bool multiThread           = true;    ///< bool        :   [true]             是否开启多线程，默认开启(实时)，拍后需设置为false
        bool trackEnable           = true;    ///< bool        :   [true]             是否开启追踪模式，仅实时模式有效，默认开启
        bool enforceSingleBox      = false;   ///< bool        :   [false]            拍后模式是否强制输出单个检测框，默认为false，拍后会输出多个检测框， 设置为true的话，会从多个检测框选择一个输出
        int boxMultiPerson         = 1;       ///<int          :   [1]                控制检测器判断当前图片为多人的阈值，数值1代表只要出现多个人体框就判断为多人场景，其他数值代表需要累积N帧多人才判断为多人
        bool boxInstanceMode       = false;   ///< bool        :   [false]            强制当前帧返回检测结果
        bool poseFastMode          = true;   ///< bool         :   [true]             肢体网络运行模式
        //*用于解决小目标检测问题，提升检测精度，目前提供给server开放平台使用*//
        bool multiScaleInference   = false;   ///< bool        :   [false]            BOX检测是否启用多尺度目标检测
        bool boxGpuMode            = false;   ///< bool        :   [false]            检测框是否开启gpu:cuda模式， true: DeviceType:DEVICE_CUDA, false: DeviceType:DEVICE_CPU(默认)
        float boxConfidence        = 0.9f;    ///< float       :   [0.9]              多尺度目标检测合并检测框
        bool setCudaOpt            = true;    ///< bool        :   [true]             设置cuda模式opt参数
        bool mergeBBox             = false;   ///< bool        :   [false]            设置多人人像框是否进行合并
        //*用于解决小目标检测问题，提升检测精度，目前提供给server开放平台使用*//
        bool cameraMode            = true;    ///< bool        :  [true]              是否使用相机模式
        bool contourUseMainThread  = false;   ///< bool        :   [false]            设置contour是否使用主推理
        bool shoulderUseMainThread = false;   ///< bool        :   [false]            设置shoulder是否使用主推理
        bool neckUseMainThread     = false;   ///< bool        :   [false]            设置neck是否使用主推理
        bool breastUseMainThread   = false;   ///< bool        :   [false]            设置breast是否使用主推理
        bool yoloUseRect           = false;   ///< bool        :   [false]
        bool contourFp16           = true;    ///< bool        :    [true]             设置false时轮廓点检测跑fp32
        bool poseModel33           = false;   ///< bool        :  [false]              是否使用33版本的pose模型，和cameraMode互斥，cameraMode优先
        bool useOpenvino           = false;   ///< bool        :  [true]               是否使用openvino
        int openvinoThreadNum      = 1;       ///< int         :   [1]                openvino线程数
        bool rtMultiMode           = false;   ///< bool        : [false]              是否开启实时多人检测模式
        int  rtMultiMaxNum         = 2;       ///< int         : [2]                  实时多人检测最大个数
        int rtReidInterval         = 5;       ///< int         : [5]                  运行reid模型的频率


        //模型类型设置
        MTBodyInOneModelType     neckModelType     = MT_BODYINONE_MODEL_TPYE_MIDDLE;
        MTBodyInOneModelType     breastModelType   = MT_BODYINONE_MODEL_TPYE_MIDDLE;
        MTBodyInOneModelMode     modelMode         = MT_BODYINONE_MODEL_MODE_AUTO;    //neck和breast都依赖于box_yolo模型，所以只能统一使用模型类型

        MTBodyInOneModelType     poseModelType     = MT_BODYINONE_MODEL_TPYE_MIDDLE;
        MTBodyInOneModelMode     poseModelMode     = MT_BODYINONE_MODEL_MODE_NCHWC4;

        MTBodyInOneModelType     rtMultiModelType    = MT_BODYINONE_MODEL_TPYE_MIDDLE;
        MTBodyInOneModelMode     rtMultiModelMode    = MT_BODYINONE_MODEL_MODE_AUTO;

        MTBodyInOneModelType     rtReidModelType    = MT_BODYINONE_MODEL_TPYE_MIDDLE;
        MTBodyInOneModelMode     rtReidModelMode    = MT_BODYINONE_MODEL_MODE_AUTO;

        MTBodyInOneAppScene      appScene          = MT_BODYINONE_APP_SCENE_NONE;
        /////////////////////////以上是RegisterModule时才有效的接口(参数)/////////////////////////

        /////////////////////////以下是Run时才有效的接口(参数)/////////////////////////
        bool clearBuffer           = false;   ///< bool        :   [false]            清空历史状态，用于画面突变时的情况(比如home切出、前后置摄像头切换)，避免平滑造成点错误，设置为true生效一次
        MTVector<float> boxScoreList;  ///< 外部传入人体框和置信度，5个float，归一化后数值，范围[0-1]，顺序含义 (x, y, width, height, score);
        bool neckUseMask           = false;   ///< bool        :   [false]            设置neck是否使用mask结果
        float neckSmoothSigma      = 3.0f;    ///< float       :   [3.0]              设置neck 平滑系数，设置范围0-10
        float neckSmoothNormalizer = 0.01f;   ///< float       :   [0.01]             设置neck 平滑系数，设置范围0.01-0.05
        float breastSmoothSigma      = 3.0f;    ///< float       :   [3.0]              设置breast 平滑系数，设置范围0-10
        float breastSmoothNormalizer = 0.01f;   ///< float       :   [0.01]             设置breast 平滑系数，设置范围0.01-0.05
        bool imageExpandBox        = false;   ///< bool        :   [false]             图片检测是否使用扩大的检测框

        bool rtMultiTrackReset     = false;   ///< bool        :   [false]            是否重置多人跟踪状态，ID从0重新开始
        bool rtReidMerge           = false;   ///< bool        :   [false]            是否运行合并id的操作

        bool rtMultifinalMerge     = false;
        /////////////////////////以上是Run时才有效的接口(参数)/////////////////////////


    };

    inline MTAiEngineType MTBodyInOneModuleOption::MuduleType() {return MTAiEngineType_BodyInOneModule;}
    inline void MTBodyInOneModuleOption::SetSigEnaOption(MTBodyInOneEnableEnum flag) { enable_option_ |= flag; } //SetSingleEnableOption
    inline bool MTBodyInOneModuleOption::GetSigEnaOptionStatus(MTBodyInOneEnableEnum flag){ return ((enable_option_ & flag) == flag); }
}

