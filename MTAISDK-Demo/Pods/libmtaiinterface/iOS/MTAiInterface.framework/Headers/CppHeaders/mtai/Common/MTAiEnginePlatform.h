//
// Created by wm<PERSON> on 2019/3/10.
//

#ifndef MTAIENGINE_MTMODULEPLATFORM_H
#define MTAIENGINE_MTMODULEPLATFORM_H

// 优先使用CMAKE工程预定义平台
#if defined(CMAKE_PLATFORM_WINDOWS)
    #define MTAIENGINE_PLATFORM_WINDOWS 1
#elif defined(CMAKE_PLATFORM_OSX)
    #define MTAIENGINE_PLATFORM_OSX 1
    #if defined(__aarch64__)
        #define MTAIENGINE_PLATFORM_OSX_ARM64
    #elif defined(__x86_64__)
        #define MTAIENGINE_PLATFORM_OSX_X64
    #endif
#elif defined(CMAKE_PLATFORM_LINUX)
    #define MTAIENGINE_PLATFORM_LINUX 1
#elif defined(CMAKE_PLATFORM_IOS)
    #define MTAIENGINE_PLATFORM_IOS 1
#elif defined(CMAKE_PLATFORM_ANDROID)
    #define MTAIENGINE_PLATFORM_ANDROID 1
#elif defined(_WIN32) || defined(_WIN32_) || defined(WIN32) || defined(_WIN64_) || defined(WIN64) || defined(_WIN64)
    #define MTAIENGINE_PLATFORM_WINDOWS 1
#elif defined(__APPLE__)
    #include <TargetConditionals.h>
    #if TARGET_OS_IPHONE
        #define MTAIENGINE_PLATFORM_IOS 1
    #elif TARGET_OS_OSX
        #define MTAIENGINE_PLATFORM_OSX 1
    #endif
#elif defined(ANDROID) || defined(_ANDROID_)
    #define MTAIENGINE_PLATFORM_ANDROID 1
#elif defined(__linux__)
    #define MTAIENGINE_PLATFORM_LINUX 1
#else
    #define MTAIENGINE_PLATFORM_UNKNOWN 1
#endif

#endif //MTAIENGINE_MTMODULEPLATFORM_H
