//
//Created by <PERSON>ez<PERSON><PERSON> on 2021/07/29.
//
#pragma once

#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"

namespace mtai
{
    struct MTAIENGINE_API MTAIKitResult{
        void Print() const;
        void*                gpuSync     = nullptr;              ///< gpu同步锁，外部不用管这个值
        float runTime = 0.0;                  ///< 运行耗时 单位ms
        bool normalize = true;
        int orientation = 1;
        MTSize_<int> size = MTSize_<int>(1, 1);
        std::string json_str_;
    };

    /**
     * 结果转化
     * 需要在dst中先填充参数
     *
     * @param src   输入
     * @param dst   输出
     * @return      结果
     */
    // MTAIENGINE_API
    // MTAiEngineRet ConvertWrinkleDetectionResult(const MTWrinkleDetectionResult &src, MTWrinkleDetectionResult &dst);

}