//
// Created by IF on 2019/3/12.
//

#ifndef MTAIENGINE_MTANIMALMODULEOPTION_H
#define MTAIENGINE_MTANIMALMODULEOPTION_H

#include <mtai/Common/MTAiEngineOption.h>

namespace mtai
{

    enum MTAnimalEnableEnum : uint64_t{
        MT_ANIMAL_ENABLE_NONE                                          = 0x0, // 无检测
        MT_ANIMAL_ENABLE_ANIMAL                                        = MT_MASK(0), // 开启动物检测
        MT_ANIMAL_ENABLE_TIME                                          = MT_MASK(1),  // 获取运行耗时
    };

    class MTAIENGINE_API MTAnimalModuleOption : public MTAiEngineOption{
    public:

        // 单独设置某个开关
        void SetSigEnaOption(MTAnimalEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MTAnimalEnableEnum flag);//GetSingleEnableOptionStatus


        MTAiEngineType MuduleType() override;

        std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;
    
        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

    };
    inline MTAiEngineType MTAnimalModuleOption::MuduleType() {return MTAiEngineType_AnimalModule;}
    inline void MTAnimalModuleOption::SetSigEnaOption(MTAnimalEnableEnum flag) { enable_option_ |= flag; };//SetSingleEnableOption
    inline bool MTAnimalModuleOption::GetSigEnaOptionStatus(MTAnimalEnableEnum flag){ return ((enable_option_ & flag) == flag); };


}

#endif //MTAIENGINE_MTANIMALMODULEOPTION_H
