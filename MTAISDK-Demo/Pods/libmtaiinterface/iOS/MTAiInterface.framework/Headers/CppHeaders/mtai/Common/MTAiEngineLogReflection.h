#pragma once
#include <mtai/Common/MTAiEngineDefine.h>
#include <mtai/Common/MTAiEngineType.h>
#include <mtai/Common/MTAiEnginePlatform.h>

#if defined(MTAIENGINE_PLATFORM_ANDROID) && defined(NEW_LOG_MESSAGE)
#include <jni.h>
#endif

namespace mtai
{

#if defined(MTAIENGINE_PLATFORM_IOS) || defined(MTAIENGINE_PLATFORM_ANDROID)
    #ifdef NEW_LOG_MESSAGE
        #if defined(MTAIENGINE_PLATFORM_ANDROID)
            MTAIENGINE_API void setLogClassMethod(const char* clsName, const char* methodName = NULL);
            MTAIENGINE_API void set_javavm(JavaVM* jvm);
            MTAIENGINE_API void clear_javavm();
        #else
            MTAIENGINE_API void setLogClassMethod(const char* clsName, const char* methodName = NULL);
        #endif
    #else
        MTAIENGINE_API void setLogClassMethod(const char* clsName, const char* methodName=nullptr);
        MTAIENGINE_API bool appLog(mtai::MTAI_LOG_LEVEL level, const char* tag, const char* content);
    #endif
#else
    MTAIENGINE_API void setLogClassMethod(const char* clsName, const char* methodName) {}
    MTAIENGINE_API bool appLog(mtai::MTAI_LOG_LEVEL level, const char* tag, const char* content) { return false; }
#endif

}
