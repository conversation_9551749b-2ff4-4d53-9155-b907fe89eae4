//
// Created by IF on 2019/3/12.
//

#ifndef MTAIENGINE_MTBODYMODULEOPTION_H
#define MTAIENGINE_MTBODYMODULEOPTION_H

#include <mtai/Common/MTAiEngineDefine.h>
#include <mtai/Common/MTAiEngineOption.h>
#include <mtai/Common/MTVector.h>

namespace mtai
{
    enum MTBodyEnableEnum : uint64_t {
         MT_BODY_ENABLE_NONE                                          = 0x0, // 无检测
         MT_BODY_ENABLE_POSE                                          = MT_MASK(1), // 肢体检测，只支持单人检测
         MT_BODY_ENABLE_CONTOUR                                       = MT_MASK(2), // 轮廓检测，只支持单人检测
         MT_BODY_ENABLE_HUMAN                                         = MT_MASK(3), // 人体框检测，支持多人检测
         MT_BODY_ENABLE_POSE_PHOTO                                    = MT_MASK(4), // 拍后肢体检测
         MT_BODY_ENABLE_TIME                                          = MT_MASK(5), // 获取运行耗时
    };

    struct MTContourKey {
        bool detect_per_frame_ = false;        //(bool,false)   每帧检测，用于MT_BODY_ENABLE_CONTOUR(38)视频处理,需要每帧美型等场景  **init初始化设置有效**
    };

    struct MTAIENGINE_API SmoothParam { //用于MT_BODY_ENABLE_POSE,其他模块不用设置
        float smooth_sigma = 2.0f; //平滑sigma参数，越大越平滑
        int smooth_frames = 5;  //用多少帧做平滑
        int smooth_type = 0; //平滑模式，可选值：0、1。0对大抖动效果较好；1对小抖动效果较好
    };

    struct MTAIENGINE_API ContourThreshold { //用于MT_BODY_ENABLE_CONTOUR，其他模块不用设置
        float boundScore = 0.7f; // 框的阈值
        float pointsScore = 0.2f; // 点的阈值
        int pointsNum = 25; // 大于阈值的点数
    };

    class MTAIENGINE_API MTBodyModuleOption : public MTAiEngineOption{

    public:
        
        // 单独设置某个开关
        void SetSigEnaOption(MTBodyEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MTBodyEnableEnum flag);//GetSingleEnableOptionStatus
        MTAiEngineType MuduleType() override;

        std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;
        
        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

        //轮廓点设置参数，用于MT_BODY_ENABLE_CONTOUR（38）模块
        MTContourKey contourKey_;

        /////////////////////////以下是Register有效的接口(参数)/////////////////////////
        void SetMultiThread(bool isMultiThread); // 设置多线程状态，默认开启
        bool GetMultiThread();
        bool isMultiThread_ = true;
        
        //（MT_BODY_ENABLE_HUMAN模块）最大检测人数阈值 
        int humanMaxNum = 5; 

        // MT_BODY_ENABLE_CONTOUR38 仅用于"轮廓点38方案"的"拍后模式"，设置后不再需要检测框模型，需要配合外部输入box使用
        bool unloadBoxModel_ = false;
        // MT_BODY_ENABLE_CONTOUR38 仅用于"轮廓点38方案"的"拍后模式"，设置后会使用实时模型用于拍后检测
        bool useVideoModelForImage = false;

        /////////////////////////以上是Register有效的接口(参数)/////////////////////////



        /////////////////////////以下是Run时有效的接口(参数)/////////////////////////
        // 设置pose模块的平滑系数，用于MT_BODY_ENABLE_POSE
        void SetSmoothParam(SmoothParam smoothParam);
        SmoothParam GetSmoothParam();
        SmoothParam smoothParam_;

        // contour模块的阈值设置，用于MT_BODY_ENABLE_CONTOUR
        void SetContourThreshold(ContourThreshold contourThreshold);
        ContourThreshold GetContourThreshold();
        ContourThreshold contourThreshold_;

        MTVector<float> box;  ///< 用于MT_BODY_ENABLE_CONTOUR，外部传入人体框和置信度，5个float，归一化后数值，范围[0-1]，顺序含义 (x, y, width, height, score);

        //（MT_BODY_ENABLE_HUMAN模块）输出box的阈值
        void SetThresholdForHuman(float score);
        float GetThresholdForHuman();
        float humanBoundScore_ = 0.7f;

        //ai内部过滤一次轮廓 默认:[true]; false时ai不再过滤，轮廓全部给出
        bool aiFilterContour_ = true;
        /////////////////////////以上是Run时有效的接口(参数)/////////////////////////

    };
    inline MTAiEngineType MTBodyModuleOption::MuduleType() { return MTAiEngineType_BodyModule; }
    inline void MTBodyModuleOption::SetSigEnaOption(MTBodyEnableEnum flag) { enable_option_ |= flag; }//SetSingleEnableOption
    inline bool MTBodyModuleOption::GetSigEnaOptionStatus(MTBodyEnableEnum flag){ return ((enable_option_ & flag) == flag); }
    inline void MTBodyModuleOption::SetMultiThread(bool isMultiThread) { isMultiThread_ = isMultiThread; }
    inline bool MTBodyModuleOption::GetMultiThread() { return isMultiThread_; }
    inline void MTBodyModuleOption::SetThresholdForHuman(float score) { humanBoundScore_ = score; }
    inline float MTBodyModuleOption::GetThresholdForHuman() { return humanBoundScore_; }
    inline void MTBodyModuleOption::SetSmoothParam(SmoothParam smoothParam) { smoothParam_ = smoothParam; }
    inline SmoothParam MTBodyModuleOption::GetSmoothParam() { return smoothParam_; }
    inline void MTBodyModuleOption::SetContourThreshold(ContourThreshold contourThreshold) { contourThreshold_ = contourThreshold; }
    inline ContourThreshold MTBodyModuleOption::GetContourThreshold() { return contourThreshold_; }

}

#endif //MTAIENGINE_MTBODYMODULEOPTION_H
