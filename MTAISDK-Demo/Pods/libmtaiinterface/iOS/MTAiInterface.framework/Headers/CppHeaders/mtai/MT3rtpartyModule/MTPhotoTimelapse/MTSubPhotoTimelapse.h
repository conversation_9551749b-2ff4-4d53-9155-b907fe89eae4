#ifndef _MTSUB_PHOTOTIMELAPSE_H_
#define _MTSUB_PHOTOTIMELAPSE_H_

#include <mtai/Common/MTAiEngineMacro.h>

#ifdef __cplusplus
extern "C"
#endif
{


	MTAIENGINE_API void* mtlabai_sub_timelapse_create_handle(int log_level);

	MTAIENGINE_API void mtlabai_sub_timelapse_release_handle(void* handle);

    /**
	* \brief 阶段1: 初始化算法
	* \param[in] nWidth 视频图像宽度
	* \param[in] nHeight 视频图像高度
	* \param[in] nStride 视频图像步长
	* \param[in] pImage 第一帧图片
	* \param[in] fFaceInfos 人脸检测数据（num,x,y,w,h,x,y,w,h,...）
	* \param[in] fBV 设备获取的BV值，用来判断白天黑夜(安卓设备传>10000的值)
	*/
	MTAIENGINE_API bool mtlabai_sub_timelapse_init(void* handle, int nWidth, int nHeight, int nStride, unsigned char* pImage, int* fFaceInfos, float fBV);

	/**
	* \brief 获取模式参数
	* \param[out] bIsDay true代表白天
	* \param[out] bIsPortraitMode true代表人像模式
	*/
	MTAIENGINE_API bool mtlabai_sub_timelapse_get_mode_param(void* handle, bool& bIsDay, bool& bIsPortraitMode);

	/**
	* \brief 阶段2: 选帧
	* \param[in] pImage 输入缩略图
	* \param[in] nWidth 缩略图宽
	* \param[in] nHeight 缩略图高
	* \param[in] nStride 缩略图步长
	* \return: 0代表未选中，继续执行；1代表选帧成功，进入下一个阶段;-1代表错误
	*/
	MTAIENGINE_API int mtlabai_sub_timelapse_select_frame(void* handle, unsigned char* pImage, int nWidth, int nHeight, int nStride);

	/**
	* \brief 阶段2: 设置第一帧大图,系选帧成功那一帧对应的大图
	* \param[in] pImage 输入帧
	*/
	MTAIENGINE_API bool mtlabai_sub_timelapse_set_first_frame(void* handle, unsigned char* pImage);

	/**
	* \brief 阶段3: 设置人像mask（非人像场景跳过）
	* \param[in] pMask 人像mask
	* \param[in] nMaskWidth 人像mask宽
	* \param[in] nMaskHeight 人像mask高
	* \param[in] fFaceInfos 人脸检测数据（num,x,y,w,h,x,y,w,h,...）
	*/
	MTAIENGINE_API bool mtlabai_sub_timelapse_set_portrait_mask(void* handle, unsigned char* pMask, int nMaskWidth, int nMaskHeight, int* fFaceInfos);

	/**
	* \brief 阶段4: 图片合成，逐帧输入
	* \param[in] pImage 输入帧
	* \return: 0代表正常运行；<0代表运行结束或者出现异常，进入下个阶段；1代表错误
	*/
	MTAIENGINE_API int mtlabai_sub_timelapse_run(void* handle, unsigned char* pImage);

	/**
	* \brief 阶段5: 获取结果
	* \param[out] pImage 结果图片
	*/
	MTAIENGINE_API bool mtlabai_sub_timelapse_get_res(void* handle, unsigned char* pImage);

	/**
	* \brief 阶段6: 释放内存
	*/
	MTAIENGINE_API bool mtlabai_sub_timelapse_release_resource(void* handle);

	/**
	* \brief 获取log字符串
	* \return: string字符串,内存需要外部手动释放
	*/
	MTAIENGINE_API const char* mtlabai_sub_timelapse_get_log(void* handle);
}

#endif // _MTSUB_PHOTOTIMELAPSE_H_