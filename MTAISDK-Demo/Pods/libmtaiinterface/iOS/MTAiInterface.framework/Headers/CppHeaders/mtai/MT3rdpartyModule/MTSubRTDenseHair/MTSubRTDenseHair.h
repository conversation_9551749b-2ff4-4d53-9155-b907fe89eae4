#pragma once

#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/Common/MTVector.h>
#include <mtai/Common/MTAiEngineDefine.h>

#ifndef GLuint
typedef unsigned int GLuint;
#endif

#ifdef __cplusplus
extern "C"
#endif
{
    typedef struct mtlabai_sub_rt_dense_hair_handle mtlabai_sub_rt_dense_hair_handle_t;

    /**
    * @brief 初始化merak
    *
    * @param
    *
    * @return mtlabai_sub_rt_dense_hair_handle_t
    */
    MTAIENGINE_API mtlabai_sub_rt_dense_hair_handle_t* mtlabai_sub_rt_dense_hair_init();

    /**
    * @brief 加载dtu、模型
    *
    * @param  handle       [in] merak句柄
    * @param  srcDir       [in] 资源文件目录  /XX/MTAiModel
    * @param  glContext    [in] gpu环境指针
    * @param  assetManager [in] 安卓asset管理器，预留
    *
    * @return 0:加载正常，-1:加载异常且会有相应的log
    */
    MTAIENGINE_API int mtlabai_sub_rt_dense_hair_load_model_path(
        mtlabai_sub_rt_dense_hair_handle_t* handle,
        const char* srcDir,
        void* glContext,
        void* assetManager);

    /**
    * @brief 运行检测
    *
    * @param  handle          [in] merak句柄
    * @param  inTextureID     [in] 输入纹理ID
    * @param  inTextureWidth  [in] 输入纹理宽，输出纹理和输入纹理等大
    * @param  inTextureHeight [in] 输入纹理高，输出纹理和输入纹理等大
    * @param  faPoint         [in] 人脸fa点(130个，xy)
    * @param  headPoint       [in] 人脸头部点(40个，xy)
    * @param  visibility      [in] 人脸可见性(130个)
    * @param  pitch           [in] 人脸欧拉角pitch分量
    * @param  yaw             [in] 人脸欧拉角yaw分量
    * @param  roll            [in] 人脸欧拉角roll分量
    * @param  faceID          [in] 人脸ID
    * @param  outTextureID    [in] 输出纹理ID
    *
    * @return 0:运行正常，-1:运行异常且会有相应的log
    */
    MTAIENGINE_API int mtlabai_sub_rt_dense_hair_run(
        mtlabai_sub_rt_dense_hair_handle_t* handle,
        const GLuint inTextureID,
        const int inTextureWidth,
        const int inTextureHeight,
        const mtai::MTVector<mtai::MTVector<mtai::MTPoint2f>> &faPoint,
        const mtai::MTVector<mtai::MTVector<mtai::MTPoint2f>> &headPoint,
        const mtai::MTVector<mtai::MTVector<float>>& visibility,
        const mtai::MTVector<float> &pitch,
        const mtai::MTVector<float> &yaw,
        const mtai::MTVector<float> &roll,
        const mtai::MTVector<int> &faceID,
        const GLuint outTextureID);

    /**
    * @brief 释放merak
    *
    * @param  handle [in] merak句柄
    *
    * @return
    */
    MTAIENGINE_API int mtlabai_sub_rt_dense_hair_release(mtlabai_sub_rt_dense_hair_handle_t* handle);
}
