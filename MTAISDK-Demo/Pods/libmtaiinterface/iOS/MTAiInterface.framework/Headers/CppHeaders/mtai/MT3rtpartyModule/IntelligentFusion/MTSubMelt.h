#ifndef _MT_SUB_MELT_H_
#define _MT_SUB_MELT_H_

#include <cstddef>
#include <mtai/Common/MTAiEngineMacro.h>

#ifndef uint8_t
    typedef unsigned char uint8_t;
#endif

#ifndef uint32_t
typedef unsigned int uint32_t;
#endif

extern "C" {
    typedef struct mtlabai_sub_melt_handle_t mtlabai_sub_melt_handle_t;

    /**
    * \brief 构造融化效果处理对象
    */
    MTAIENGINE_API mtlabai_sub_melt_handle_t *mtlabai_sub_melt_handle_create();

    /**
    *
    *
    */
    MTAIENGINE_API void mtlabai_sub_melt_handle_release(mtlabai_sub_melt_handle_t **handle);

    /**
    * \brief 执行算法
    * \param[in] src_stride 输入图像的stride(pitch，行字节长)
    * \param[in] src 输入图像的内存指针，单通道
    * \param[in] dst_stride 输出图像的stride(pitch，行字节长)
    * \param[in,out] dst 输出图像的内存指针，单通道
    * \param[in] cols 处理的宽度
    * \param[in] rows 处理的高度
    * \return 处理状态
    */
    MTAIENGINE_API bool mtlabai_sub_melt_process(mtlabai_sub_melt_handle_t *handle, size_t src_stride, const uint8_t* src, size_t dst_stride, uint8_t* dst, int cols, int rows);

}

#endif