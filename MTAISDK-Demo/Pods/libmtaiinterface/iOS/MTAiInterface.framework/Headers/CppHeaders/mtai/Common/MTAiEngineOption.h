//
// Created by IF on 2019/2/12.
//

#ifndef MTAIENGINE_MTMODULEOPTION_H
#define MTAIENGINE_MTMODULEOPTION_H

#include <mtai/Common/MTAiEngineDefine.h>
#include <mtai/Common/MTAiEngineType.h>
#include <mtai/Common/MTValueMap.h>
#include <mtai/Common/MTVector.h>
#include <mutex>
#include <map>

struct cJSON;
namespace mtai
{

    /**
     * 检测件初始化所需要的选项接口类
     *
     * 这个数据决定检测件初始化的时候需要加载哪些模型
     */
    class MTAIENGINE_API MTAiEngineOption {
    protected:
        uint64_t enable_option_ = 0;                                                ///<< 检测使能开关选项 
        std::mutex lock_;
        MTAI_INFERENCE_TYPE inference_suggested_type_ = MTAI_INFERENCE_NULL;        //建议的manis推理类型

    public:
        virtual ~MTAiEngineOption() = default;

        MTAiEngineOption(){}

        MTAiEngineOption(const MTAiEngineOption &src)
        :enable_option_(src.enable_option_)
        {

        }

        virtual MTAiEngineType MuduleType() = 0;
        uint64_t GetEnableOption();
        void SetEnableOption(uint64_t option);
        void AND(uint64_t option);
        void AND(MTAiEngineOption* option);
        void AND(MTAiEngineOption& option);
        MTAI_INFERENCE_TYPE GetInferenceSuggested();
        void SetInferenceSuggested(MTAI_INFERENCE_TYPE type);
        virtual std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode);
		virtual cJSON* GetParamsCapture();

        MTAiEngineOption& operator= (const MTAiEngineOption& other){
            this->enable_option_ = other.enable_option_;
            this->inference_suggested_type_ = other.inference_suggested_type_;
            return *this;
        }

        virtual int restore(cJSON * dict,const char * cfgPath,void * assertManager = nullptr){return MTAiEngineRet_Failure;}

        virtual MTAiEngineOption * Clone() const{return nullptr;}

    };

    inline uint64_t MTAiEngineOption::GetEnableOption(){
        std::lock_guard<std::mutex> lock(lock_);
        return enable_option_;
    }
    inline void MTAiEngineOption::SetEnableOption(uint64_t option){
        std::lock_guard<std::mutex> lock(lock_);
        enable_option_ = option;
    }
    inline void MTAiEngineOption::AND(uint64_t option){enable_option_ &= option;}
    inline void MTAiEngineOption::AND(MTAiEngineOption* option){enable_option_ &= option->GetEnableOption();}
    inline void MTAiEngineOption::AND(MTAiEngineOption& option){enable_option_ &= option.GetEnableOption();}
//    /**
//     * 检测过程中检测件需要的开关选项
//     *
//     * 这个数据决定检测件检测过程中需要执行哪些功能的检测
//     */
//    MTAIENGINE_API
//    struct MTAiEngineEnableOption {
///////////////////// This code has move to "Common/MTAiEngineEnableOption.h" /////////////////
//    };
    

}

#endif //MTAIENGINE_MTMODULEOPTION_H
