#ifndef MTAIENGINE_MTANCHORGENERATIONMODULEOPTION_H
#define MTAIENGINE_MTANCHORGENERATIONMODULEOPTION_H

#include <mtai/Common/MTAiEngineOption.h>

namespace mtai
{
    enum MTAnchorGenerationEnableEnum{
         MT_ANCHOR_GENERATION_ENABLE_NONE            = 0x0,        // 无检测
         MT_ANCHOR_GENERATION_ENABLE_GENERATION      = MT_MASK(0), // 生成锚点
         MT_ANCHOR_GENERATION_ENABLE_REGENERATION    = MT_MASK(1), // 重新生成锚点
         MT_ANCHOR_GENERATION_ENABLE_MATCH           = MT_MASK(2), // 锚点匹配
         MT_ANCHOR_GENERATION_ENABLE_TIME            = MT_MASK(3), // 获取运行耗时
    };

    struct MTAnchorKey {
        float detectionThreshold            = 0.8f;        //范围:[0,1] DetectAnchor阶段过滤人体的阈值，影响生成锚点，阈值越小误检越多，认为是人的面积越大，锚点的可选范围越小
        float matchThreshold                = 0.3f;        //范围:[0,1] DetectMatch阶段控制match的阈值，阈值越高越容易匹配
    };

    class MTAIENGINE_API MTAnchorGenerationModuleOption : public MTAiEngineOption{

    public:
        // 单独设置某个开关
        void SetSigEnaOption(MTAnchorGenerationEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MTAnchorGenerationEnableEnum flag);//GetSingleEnableOptionStatus
        
        MTAiEngineType MuduleType() override;

        std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;
        
        /////////////////////////以下是RegisterModule时才有效的接口(参数)/////////////////////////   
        void SetAnchorNum(int anchorNum);
        int GetAnchorNum();

        void SetAnchorScale(float anchorScale);
        float GetAnchorScale();

        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

        //锚点设置参数
        MTAnchorKey anchorKey;
        /////////////////////////以上是RegisterModule时才有效的接口(参数)/////////////////////////   

        int anchorNum_ = 3;                 /// 锚点数量
        float anchorScale_ = 1.0/16;        /// 锚点直径占输入图长边的比例，即 锚点直径 = 输入图长边 * anchor_scale（直径过大会造成可选锚点范围变少）

    };

    inline MTAiEngineType MTAnchorGenerationModuleOption::MuduleType() {return MTAiEngineType_AnchorGenerationModule;}
    inline void MTAnchorGenerationModuleOption::SetSigEnaOption(MTAnchorGenerationEnableEnum flag) { enable_option_ |= flag; } //SetSingleEnableOption
    inline bool MTAnchorGenerationModuleOption::GetSigEnaOptionStatus(MTAnchorGenerationEnableEnum flag){ return ((enable_option_ & flag) == flag); }

    inline void MTAnchorGenerationModuleOption::SetAnchorNum(int anchorNum) {anchorNum_ = anchorNum;}
    inline int MTAnchorGenerationModuleOption::GetAnchorNum() {return anchorNum_;}

    inline void MTAnchorGenerationModuleOption::SetAnchorScale(float anchorScale) {anchorScale_ = anchorScale;}
    inline float MTAnchorGenerationModuleOption::GetAnchorScale() {return anchorScale_;}
}

#endif //MTAIENGINE_MTANCHORGENERATIONMODULEOPTION_H
