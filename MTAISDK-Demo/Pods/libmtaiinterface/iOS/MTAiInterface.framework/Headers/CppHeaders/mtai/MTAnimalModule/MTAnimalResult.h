#pragma once
#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"

namespace mtai
{
    ////////////////////////////////////////////////////////////////////////////////////////
    //								EXIF方向示例
    //     1        2       3      4         5            6           7          8
    //
    //    888888  888888      88  88      8888888888  88                  88  8888888888
    //    88          88      88  88      88  88      88  88          88  88      88  88
    //    8888      8888    8888  8888    88          8888888888  8888888888          88
    //    88          88      88  88
    //    88          88  888888  888888
    ////////////////////////////////////////////////////////////////////////////////////////
    struct MTAIENGINE_API MTAnimal {
        void Print() const ;

        int                     ID = -1;                    ///< ID. 跟踪时前后帧ID一致表示同一张猫狗脸，静态图检测单纯表示顺序
        MTAnimalLabel           label = MTAnimalLabelNone;  ///< 类型, 0-背景, 1-猫脸, 2-狗脸
        float                   score = 0.0f;               ///< 动物脸置信度， 取值范围[0, 1]
        MTRect_<float>          animalBounds;               ///< 动物脸框
        MTVector<MTPoint2f>     animalPoints;               ///< 动物脸点 2D
    };

    struct MTAIENGINE_API MTAnimalResult {
        void Print() const ;

        bool                    normalize = true;           ///< 是否归一化数据
        int                     orientation = 1;            ///< 数据方向
        MTSize_<int>            size = MTSize_<int>(1,1);   ///< 数据size
        MTVector<MTAnimal>      animals;                    ///< 动物数据

        float                   runTime     = 0.0;                  ///< 运行耗时 单位ms
    };

    /**
     * 动物结果转化
     * 需要在dst中先填充参数
     *
     * @param src   输入
     * @param dst   输出
     * @return      结果
     */
    MTAIENGINE_API
    MTAiEngineRet ConvertAnimalResult(const MTAnimalResult &src, MTAnimalResult &dst);
}
