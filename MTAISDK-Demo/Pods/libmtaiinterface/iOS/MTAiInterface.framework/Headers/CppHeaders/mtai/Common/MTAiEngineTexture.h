//
// Created by l<PERSON><PERSON><PERSON><PERSON> on 2019/4/24.
//

#ifndef MTAIENGINE_MTDETECTTEXTURE_H_
#define MTAIENGINE_MTDETECTTEXTURE_H_

#include <mtai/Common/MTAiEngineDefine.h>
#include <mtai/Common/MTAiEngineImage.h>

namespace mtai
{
    class MTAIENGINE_API MTAiEngineTexture
    {

    public:
        MTAiEngineTexture();
        MTAiEngineTexture(int width, int height, unsigned char* data = nullptr, MTAiEngineImage::PIXEL_FORMAT format = MTAiEngineImage::PIXEL_FORMAT_RGBA);
        MTAiEngineTexture(const MTAiEngineTexture& other);
        ~MTAiEngineTexture();

        MTAiEngineTexture& operator=(const MTAiEngineTexture& others);

        //在引用计数大于1或者纹理id为0时，无法resize成功，因此必须判断返回值是否是MTAiEngineRet_Success
        MTAiEngineRet ResizeOpenGLTexture(int width, int height);
        //将纹理数据读取到output指针指向的cpu内存中，需要外部先为output分配适合的内存空间
        MTAiEngineRet ReadDataFromTexture(unsigned char* output);
        bool IsValid() const;
        int GetRefCount() const;
        void ForceSetRefCount(int ref); //强制重置引用计数，用于强制删除纹理id。仅供内部纹理池使用
        static MTAiEngineTexture CreateGrayTexture(int width, int height, unsigned char* data = nullptr);
    public:
        unsigned int texture_id;
        int texture_width;
        int texture_height;
        unsigned int texture_exif{1};

    private:
        void CreateOpenGLTexture(MTAiEngineImage::PIXEL_FORMAT format, unsigned char* data = nullptr);
        void ReleaseOpenGLTexture();
        int* m_pRefcount;
    };
}


#endif //MTAIENGINE_MTDETECTTEXTURE_H_
