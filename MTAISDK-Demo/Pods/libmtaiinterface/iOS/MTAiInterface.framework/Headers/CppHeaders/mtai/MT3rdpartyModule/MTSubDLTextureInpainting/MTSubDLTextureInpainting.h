#pragma once

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
	extern "C" {
#endif

typedef struct mtlab_sub_dl_texture_inpainting_handle* mtlab_sub_dl_texture_inpainting_handle_t;
typedef struct mtlab_sub_dl_texture_inpainting_model* mtlab_sub_dl_texture_inpainting_model_t;

enum DLTextureInpaintingRetStatus {
	DLTextureInpaintingRetStatus_SUCCESS,
	DLTextureInpaintingRetStatus_FAILURE,
	DLTextureInpaintingRetStatus_RESOURCE_EXIST,
	DLTextureInpaintingRetStatus_RESOURCE_NOTFOUND,
	DLTextureInpaintingRetStatus_NULL_ARGS,
	DLTextureInpaintingRetStatus_INVALID_ARGS,
	DLTextureInpaintingRetStatus_NO_OPS,
};
enum DLTextureInpaintingModelType {
    DLTextureInpaintingModelType_HEAD,
    DLTextureInpaintingModelType_CHIN,
    DLTextureInpaintingModelType_RBX,
};

enum DLTextureInpaintingDeviceType {
    DLTextureInpaintingDeviceType_CPU,
    DLTextureInpaintingDeviceType_CL,
    DLTextureInpaintingDeviceType_METAL,
    DLTextureInpaintingDeviceType_COREML,
};

enum DLTextureInpaintingLayoutType {
    DLTextureInpaintingLayoutType_NCHW,
    DLTextureInpaintingLayoutType_NCHWC4,
};

enum DLTextureInpaintingImageType {
	DLTextureInpaintingImageType_WHITE,
	DLTextureInpaintingImageType_CROSS_POLAR,
	DLTextureInpaintingImageType_PARALLEL_POLAR,
	DLTextureInpaintingImageType_UV,
	DLTextureInpaintingImageType_RED,
	DLTextureInpaintingImageType_BROWN,
};

mtlab_sub_dl_texture_inpainting_handle_t
mtlab_sub_dl_texture_inpainting_create();

void
mtlab_sub_dl_texture_inpainting_destroy(mtlab_sub_dl_texture_inpainting_handle_t handle);

int
mtlab_sub_dl_texture_inpainting_init(mtlab_sub_dl_texture_inpainting_handle_t handle,
                                     mtlab_sub_dl_texture_inpainting_model_t model);

void 
mtlab_sub_dl_texture_inpainting_prepare_size(mtlab_sub_dl_texture_inpainting_handle_t handle,
											 int width, int height);

int
mtlab_sub_dl_texture_inpainting_prepare_mask(mtlab_sub_dl_texture_inpainting_handle_t handle,
											 uint8_t *full_mask, uint8_t *skin_mask, uint8_t *fixture_mask);

int
mtlab_sub_dl_texture_inpainting_mask_status(mtlab_sub_dl_texture_inpainting_handle_t handle);

int
mtlab_sub_dl_texture_inpainting_process(mtlab_sub_dl_texture_inpainting_handle_t handle,
										uint8_t *image_data, uint8_t *result_data,
										DLTextureInpaintingImageType image_type);

int
mtlab_sub_dl_texture_inpainting_red_brown_process(mtlab_sub_dl_texture_inpainting_handle_t handle, uint8_t *image_data,
                                                  uint8_t *result_brown_data, uint8_t *result_red_data,
                                                  const char *directory, DLTextureInpaintingDeviceType device_type);

int
mtlab_sub_dl_texture_inpainting_red_brown_data_process(mtlab_sub_dl_texture_inpainting_handle_t handle, uint8_t *image_data,
                                                  uint8_t *result_red_data, uint8_t *result_brown_data,
                                                  const char *directory, void *asset_manager, 
                                                  DLTextureInpaintingDeviceType device_type);
    
int
mtlab_sub_dl_texture_inpainting_uv_gray_process(mtlab_sub_dl_texture_inpainting_handle_t handle,
                                                uint8_t *uv_image_data, uint8_t *uv_result_data,
                                                uint8_t *result_gray_data);

int
mtlab_sub_dl_texture_inpainting_skin_homo_process(mtlab_sub_dl_texture_inpainting_handle_t handle,
                                                  uint8_t *pl_image_data, int pl_image_width, int pl_image_height,
                                                  uint8_t *face_mask_data, int face_mask_width, int face_mask_height,
                                                  float *points_data, size_t points_size, bool normalized,
                                                  uint8_t *result_data,
                                                  const char *mean_data, size_t mean_data_size);

int
mtlab_sub_dl_texture_inpainting_skin_glow_process(mtlab_sub_dl_texture_inpainting_handle_t handle, uint8_t *oil_image_data);

mtlab_sub_dl_texture_inpainting_model_t
mtlab_sub_dl_texture_inpainting_model_create(void *asset);

void
mtlab_sub_dl_texture_inpainting_model_destroy(mtlab_sub_dl_texture_inpainting_model_t model);

void
mtlab_sub_dl_texture_inpainting_model_set_type(mtlab_sub_dl_texture_inpainting_model_t model,
											   DLTextureInpaintingDeviceType device_type,
											   DLTextureInpaintingLayoutType layout_type);

int
mtlab_sub_dl_texture_inpainting_model_add_file(mtlab_sub_dl_texture_inpainting_model_t model,
												  DLTextureInpaintingModelType model_type,
												  const char *filename,
												  const char *cl_model_optimize_result_path);

int
mtlab_sub_dl_texture_inpainting_model_add_files(mtlab_sub_dl_texture_inpainting_model_t model,
											   const char *directory,
											   const char *cl_model_optimize_result_directory);

#ifdef __cplusplus
}
#endif