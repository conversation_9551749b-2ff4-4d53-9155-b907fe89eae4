#pragma once
#include <mtai/Common/MTAiEngineOption.h>

namespace mtai
{
    enum MTCgStyleDevType{
        MTCgStyleDevType_GL = 0,
        MTCgStyleDevType_CL = 1,
    };

    enum MTCgStyleEnableEnum : uint64_t {
         MT_CGSTYLE_ENABLE_NONE                  = 0x0,        // 无检测
         MT_CGSTYLE_ENABLE_TIME                  = MT_MASK(0), // 获取运行耗时
         MT_CGSTYLE_ENABLE_CGSTYLE               = MT_MASK(1), // 开启cg功能
         MT_CGSTYLE_ENABLE_DEPEND_OUTSIDE_FACE   = MT_MASK(2), // 依赖外部传入的人脸点数据，要求归一化，且和frame.image的orientation一致。 注：只对传入的第一个人进行cg处理
    };

    class MTAIENGINE_API MTCgStyleModuleOption : public MTAiEngineOption{

    public:
        
        // 单独设置某个开关
        void SetSigEnaOption(MTCgStyleEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MTCgStyleEnableEnum flag);//GetSingleEnableOptionStatus
 
        // 模块类型
        MTAiEngineType MuduleType() override;

        std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;
        
        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

        //注册时候设置    true：使用大模型   false：使用小模型
        bool use_big_model_ = true;

        //注册时候设置    设置使用的设备模式，默认使用opengl   目前只有android才支持opencl模式
        MTCgStyleDevType device_type_ = MTCgStyleDevType_GL;
    };

    inline MTAiEngineType MTCgStyleModuleOption::MuduleType() {return MTAiEngineType_CgStyleModule;}
    inline void MTCgStyleModuleOption::SetSigEnaOption(MTCgStyleEnableEnum flag) { enable_option_ |= flag; } //SetSingleEnableOption
    inline bool MTCgStyleModuleOption::GetSigEnaOptionStatus(MTCgStyleEnableEnum flag){ return ((enable_option_ & flag) == flag); }
}

