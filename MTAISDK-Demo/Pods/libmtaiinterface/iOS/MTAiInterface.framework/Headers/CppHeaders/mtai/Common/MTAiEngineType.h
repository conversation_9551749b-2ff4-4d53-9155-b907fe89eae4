//
// Created by IF on 2019/2/12.
//

#ifndef MTAIENGINE_MTMODULETYPE_H
#define MTAIENGINE_MTMODULETYPE_H

#include <mtai/Common/MTAiEngineDefine.h>

#define MTAIENGINE_MODEL_FACE_FD_BOX           MTAIENGINE_MODEL(FACE_FD_BOX)             ///<< 人脸检测模型: mtface_fd_box.manisa
#define MTAIENGINE_MODEL_FACE_FD_LMK         MTAIENGINE_MODEL(FACE_FD_LMK)           ///<< 人脸检测模型: mtface_fd_lmk.manis

#define MTAIENGINE_MODEL_FACE_FD           MTAIENGINE_MODEL(FACE_FD)             ///<< 人脸检测模型
#define MTAIENGINE_MODEL_FACE_FA_HEAVY     MTAIENGINE_MODEL(FACE_FA_HEAVY)       ///<< 人脸检测模型
#define MTAIENGINE_MODEL_FACE_FA_LIGHT     MTAIENGINE_MODEL(FACE_FA_LIGHT)       ///<< 人脸检测模型
#define MTAIENGINE_MODEL_FACE_FA_MEDIUM    MTAIENGINE_MODEL(FACE_FA_MEDIUM)      ///<< 人脸检测模型
#define MTAIENGINE_MODEL_FACE_REFINE_MOUTH MTAIENGINE_MODEL(FACE_REFINE_MOUTH)   ///<< 人脸检测模型
#define MTAIENGINE_MODEL_FACE_REFINE_EYES  MTAIENGINE_MODEL(FACE_REFINE_EYES)    ///<< 人脸检测模型
#define MTAIENGINE_MODEL_FACE_REFINE_EYES_LIGHT  MTAIENGINE_MODEL(FACE_REFINE_EYES_LIGHT)    ///<< 人脸检测眼睛小模型
#define MTAIENGINE_MODEL_FACE_AGE          MTAIENGINE_MODEL(FACE_AGE)         ///<< 人脸年龄检测模型
#define MTAIENGINE_MODEL_FACE_AGE_SEA      MTAIENGINE_MODEL(FACE_AGE_SEA)     ///<< 人脸年龄海外检测模型
#define MTAIENGINE_MODEL_FACE_AGE_SERVER   MTAIENGINE_MODEL(FACE_AGE_SERVER)     ///<< 人脸年龄海外检测模型
#define MTAIENGINE_MODEL_FACE_GENDER       MTAIENGINE_MODEL(FACE_GENDER)      ///<< 人脸性别检测模型
#define MTAIENGINE_MODEL_FACE_RACE         MTAIENGINE_MODEL(FACE_RACE)        ///<< 人脸种族检测模型
#define MTAIENGINE_MODEL_FACE_EMOTION      MTAIENGINE_MODEL(FACE_EMOTION)     ///<< 人脸表情检测模型
#define MTAIENGINE_MODEL_FACE_GLASSES      MTAIENGINE_MODEL(FACE_GLASSES)     ///<< 人脸眼镜检测模型
#define MTAIENGINE_MODEL_FACE_BEAUTY       MTAIENGINE_MODEL(FACE_BEAUTY)      ///<< 人脸颜值检测模型
#define MTAIENGINE_MODEL_FACE_EYELID       MTAIENGINE_MODEL(FACE_EYELID)      ///<< 人脸眼皮检测模型
#define MTAIENGINE_MODEL_FACE_MUSTACHE     MTAIENGINE_MODEL(FACE_MUSTACHE)    ///<< 人脸胡子检测模型
#define MTAIENGINE_MODEL_FACE_CHEEK        MTAIENGINE_MODEL(FACE_CHEEK)       ///<< 人脸颧骨检测模型
#define MTAIENGINE_MODEL_FACE_JAW          MTAIENGINE_MODEL(FACE_JAW)         ///<< 人脸下巴检测模型
#define MTAIENGINE_MODEL_FACE_EAR          MTAIENGINE_MODEL(FACE_EAR)         ///<< 人脸耳朵检测模型
#define MTAIENGINE_MODEL_FACE_FR           MTAIENGINE_MODEL(FACE_FR)          ///<< 人脸识别检测模型
#define MTAIENGINE_MODEL_FACE_FR_VIDEO     MTAIENGINE_MODEL(FACE_FR_VIDEO)          ///<< 人脸识别检测模型
#define MTAIENGINE_MODEL_FACE_FR_CAMERA    MTAIENGINE_MODEL(FACE_FR_CAMERA)   ///<< 人脸识别检测模型-相机场景
#define MTAIENGINE_MODEL_FACE_FR_FOREIGN   MTAIENGINE_MODEL(FACE_FR_FOREIGN)  ///<< 人脸识别检测模型-海外
#define MTAIENGINE_MODEL_FACE_PART         MTAIENGINE_MODEL(FACE_PART)        ///<< 人脸局部检测模型
#define MTAIENGINE_MODEL_FACE_QUALITY      MTAIENGINE_MODEL(FACE_QUALITY)     ///<< 人脸质量检测模型
#define MTAIENGINE_MODEL_FACE_PARSING      MTAIENGINE_MODEL(FACE_PARSING)     ///<< 全脸检测模型
#define MTAIENGINE_MODEL_FACE_PARSING_VIDEO      MTAIENGINE_MODEL(FACE_PARSING_VIDEO)     ///<< 全脸检测模型
#define MTAIENGINE_MODEL_FACE_DL3D         MTAIENGINE_MODEL(FACE_DL3D)     ///<< 全脸检测模型
#define MTAIENGINE_MODEL_FACE_HEAD         MTAIENGINE_MODEL(FACE_HEAD)        ///<< 人脸头部检测模型
#define MTAIENGINE_MODEL_FACE_PARSING_SERVER MTAIENGINE_MODEL(FACE_PARSING_SERVER) ///<< 全脸检测服务端模型
#define MTAIENGINE_MODEL_FACE_PARSING_SERVER_VIDEO MTAIENGINE_MODEL(FACE_PARSING_SERVER_VIDEO) ///<< 全脸检测服务端模型
#define MTAIENGINE_MODEL_FACE_PARSING_HEAVY   MTAIENGINE_MODEL(FACE_PARSING_HEAVY)  ///<< 全脸检测HEAVY模型，包含额头区域
#define MTAIENGINE_MODEL_FACE_PARSING_HEAVY_VIDEO   MTAIENGINE_MODEL(FACE_PARSING_HEAVY_VIDEO)  ///<< 全脸检测HEAVY模型，包含额头区域
#define MTAIENGINE_MODEL_FACE_PARSING_LIGHT   MTAIENGINE_MODEL(FACE_PARSING_LIGHT)  ///<< 全脸检测LIGHT模型，速度较快
#define MTAIENGINE_MODEL_FACE_PARSING_LIGHT_VIDEO   MTAIENGINE_MODEL(FACE_PARSING_LIGHT_VIDEO)  ///<< 全脸检测LIGHT模型，速度较快
#define MTAIENGINE_MODEL_FACE_OCCLUSION MTAIENGINE_MODEL(FACE_OCCLUSION)   ///<< 人脸遮挡检测模型
#define MTAIENGINE_MODEL_FACE_3DFA_AVATAR MTAIENGINE_MODEL(FACE_3DFA_AVATAR)   ///<< 人脸3DFA驱动模型
#define MTAIENGINE_MODEL_FACE_3DFA MTAIENGINE_MODEL(FACE_3DFA)   ///<< 人脸标准MVP 3DFA模型
#define MTAIENGINE_MODEL_FACE_3DFA_CROP MTAIENGINE_MODEL(3DFA_CROP)   ///<< 人脸非标准MVP 3DFA模型，此处key值不能修改，智枢依赖
#define MTAIENGINE_MODEL_FACE_SCENE_CHANGE MTAIENGINE_MODEL(SCENE_CHANGE)   ///<< 人脸场景切换模型，此处key值不能修改，智枢依赖

#define MTAIENGINE_MODEL_FACE_REFINE_NOSE MTAIENGINE_MODEL(FACE_REFINE_NOSE)   ///<< 
#define MTAIENGINE_MODEL_FACE_REFINE_CONTOUR MTAIENGINE_MODEL(FACE_REFINE_CONTOUR)   ///<< 人脸3DFA驱动模型


/*五官分析框架系列会有多个配置文件, 目前有:
    五官框架基础配置文件、五官分析配置文件、脖子检测配置文件
 后续还会加入宜肤算法的各个配置文件, 所有的配置文件加载之前都必须先加载五官框架基础配置文件
 */
#define MTAIENGINE_MODEL_DETECT_FRAMEWORK_ROOT_CONFIG         MTAIENGINE_MODEL(DETECT_FRAMEWORK_ROOT_CONFIG)          ///<< 五官框架基础配置文件
#define MTAIENGINE_MODEL_FACE_ANA_CONFIG  MTAIENGINE_MODEL(FACE_ANA_CONFIG)   ///<< 五官分析人脸配置文件
#define MTAIENGINE_MODEL_FACE_NECK_CONFIG  MTAIENGINE_MODEL(FACE_NECK_CONFIG)   ///<< 五官分析脖子检测配置
#define MTAIENGINE_MODEL_FACE_NECK         MTAIENGINE_MODEL(FACE_NECK)          ///<< 五官脖子检测模型

// 五官分析模型
#define MTAIENGINE_MODEL_FACE_ANALYSIS_DL_FACE_TYPE         MTAIENGINE_MODEL(FACE_ANALYSIS_DL_FACE_TYPE)          ///<< 五官脸型模型
#define MTAIENGINE_MODEL_FACE_ANALYSIS_DL_EYE_BAG         MTAIENGINE_MODEL(FACE_ANALYSIS_DL_EYE_BAG)          ///<< 五官眼袋模型
#define MTAIENGINE_MODEL_FACE_ANALYSIS_DL_TEMPLE         MTAIENGINE_MODEL(FACE_ANALYSIS_DL_TEMPLE)          ///<< 五官太阳穴模型
#define MTAIENGINE_MODEL_FACE_ANALYSIS_DL_RISORIUS         MTAIENGINE_MODEL(FACE_ANALYSIS_DL_RISORIUS)          ///<< 五官苹果肌

// 手势检测模型
#define MTAIENGINE_MODEL_HAND_GESTURE   MTAIENGINE_MODEL(HAND_GESTURE)
#define MTAIENGINE_MODEL_HAND_GESTURE_A MTAIENGINE_MODEL(HAND_GESTURE_A)
#define MTAIENGINE_MODEL_HAND_GESTURE_B MTAIENGINE_MODEL(HAND_GESTURE_B)
#define MTAIENGINE_MODEL_HAND_POSE MTAIENGINE_MODEL(HAND_POSE)
#define MTAIENGINE_MODEL_HAND_TRACKER MTAIENGINE_MODEL(HAND_TRACKER)

//Nail
#define MTAIENGINE_MODEL_HAND_NAIL   MTAIENGINE_MODEL(HAND_NAIL) // 模型： 
#define MTAIENGINE_MODEL_HAND_NAIL_DETECT MTAIENGINE_MODEL(HAND_NAIL_DETECT) // 模型： 
#define MTAIENGINE_MODEL_HAND_NAIL_RANGER MTAIENGINE_MODEL(HAND_NAIL_RANGER) // 模型： 

#define MTAIENGINE_MODEL_HAND_NAIL_HANDJOINTS MTAIENGINE_MODEL(HAND_NAIL_HANDJOINTS) // 模型： handjoints.manis
#define MTAIENGINE_MODEL_HAND_NAIL_YOLOX MTAIENGINE_MODEL(HAND_NAIL_YOLOX) // 模型： hand_detect.manis

// 人体框检测模型
#define MTAIENGINE_MODEL_BODY_BOUND_A MTAIENGINE_MODEL(BODY_BOUND_A) // 模型：pd_A.manis
#define MTAIENGINE_MODEL_BODY_BOUND_B MTAIENGINE_MODEL(BODY_BOUND_B) // 模型：pd_B.manis
// 人体检测 肢体点检测模型
#define MTAIENGINE_MODEL_BODY_POSE MTAIENGINE_MODEL(BODY_POSE) // 模型：realtime2.2.1.0_pose_5ae2.manis
#define MTAIENGINE_MODEL_BODY_POSE_A MTAIENGINE_MODEL(BODY_POSE_A) // 模型：realtime2.0.0.0_96_detectionA_6eb2.manis
#define MTAIENGINE_MODEL_BODY_POSE_B MTAIENGINE_MODEL(BODY_POSE_B) // 模型：realtime2.0.0.0_96_detectionB_66d5.manis
// 人体检测 肢体点拍后检测模型
#define MTAIENGINE_MODEL_BODY_POSE_PHOTO MTAIENGINE_MODEL(BODY_POSE_PHOTO) //模型：v0.9..16_d950.manis
// 人体检测 外轮廓点检测
#define MTAIENGINE_MODEL_BODY_CONTOUR   MTAIENGINE_MODEL(BODY_CONTOUR) // 模型：bc_0.1.0_contour_7fd6.manis
#define MTAIENGINE_MODEL_BODY_CONTOUR_A MTAIENGINE_MODEL(BODY_CONTOUR_A) // 模型：bc_0.1.0_detectionA_72a5.manis
#define MTAIENGINE_MODEL_BODY_CONTOUR_B MTAIENGINE_MODEL(BODY_CONTOUR_B) // 模型：bc_0.1.0_detectionB_f05a.manis
// 人体检测 外轮廓点检测38
#define MTAIENGINE_MODEL_BODY_CONTOUR38_REALTIME   MTAIENGINE_MODEL(BODY_CONTOUR38_REALTIME) // 模型：ct_contour.manis
#define MTAIENGINE_MODEL_BODY_CONTOUR38_P_REALTIME MTAIENGINE_MODEL(BODY_CONTOUR38_P_REALTIME) // 模型：ct_pose.manis
#define MTAIENGINE_MODEL_BODY_CONTOUR38_PHOTO   MTAIENGINE_MODEL(BODY_CONTOUR38_PHOTO) // 模型：ct_contour.manis
#define MTAIENGINE_MODEL_BODY_CONTOUR38_P_PHOTO MTAIENGINE_MODEL(BODY_CONTOUR38_P_PHOTO) // 模型：ct_pose.manis
#define MTAIENGINE_MODEL_BODY_CONTOUR38_A MTAIENGINE_MODEL(BODY_CONTOUR38_A) // 模型：ct_detectA.manis
#define MTAIENGINE_MODEL_BODY_CONTOUR38_B MTAIENGINE_MODEL(BODY_CONTOUR38_B) // 模型：ct_detectB.manis
// 猫狗检测模型
#define MTAIENGINE_MODEL_ANIMAL_IRONMAN_MARK1      MTAIENGINE_MODEL(ANIMAL_IRONMAN_MARK1)
#define MTAIENGINE_MODEL_ANIMAL_IRONMAN_MARK2      MTAIENGINE_MODEL(ANIMAL_IRONMAN_MARK2)
#define MTAIENGINE_MODEL_ANIMAL_SPIDERMAN_MARK1    MTAIENGINE_MODEL(ANIMAL_SPIDERMAN_MARK1)
#define MTAIENGINE_MODEL_ANIMAL_SPIDERMAN_MARK2    MTAIENGINE_MODEL(ANIMAL_SPIDERMAN_MARK2)
// 实时分割模型
#define MTAIENGINE_MODEL_REALTIMESEG_HALFBODY MTAIENGINE_MODEL(REALTIMESEG_HALFBODY) //半身
#define MTAIENGINE_MODEL_REALTIMESEG_HALFBODY_COREML MTAIENGINE_MODEL(REALTIMESEG_HALFBODY_COREML) //半身
#define MTAIENGINE_MODEL_REALTIMESEG_HALFBODY_HEAVY MTAIENGINE_MODEL(REALTIMESEG_HALFBODY_HEAVY)
#define MTAIENGINE_MODEL_REALTIMESEG_HALFBODY_HIGH MTAIENGINE_MODEL(REALTIMESEG_HALFBODY_HIGH)
#define MTAIENGINE_MODEL_REALTIMESEG_HAIR MTAIENGINE_MODEL(REALTIMESEG_HAIR) //头发
#define MTAIENGINE_MODEL_REALTIMESEG_HAIR_COREML MTAIENGINE_MODEL(REALTIMESEG_HAIR_COREML) //头发
#define MTAIENGINE_MODEL_REALTIMESEG_SKY MTAIENGINE_MODEL(REALTIMESEG_SKY) //天空
#define MTAIENGINE_MODEL_REALTIMESEG_SKY_COREML MTAIENGINE_MODEL(REALTIMESEG_SKY_COREML) //天空
#define MTAIENGINE_MODEL_REALTIMESEG_SKIN MTAIENGINE_MODEL(REALTIMESEG_SKIN) //皮肤
#define MTAIENGINE_MODEL_REALTIMESEG_SKIN_COREML MTAIENGINE_MODEL(REALTIMESEG_SKIN_COREML) //皮肤
#define MTAIENGINE_MODEL_REALTIMESEG_FACIAL MTAIENGINE_MODEL(REALTIMESEG_FACIAL) //五官
#define MTAIENGINE_MODEL_REALTIMESEG_FACIAL_COREML MTAIENGINE_MODEL(REALTIMESEG_FACIAL_COREML) //五官
#define MTAIENGINE_MODEL_REALTIMESEG_WHOLEBODY MTAIENGINE_MODEL(REALTIMESEG_WHOLEBODY) //全身
#define MTAIENGINE_MODEL_REALTIMESEG_WHOLEBODY_COREML MTAIENGINE_MODEL(REALTIMESEG_WHOLEBODY_COREML) //全身
#define MTAIENGINE_MODEL_REALTIMESEG_CW MTAIENGINE_MODEL(REALTIMESEG_CW) //带检测的分割
#define MTAIENGINE_MODEL_REALTIMESEG_FACECONTOUR MTAIENGINE_MODEL(REALTIMESEG_FACECONTOUR) //人脸轮廓分割
#define MTAIENGINE_MODEL_REALTIMESEG_HEAD MTAIENGINE_MODEL(REALTIMESEG_HEAD) //头部分割
#define MTAIENGINE_MODEL_REALTIMESEG_HEAD_COREML MTAIENGINE_MODEL(REALTIMESEG_HEAD_COREML) //头部分割
#define MTAIENGINE_MODEL_REALTIMESEG_CLOTH MTAIENGINE_MODEL(REALTIMESEG_CLOTH) // 衣服分割
#define MTAIENGINE_MODEL_REALTIMESEG_CLOTH_COREML MTAIENGINE_MODEL(REALTIMESEG_CLOTH_COREML) // 衣服分割
#define MTAIENGINE_MODEL_REALTIMESEG_MUTI MTAIENGINE_MODEL(REALTIMESEG_MUTI) // 多分割
#define MTAIENGINE_MODEL_REALTIMESEG_MUTI_COREML MTAIENGINE_MODEL(REALTIMESEG_MUTI_COREML) // 多分割
#define MTAIENGINE_MODEL_REALTIMESEG_MUTI_HEAVY MTAIENGINE_MODEL(REALTIMESEG_MUTI_HEAVY) // 多分割高精度
#define MTAIENGINE_MODEL_REALTIMESEG_MIDAS MTAIENGINE_MODEL(REALTIMESEG_MIDAS) // 单目深度估计
#define MTAIENGINE_MODEL_REALTIMESEG_MIDAS_COREML MTAIENGINE_MODEL(REALTIMESEG_MIDAS_COREML) // 单目深度估计
#define MTAIENGINE_MODEL_REALTIMESEG_BLURPORTRAIT MTAIENGINE_MODEL(REALTIMESEG_BLURPORTRAIT) // 背景虚化
#define MTAIENGINE_MODEL_REALTIMESEG_VIDEOBODY MTAIENGINE_MODEL(REALTIMESEG_VIDEOBODY) // 实时视频抠图
#define MTAIENGINE_MODEL_REALTIMESEG_VIDEOBODY_COREML MTAIENGINE_MODEL(REALTIMESEG_VIDEOBODY_COREML) // 实时视频抠图coreml
#define MTAIENGINE_MODEL_REALTIMESEG_BLURPORTRAIT_COREML MTAIENGINE_MODEL(REALTIMESEG_BLURPORTRAIT_COREML) //背景虚化coreml
#define MTAIENGINE_MODEL_REALTIMESEG_SPACEDEPTH MTAIENGINE_MODEL(REALTIMESEG_SPACEDEPTH) // 实时视频空间深度信息
#define MTAIENGINE_MODEL_REALTIMESEG_SPACEDEPTH_COREML MTAIENGINE_MODEL(REALTIMESEG_SPACEDEPTH_COREML) // 实时视频空间深度信息coreml
#define MTAIENGINE_MODEL_REALTIMESEG_VIDEOSKIN MTAIENGINE_MODEL(REALTIMESEG_VIDEOSKIN) // 实时视频皮肤
#define MTAIENGINE_MODEL_REALTIMESEG_VIDEOSKIN_COREML MTAIENGINE_MODEL(REALTIMESEG_VIDEOSKIN_COREML) // 实时视频皮肤coreml
#define MTAIENGINE_MODEL_REALTIMESEG_INTERACTIVE_KEY MTAIENGINE_MODEL(REALTIMESEG_INTERACTIVE_KEY) // 实时视频交互分割key
#define MTAIENGINE_MODEL_REALTIMESEG_INTERACTIVE_VALUE MTAIENGINE_MODEL(REALTIMESEG_INTERACTIVE_VALUE) // 实时视频交互分割value
#define MTAIENGINE_MODEL_REALTIMESEG_INTERACTIVE_KEY_COREML MTAIENGINE_MODEL(REALTIMESEG_INTERACTIVE_KEY_COREML) // 实时视频交互分割key coreml
#define MTAIENGINE_MODEL_REALTIMESEG_INTERACTIVE_VALUE_COREML MTAIENGINE_MODEL(REALTIMESEG_INTERACTIVE_VALUE_COREML) // 实时视频交互分割value coreml
#define MTAIENGINE_MODEL_REALTIMESEG_SALIENTOBJECTDETECTION MTAIENGINE_MODEL(REALTIMESEG_SALIENTOBJECTDETECTION) // 实时视频显著性检测分割
#define MTAIENGINE_MODEL_REALTIMESEG_SALIENTOBJECTDETECTION_COREML MTAIENGINE_MODEL(REALTIMESEG_SALIENTOBJECTDETECTION_COREML) // 实时视频显著性检测分割 coreml

// 拍后分割模型
#define MTAIENGINE_MODEL_PHOTOSEG_HALFBODY MTAIENGINE_MODEL(PHOTOSEG_HALFBODY) //半身
#define MTAIENGINE_MODEL_PHOTOSEG_HALFBODY_COREML MTAIENGINE_MODEL(PHOTOSEG_HALFBODY_COREML) //半身
#define MTAIENGINE_MODEL_PHOTOSEG_HAIR MTAIENGINE_MODEL(PHOTOSEG_HAIR) //头发
#define MTAIENGINE_MODEL_PHOTOSEG_HAIR_COREML MTAIENGINE_MODEL(PHOTOSEG_HAIR_COREML) //头发
#define MTAIENGINE_MODEL_PHOTOSEG_SKY MTAIENGINE_MODEL(PHOTOSEG_SKY) //天空
#define MTAIENGINE_MODEL_PHOTOSEG_SKY_COREML MTAIENGINE_MODEL(PHOTOSEG_SKY_COREML) //天空
#define MTAIENGINE_MODEL_PHOTOSEG_SKIN MTAIENGINE_MODEL(PHOTOSEG_SKIN) //皮肤
#define MTAIENGINE_MODEL_PHOTOSEG_SKIN_HIGH MTAIENGINE_MODEL(PHOTOSEG_SKIN_HIGH) //皮肤high
#define MTAIENGINE_MODEL_PHOTOSEG_SKIN_COREML MTAIENGINE_MODEL(PHOTOSEG_SKIN_COREML) //皮肤
#define MTAIENGINE_MODEL_PHOTOSEG_FACIAL MTAIENGINE_MODEL(PHOTOSEG_FACIAL) //五官
#define MTAIENGINE_MODEL_PHOTOSEG_FACIAL_COREML MTAIENGINE_MODEL(PHOTOSEG_FACIAL_COREML) //五官
#define MTAIENGINE_MODEL_PHOTOSEG_WHOLEBODY MTAIENGINE_MODEL(PHOTOSEG_WHOLEBODY) //全身
#define MTAIENGINE_MODEL_PHOTOSEG_WHOLEBODY_COREML MTAIENGINE_MODEL(PHOTOSEG_WHOLEBODY_COREML) //全身
#define MTAIENGINE_MODEL_PHOTOSEG_CW MTAIENGINE_MODEL(PHOTOSEG_CW) //带检测的分割
#define MTAIENGINE_MODEL_PHOTOSEG_FACECONTOUR MTAIENGINE_MODEL(PHOTOSEG_FACECONTOUR)//人脸轮廓分割
#define MTAIENGINE_MODEL_PHOTOSEG_FACECONTOUR_COREML  MTAIENGINE_MODEL(PHOTOSEG_FACECONTOUR_COREML )//人脸轮廓分割
#define MTAIENGINE_MODEL_PHOTOSEG_HEAD MTAIENGINE_MODEL(PHOTOSEG_HEAD)//人脸轮廓分割
#define MTAIENGINE_MODEL_PHOTOSEG_HEAD_COREML MTAIENGINE_MODEL(PHOTOSEG_HEAD_COREML)//人脸轮廓分割
#define MTAIENGINE_MODEL_PHOTOSEG_MIDAS MTAIENGINE_MODEL(PHOTOSEG_MIDAS)//单目深度估计
#define MTAIENGINE_MODEL_PHOTOSEG_MIDAS_COREML MTAIENGINE_MODEL(PHOTOSEG_MIDAS_COREML)//单目深度估计
#define MTAIENGINE_MODEL_PHOTOSEG_MATTING_TRIMAP MTAIENGINE_MODEL(MATTING_TRIMAP) //MATTING_TRIMAP
#define MTAIENGINE_MODEL_PHOTOSEG_MATTING_ALPHA MTAIENGINE_MODEL(MATTING_ALPHA) //MATTING_ALPHA
#define MTAIENGINE_MODEL_PHOTOSEG_SAM MTAIENGINE_MODEL(PHOTOSEG_SAM) //SAM
#define MTAIENGINE_MODEL_PHOTOSEG_SEGMENTATION MTAIENGINE_MODEL(PHOTOSEG_SEGMENTATION)//万物分割
#define MTAIENGINE_MODEL_PHOTOSEG_SEGMENTATION_COREML MTAIENGINE_MODEL(PHOTOSEG_SEGMENTATION_COREML)//万物分割coreml
#define MTAIENGINE_MODEL_PHOTOSEG_VIDEOBODY MTAIENGINE_MODEL(PHOTOSEG_VIDEOBODY)//拍后视频抠图
#define MTAIENGINE_MODEL_PHOTOSEG_VIDEOBODY_COREML MTAIENGINE_MODEL(PHOTOSEG_VIDEOBODY_COREML)//拍后视频抠图coreml
#define MTAIENGINE_MODEL_PHOTOSEG_BLURPORTRAIT MTAIENGINE_MODEL(PHOTOSEG_BLURPORTRAIT)//拍后背景虚化
#define MTAIENGINE_MODEL_PHOTOSEG_BLURPORTRAIT_COREML MTAIENGINE_MODEL(PHOTOSEG_BLURPORTRAIT_COREML)//拍后背景虚化coreml
#define MTAIENGINE_MODEL_PHOTOSEG_SPACEDEPTH MTAIENGINE_MODEL(PHOTOSEG_SPACEDEPTH)//拍后视频空间深度信息
#define MTAIENGINE_MODEL_PHOTOSEG_SPACEDEPTH_COREML MTAIENGINE_MODEL(PHOTOSEG_SPACEDEPTH_COREML)//拍后视频空间深度信息coreml
#define MTAIENGINE_MODEL_PHOTOSEG_VIDEOSKIN MTAIENGINE_MODEL(PHOTOSEG_VIDEOSKIN)//拍后视频皮肤
#define MTAIENGINE_MODEL_PHOTOSEG_VIDEOSKIN_COREML MTAIENGINE_MODEL(PHOTOSEG_VIDEOSKIN_COREML)//拍后视频皮肤coreml
#define MTAIENGINE_MODEL_PHOTOSEG_DEEPBLUR_ARNET MTAIENGINE_MODEL(DEEPBLUR_ARNET) //深度虚化 arnet
#define MTAIENGINE_MODEL_PHOTOSEG_DEEPBLUR_ARNET_COREML MTAIENGINE_MODEL(DEEPBLUR_ARNET_COREML) //深度虚化 arnet coreml
#define MTAIENGINE_MODEL_PHOTOSEG_DEEPBLUR_IUNET MTAIENGINE_MODEL(DEEPBLUR_IUNET) //深度虚化 iunet
#define MTAIENGINE_MODEL_PHOTOSEG_DEEPBLUR_IUNET_COREML MTAIENGINE_MODEL(DEEPBLUR_IUNET_COREML) //深度虚化 iunet coreml
#define MTAIENGINE_MODEL_PHOTOSEG_DEEPBLUR_ARNET_LIGHT MTAIENGINE_MODEL(DEEPBLUR_ARNET_LIGHT) //深度虚化(光斑) arnetB
#define MTAIENGINE_MODEL_PHOTOSEG_DEEPBLUR_IUNET_LIGHT MTAIENGINE_MODEL(DEEPBLUR_IUNET_LIGHT) //深度虚化(光斑) iunetB
#define MTAIENGINE_MODEL_PHOTOSEG_DEEPBLUR_ARNET_LIGHT_COREML MTAIENGINE_MODEL(DEEPBLUR_ARNET_LIGHT_COREML) //深度虚化 arnet coreml
#define MTAIENGINE_MODEL_PHOTOSEG_DEEPBLUR_IUNET_LIGHT_COREML MTAIENGINE_MODEL(DEEPBLUR_IUNET_LIGHT_COREML) //深度虚化 iunet coreml
#define MTAIENGINE_MODEL_PHOTOSEG_MONOCULAR_DEPTH MTAIENGINE_MODEL(PHOTOSEG_MONOCULAR_DEPTH)//深度估计(人体打光项目)
#define MTAIENGINE_MODEL_PHOTOSEG_HUMAN_SEG MTAIENGINE_MODEL(PHOTOSEG_HUMAN_SEG)//移动端人像分割
#define MTAIENGINE_MODEL_PHOTOSEG_HUMAN_MATTING MTAIENGINE_MODEL(PHOTOSEG_HUMAN_MATTING)//移动端人像分割
#define MTAIENGINE_MODEL_PHOTOSEG_HUMAN_SEG_COREML MTAIENGINE_MODEL(PHOTOSEG_HUMAN_SEG_COREML)//移动端人像分割 coreml
#define MTAIENGINE_MODEL_PHOTOSEG_HUMAN_MATTING_COREML MTAIENGINE_MODEL(PHOTOSEG_HUMAN_MATTING_COREML) // 移动端人像分割 coreml
#define MTAIENGINE_MODEL_PHOTOSEG_DEPTH_ANYTHING_CONV MTAIENGINE_MODEL(PHOTOSEG_DEPTH_ANYTHING_CONV)//PhotoDepthAnythingConv.manis
#define MTAIENGINE_MODEL_PHOTOSEG_DEPTH_ANYTHING MTAIENGINE_MODEL(PHOTOSEG_DEPTH_ANYTHING)//PhotoDepthAnything.manis
#define MTAIENGINE_MODEL_PHOTOSEG_DEPTH_ANYTHING_COREML MTAIENGINE_MODEL(PHOTOSEG_DEPTH_ANYTHING_COREML)//coreml PhotoDepthAnything.manisa
#define MTAIENGINE_MODEL_PHOTOSEG_DEPTH_ANYTHING_V2 MTAIENGINE_MODEL(PHOTOSEG_DEPTH_ANYTHING_V2)//PhotoDepthAnything.manis
#define MTAIENGINE_MODEL_PHOTOSEG_DEPTH_ANYTHING_V2_COREML MTAIENGINE_MODEL(PHOTOSEG_DEPTH_ANYTHING_V2_COREML)//coreml PhotoDepthAnything.manisa

#define MTAIENGINE_MODEL_PHOTOSEG_BODY_SERVER MTAIENGINE_MODEL(PHOTOSEG_BODY_SERVER)//服务器人像
#define MTAIENGINE_MODEL_PHOTOSEG_BODY_SERVER_COREML MTAIENGINE_MODEL(PHOTOSEG_BODY_SERVER_COREML)//服务器人像
#define MTAIENGINE_MODEL_PHOTOSEG_HAIR_SERVER MTAIENGINE_MODEL(PHOTOSEG_HAIR_SERVER)//服务器头发
#define MTAIENGINE_MODEL_PHOTOSEG_HAIR_SERVER_COREML MTAIENGINE_MODEL(PHOTOSEG_HAIR_SERVER_COREML)//服务器头发
#define MTAIENGINE_MODEL_PHOTOSEG_SKY_SERVER MTAIENGINE_MODEL(PHOTOSEG_SKY_SERVER)//服务器天空
#define MTAIENGINE_MODEL_PHOTOSEG_SKY_SERVER_COREML MTAIENGINE_MODEL(PHOTOSEG_SKY_SERVER_COREML)//服务器天空
#define MTAIENGINE_MODEL_PHOTOSEG_SKIN_SERVER MTAIENGINE_MODEL(PHOTOSEG_SKIN_SERVER)//服务器皮肤
#define MTAIENGINE_MODEL_PHOTOSEG_SKIN_SERVER_COREML MTAIENGINE_MODEL(PHOTOSEG_SKIN_SERVER_COREML)//服务器皮肤
#define MTAIENGINE_MODEL_PHOTOSEG_HEAD_SERVER MTAIENGINE_MODEL(PHOTOSEG_HEAD_SERVER)//服务器头部
#define MTAIENGINE_MODEL_PHOTOSEG_HEAD_SERVER_COREML MTAIENGINE_MODEL(PHOTOSEG_HEAD_SERVER_COREML)//服务器头部
#define MTAIENGINE_MODEL_PHOTOSEG_CLOTH MTAIENGINE_MODEL(PHOTOSEG_CLOTH) // 衣服
#define MTAIENGINE_MODEL_PHOTOSEG_CLOTH_COREML MTAIENGINE_MODEL(PHOTOSEG_CLOTH_COREML) // 衣服

#define MTAIENGINE_MODEL_BROWSEG_PHOTO MTAIENGINE_MODEL(BROWSEG_PHOTO)// 眉毛分割拍后
#define MTAIENGINE_MODEL_BROWSEG_REALTIME MTAIENGINE_MODEL(BROWSEG_REALTIME)// 眉毛分割实时

// 拍后分割模型
#define MTAIENGINE_MERAK_PHOTOSEG_HALFBODY MTAIENGINE_MERAK(PHOTOSEG_HALFBODY) //半身
#define MTAIENGINE_MERAK_PHOTOSEG_HAIR MTAIENGINE_MERAK(PHOTOSEG_HAIR) //头发
#define MTAIENGINE_MERAK_PHOTOSEG_SKY MTAIENGINE_MERAK(PHOTOSEG_SKY) //天空
#define MTAIENGINE_MERAK_PHOTOSEG_SKIN MTAIENGINE_MERAK(PHOTOSEG_SKIN) //皮肤
#define MTAIENGINE_MERAK_PHOTOSEG_FACIAL MTAIENGINE_MERAK(PHOTOSEG_FACIAL) //五官
#define MTAIENGINE_MERAK_PHOTOSEG_WHOLEBODY MTAIENGINE_MERAK(PHOTOSEG_WHOLEBODY) //全身
#define MTAIENGINE_MERAK_PHOTOSEG_FACECONTOUR MTAIENGINE_MERAK(PHOTOSEG_FACECONTOUR)//人脸轮廓分割
#define MTAIENGINE_MERAK_PHOTOSEG_HEAD MTAIENGINE_MERAK(PHOTOSEG_HEAD)//人脸轮廓分割
#define MTAIENGINE_MERAK_PHOTOSEG_MIDAS MTAIENGINE_MERAK(PHOTOSEG_MIDAS)//单目深度估计
#define MTAIENGINE_MERAK_PHOTOSEG_MATTING_TRIMAP MTAIENGINE_MERAK(MATTING_TRIMAP) //MATTING_TRIMAP
#define MTAIENGINE_MERAK_PHOTOSEG_MATTING_ALPHA MTAIENGINE_MERAK(MATTING_ALPHA) //MATTING_ALPHA
#define MTAIENGINE_MERAK_PHOTOSEG_SEGMENTATION MTAIENGINE_MODEL(PHOTOSEG_SEGMENTATION)//万物分割
#define MTAIENGINE_MERAK_PHOTOSEG_VIDEOBODY MTAIENGINE_MERAK(PHOTOSEG_VIDEOBODY)//拍后视频抠图
#define MTAIENGINE_MERAK_PHOTOSEG_BLURPORTRAIT MTAIENGINE_MERAK(PHOTOSEG_BLURPORTRAIT)//拍后背景虚化

#define MTAIENGINE_MERAK_PHOTOSEG_BODY_SERVER MTAIENGINE_MERAK(PHOTOSEG_BODY_SERVER)//服务器人像
#define MTAIENGINE_MERAK_PHOTOSEG_HAIR_SERVER MTAIENGINE_MERAK(PHOTOSEG_HAIR_SERVER)//服务器头发
#define MTAIENGINE_MERAK_PHOTOSEG_SKY_SERVER MTAIENGINE_MERAK(PHOTOSEG_SKY_SERVER)//服务器天空
#define MTAIENGINE_MERAK_PHOTOSEG_SKIN_SERVER MTAIENGINE_MERAK(PHOTOSEG_SKIN_SERVER)//服务器皮肤
#define MTAIENGINE_MERAK_PHOTOSEG_HEAD_SERVER MTAIENGINE_MERAK(PHOTOSEG_HEAD_SERVER)//服务器头部
#define MTAIENGINE_MERAK_PHOTOSEG_CLOTH MTAIENGINE_MERAK(PHOTOSEG_CLOTH) // 半身
#define MTAIENGINE_MODEL_PHOTOSEG_FOREGROUND MTAIENGINE_MODEL(PHOTOSEG_FOREGROUND) //
#define MTAIENGINE_MODEL_PHOTOSEG_FOREGROUND_COREML MTAIENGINE_MODEL(PHOTOSEG_FOREGROUND_COREML) //

// 实时分割模型
#define MTAIENGINE_MERAK_REALTIMESEG_HALFBODY MTAIENGINE_MERAK(REALTIMESEG_HALFBODY) //半身
#define MTAIENGINE_MERAK_REALTIMESEG_HAIR MTAIENGINE_MERAK(REALTIMESEG_HAIR) //头发
#define MTAIENGINE_MERAK_REALTIMESEG_SKY MTAIENGINE_MERAK(REALTIMESEG_SKY) //天空
#define MTAIENGINE_MERAK_REALTIMESEG_SKIN MTAIENGINE_MERAK(REALTIMESEG_SKIN) //皮肤
#define MTAIENGINE_MERAK_REALTIMESEG_WHOLEBODY MTAIENGINE_MERAK(REALTIMESEG_WHOLEBODY) //全身
#define MTAIENGINE_MERAK_REALTIMESEG_HEAD MTAIENGINE_MERAK(REALTIMESEG_HEAD) //头部分割
#define MTAIENGINE_MERAK_REALTIMESEG_CLOTH MTAIENGINE_MERAK(REALTIMESEG_CLOTH) // 衣服分割
#define MTAIENGINE_MERAK_REALTIMESEG_MUTI MTAIENGINE_MERAK(REALTIMESEG_MUTI) // 多分割
#define MTAIENGINE_MERAK_REALTIMESEG_MIDAS MTAIENGINE_MERAK(REALTIMESEG_MIDAS) // 单目深度估计
#define MTAIENGINE_MERAK_REALTIMESEG_BLURPORTRAIT MTAIENGINE_MERAK(REALTIMESEG_BLURPORTRAIT) // 背景虚化
#define MTAIENGINE_MERAK_REALTIMESEG_VIDEOBODY MTAIENGINE_MERAK(REALTIMESEG_VIDEOBODY) // 实时视频抠图

#define MTAIENGINE_MERAK_REALTIMESEG_HALFBODY_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_HALFBODY_TETURE_TEXTURE) //半身
#define MTAIENGINE_MERAK_REALTIMESEG_HAIR_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_HAIR_TEXTURE) //头发
#define MTAIENGINE_MERAK_REALTIMESEG_SKY_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_SKY_TEXTURE) //天空
#define MTAIENGINE_MERAK_REALTIMESEG_SKIN_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_SKIN_TEXTURE) //皮肤
#define MTAIENGINE_MERAK_REALTIMESEG_WHOLEBODY_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_WHOLEBODY_TEXTURE) //全身
#define MTAIENGINE_MERAK_REALTIMESEG_HEAD_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_HEAD_TEXTURE) //头部分割
#define MTAIENGINE_MERAK_REALTIMESEG_CLOTH_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_CLOTH_TEXTURE) // 衣服分割
#define MTAIENGINE_MERAK_REALTIMESEG_MUTI_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_MUTI_TEXTURE) // 多分割
#define MTAIENGINE_MERAK_REALTIMESEG_MIDAS_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_MIDAS_TEXTURE) // 单目深度估计
#define MTAIENGINE_MERAK_REALTIMESEG_BLURPORTRAIT_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_BLURPORTRAIT_TEXTURE) // 背景虚化
#define MTAIENGINE_MERAK_REALTIMESEG_VIDEOBODY_TEXTURE MTAIENGINE_MERAK(REALTIMESEG_VIDEOBODY_TEXTURE) // 实时视频抠图

// 妆容识别
#define MTAIENGINE_MODEL_MAKEUP_EYE MTAIENGINE_MODEL(MAKEUP_EYE) // 模型：眼睛
#define MTAIENGINE_MODEL_MAKEUP_BROW MTAIENGINE_MODEL(MAKEUP_BROW) // 模型：眉毛
#define MTAIENGINE_MODEL_MAKEUP_MOUTH MTAIENGINE_MODEL(MAKEUP_MOUTH) // 模型：嘴唇
#define MTAIENGINE_MODEL_MAKEUP_CHEEK MTAIENGINE_MODEL(MAKEUP_CHEEK) // 模型：脸颊

// 新版本妆容识别 移动端
#define MTAIENGINE_MODEL_MAKEUP_V2_EYE MTAIENGINE_MODEL(MAKEUP_V2_EYE)
#define MTAIENGINE_MODEL_MAKEUP_V2_BROW MTAIENGINE_MODEL(MAKEUP_V2_BROW)
#define MTAIENGINE_MODEL_MAKEUP_V2_CHEEK MTAIENGINE_MODEL(MAKEUP_V2_CHEEK)
#define MTAIENGINE_MODEL_MAKEUP_V2_MOUTH MTAIENGINE_MODEL(MAKEUP_V2_MOUTH)
#define MTAIENGINE_MODEL_MAKEUP_V2_FACE MTAIENGINE_MODEL(MAKEUP_V2_FACE)

// 新版本妆容识别 服务端
#define MTAIENGINE_MODEL_MAKEUP_V2_HEAVY_EYE MTAIENGINE_MODEL(MAKEUP_V2_HEAVY_EYE)
#define MTAIENGINE_MODEL_MAKEUP_V2_HEAVY_BROW MTAIENGINE_MODEL(MAKEUP_V2_HEAVY_BROW)
#define MTAIENGINE_MODEL_MAKEUP_V2_HEAVY_CHEEK MTAIENGINE_MODEL(MAKEUP_V2_HEAVY_CHEEK)
#define MTAIENGINE_MODEL_MAKEUP_V2_HEAVY_MOUTH MTAIENGINE_MODEL(MAKEUP_V2_HEAVY_MOUTH)
#define MTAIENGINE_MODEL_MAKEUP_V2_HEAVY_FACE MTAIENGINE_MODEL(MAKEUP_V2_HEAVY_FACE)

// 新版本妆容识别 宜肤移动端
#define MTAIENGINE_MODEL_MAKEUP_V2_EVE_EYE MTAIENGINE_MODEL(MAKEUP_V2_EVE_EYE)
#define MTAIENGINE_MODEL_MAKEUP_V2_EVE_BROW MTAIENGINE_MODEL(MAKEUP_V2_EVE_BROW)
#define MTAIENGINE_MODEL_MAKEUP_V2_EVE_CHEEK MTAIENGINE_MODEL(MAKEUP_V2_EVE_CHEEK)
#define MTAIENGINE_MODEL_MAKEUP_V2_EVE_MOUTH MTAIENGINE_MODEL(MAKEUP_V2_EVE_MOUTH)

// 新版本妆容识别 宜肤服务端
#define MTAIENGINE_MODEL_MAKEUP_V2_EVE_HEAVY_EYE MTAIENGINE_MODEL(MAKEUP_V2_EVE_HEAVY_EYE)
#define MTAIENGINE_MODEL_MAKEUP_V2_EVE_HEAVY_BROW MTAIENGINE_MODEL(MAKEUP_V2_EVE_HEAVY_BROW)
#define MTAIENGINE_MODEL_MAKEUP_V2_EVE_HEAVY_CHEEK MTAIENGINE_MODEL(MAKEUP_V2_EVE_HEAVY_CHEEK)
#define MTAIENGINE_MODEL_MAKEUP_V2_EVE_HEAVY_MOUTH MTAIENGINE_MODEL(MAKEUP_V2_EVE_HEAVY_MOUTH)


// 皮肤分析模型
#define MTAIENGINE_MODEL_SKIN_EYEWRINKLE MTAIENGINE_MODEL(SKIN_EYEWRINKLE)                       ///<< 前置&后置：皱纹分类器模型：EW.manis
#define MTAIENGINE_MODEL_SKIN_EYEFINELINE MTAIENGINE_MODEL(SKIN_EYEFINELINE)                     ///<< 前置&后置：眼部细纹模型：EFL.manis
#define MTAIENGINE_MODEL_SKIN_NEVUS MTAIENGINE_MODEL(SKIN_NEVUS)                                 ///<< 前置/后置：基于RPN的痣检测，模型：NE.manis
#define MTAIENGINE_MODEL_SKIN_SKINTONE MTAIENGINE_MODEL(SKIN_SKINTONE)                           ///<< 前置&后置：基于皮肤分割的肤色识别，模型：PSL107.manis
#define MTAIENGINE_MODEL_SKIN_ACNE_FRONT MTAIENGINE_MODEL(SKIN_ACNE_FRONT)                       ///<< 前置：基于RPN的前置痘痘+痘印检测，模型：GA.manis

#define MTAIENGINE_MODEL_SKIN_ROSACEA_NOSE_BACK MTAIENGINE_MODEL(SKIN_ROSACEA_NOSE_BACK)         ///<< 后置：玫瑰痤疮检测-鼻周模型：RNS.manis
#define MTAIENGINE_MODEL_SKIN_ROSACEA_CHEEK_BACK MTAIENGINE_MODEL(SKIN_ROSACEA_CHEEK_BACK)       ///<< 后置：玫瑰痤疮检测-左右脸模型：RCK.manis
#define MTAIENGINE_MODEL_SKIN_ROSACEA_FOREHEAD_BACK MTAIENGINE_MODEL(SKIN_ROSACEA_FOREHEAD_BACK) ///<< 后置：玫瑰痤疮检测-前额模型：RFH.manis
#define MTAIENGINE_MODEL_SKIN_ROSACEA_CHIN_BACK MTAIENGINE_MODEL(SKIN_ROSACEA_CHIN_BACK)         ///<< 后置：玫瑰痤疮检测-下巴模型：RCN.manis
#define MTAIENGINE_MODEL_SKIN_FLAW_FD2_BACK MTAIENGINE_MODEL(SKIN_FLAW_FD2_BACK)         ///<< 后置：基于YOLO2的瑕疵分类，模型：FD2.manis
#define MTAIENGINE_MODEL_SKIN_FLAW_FFC_BACK MTAIENGINE_MODEL(SKIN_FLAW_FFC_BACK)         ///<< 后置：基于YOLO2的瑕疵分类，模型：FFC.manis
#define MTAIENGINE_MODEL_SKIN_EYEBAG_SEGMENTER_BACK MTAIENGINE_MODEL(SKIN_EYEBAG_SEGMENTER_BACK) ///<< 后置：眼袋分割， 模型：EBS.manis
#define MTAIENGINE_MODEL_SKIN_BLACKHEAD_BACK MTAIENGINE_MODEL(SKIN_BLACKHEAD_BACK)               ///<< 后置：基于ICNet的黑头分割，模型：BH2.manis
#define MTAIENGINE_MODEL_SKIN_BLACKHEAD_BACK_V3 MTAIENGINE_MODEL(SKIN_BLACKHEAD_BACK_V3)         ///<< 后置：基于ICNet的黑头分割，模型：BH3.manis
#define MTAIENGINE_MODEL_SKIN_TEARTHROUGH_BACK MTAIENGINE_MODEL(SKIN_TEARTHROUGH_BACK)           ///<< 后置：泪沟分割，模型：TTS.manis
#define MTAIENGINE_MODEL_SKIN_EYE_BROW_LINE MTAIENGINE_MODEL(SKIN_EYE_BROW_LINE)                  ///<< 眉间纹检测，模型：eyebrowline_kitty_ipad.manis

#define MTAIENGINE_MODEL_SKIN_FOREHEAD_WRINKLE MTAIENGINE_MODEL(SKIN_FOREHEAD_WRINKLE)           ///<< 前置&后置：额头皱纹，模型：FHW.manis
#define MTAIENGINE_MODEL_SKIN_FOREHEAD_FRONT MTAIENGINE_MODEL(SKIN_FOREHEAD_FRONT)               ///<< 前置：抬头纹，模型：FFH.manis

#define MTAIENGINE_MODEL_SKIN_CROWSFEET MTAIENGINE_MODEL(SKIN_CROWSFEET)                         ///<< 前置&后置：鱼尾纹分类器，模型：CF.manis
#define MTAIENGINE_MODEL_SKIN_CROWSFEET_FRONT MTAIENGINE_MODEL(SKIN_CROWSFEET_FRONT)             ///<< 前置：鱼尾纹，模型：FCF.manis

#define MTAIENGINE_MODEL_SKIN_NASOLABIALFOLDS MTAIENGINE_MODEL(SKIN_NASOLABIALFOLDS)             ///<< 前置&后置：法令纹分类器，模型：NF.manis
#define MTAIENGINE_MODEL_SKIN_NASOLABIALFOLDS_FRONT MTAIENGINE_MODEL(SKIN_NASOLABIALFOLDS_FRONT) ///<< 前置：法令纹，模型：FNS.manis

#if MTAIENGINE_SKIN_VERSION >= MTAIENGINE_SKIN_VERSION_1_1_03
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_V2_FOREHEAD_WRINKLE MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_V2_FOREHEAD_WRINKLE)  ///<< 抬头纹，模型： Wrinkle_Forehead.manis
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_V2_CROWSFEET MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_V2_CROWSFEET)                ///<< 鱼尾纹，模型： Wrinkle_Crowsfeet.manis
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_V2_NASOLABIALFOLDS MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_V2_NASOLABIALFOLDS)    ///<< 法令纹，模型： MTEveWrinkle_Nasolabial.manis
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_V2_EYE MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_V2_EYE)                            ///<< 眼纹，模型：   Wrinkle_Eye.manis
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_V2_MOUTH_CORNER MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_V2_MOUTH_CORNER)          ///<< 口角纹，模型： Wrinkle_MouthCorner.manis
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_V2_NASOLABIALFOLDS_B MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_V2_NASOLABIALFOLDS_B)    ///<< 法令纹，模型： Wrinkle_Nasolabial.manis
#else
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_FOREHEAD_WRINKLE MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_FOREHEAD_WRINKLE)  ///<< 后置：抬头纹，模型：MTEveWrinkle_Forehead.manis
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_CROWSFEET MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_CROWSFEET)                ///<< 后置：鱼尾纹，模型：MTEveWrinkle_Crowsfeet.manis
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_NASOLABIALFOLDS MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_NASOLABIALFOLDS)    ///<< 后置：法令纹，模型：MTEveWrinkle_Nasolabial.manis
#define MTAIENGINE_MODEL_SKIN_WRINKLE_LYH_PORT_EYEFRONT MTAIENGINE_MODEL(SKIN_WRINKLE_LYH_PORT_EYEFRONT)                  ///<< 后置：眼纹，模型：MTEveWrinkle_EyeFront.manis
#endif

#define MTAIENGINE_MODEL_SKIN_PORES_CHEEKS_BACK MTAIENGINE_MODEL(SKIN_PORES_CHEEKS_BACK)         ///<< 后置：毛孔粗大-脸颊区域 检测器,模型：P.manis
#define MTAIENGINE_MODEL_SKIN_PORES_BETWEENBROW_BACK MTAIENGINE_MODEL(SKIN_PORES_BETWEENBROW_BACK) ///<< 后置：毛孔粗大-眉心区域 检测器,模型：PBB.manis
#define MTAIENGINE_MODEL_SKIN_PORES_FOREHEAD_BACK MTAIENGINE_MODEL(SKIN_PORES_FOREHEAD_BACK)     ///<< 后置：毛孔粗大-额头区域 检测器,模型：PFH.manis
#define MTAIENGINE_MODEL_SKIN_PORES_CHEEK_FRONT MTAIENGINE_MODEL(SKIN_PORES_CHEEK_FRONT)         ///<< 前置：毛孔粗大-脸颊,模型：PF_C.manis
#define MTAIENGINE_MODEL_SKIN_PORES_FRONTHEAD_FRONT MTAIENGINE_MODEL(SKIN_PORES_FRONTHEAD_FRONT) ///<< 前置：毛孔粗大-额头眉心,模型：PF_FHBW.manis

#define MTAIENGINE_MODEL_SKIN_PORES_SEGMENTATION MTAIENGINE_MODEL(SKIN_PORES_SEGMENTATION)       ///<< 后置：毛孔分割,模型：pores_seg.manis
#define MTAIENGINE_MODEL_SKIN_PORES_SEGMENTATION_2 MTAIENGINE_MODEL(SKIN_PORES_SEGMENTATION_2)   ///<< 后置：毛孔分割,模型：poreseg2a.manis

#define MTAIENGINE_MODEL_SKIN_ACNEMARK_BACK MTAIENGINE_MODEL(SKIN_ACNEMARK_BACK)                 ///<< 后置：痘印检测，模型：PAD.manis
#define MTAIENGINE_MODEL_SKIN_ACNEMARK_FRONT MTAIENGINE_MODEL(SKIN_ACNEMARK_FRONT)               ///<< 前置：痘印检测，模型：FAD.manis

#define MTAIENGINE_MODEL_SKIN_PANDAEYE_FINEGRAINED_CLASSIFER_BACK MTAIENGINE_MODEL(SKIN_PANDAEYE_FINEGRAINED_CLASSIFER_BACK) ///<< 后置：黑眼圈细粒度分类器，模型：PECFG.manis
#define MTAIENGINE_MODEL_SKIN_PANDAEYE_FRONT MTAIENGINE_MODEL(SKIN_PANDAEYE_FRONT)               ///<< 通用前置：黑眼圈，模型：front_pandaeyes.manis
#define MTAIENGINE_MODEL_SKIN_PANDAEYE_CLASSIFER_FRONT MTAIENGINE_MODEL(SKIN_PANDAEYE_CLASSIFER_FRONT) ///<< 美图前置：黑眼圈检测器和分类器，模型：PEPEC.manis


#define MTAIENGINE_MODEL_SKIN_EYE_STAIN_V3 MTAIENGINE_MODEL(SKIN_EYE_STAIN_V3)  ///<< 眼部色斑，模型：eye_stainv3.manis
#define MTAIENGINE_MODEL_SKIN_SC MTAIENGINE_MODEL(SKIN_SC)                      ///<< 色斑分类，模型：SC.manis
#define MTAIENGINE_MODEL_SKIN_EYELID MTAIENGINE_MODEL(SKIN_EYELID)              ///<< 眼皮下垂，模型：EyeLid.manis
#define MTAIENGINE_MODEL_SKIN_EYELID_PUFF MTAIENGINE_MODEL(SKIN_EYELID_PUFF)    ///<< 眼皮浮肿，模型：eyelidpuff.manis
#define MTAIENGINE_MODEL_SKIN_EYE_SEG MTAIENGINE_MODEL(SKIN_EYE_SEG)            ///<< 眼瞳明亮度，模型：EyeSeg.manis
#define MTAIENGINE_MODEL_SKIN_STAIN_DETECTION MTAIENGINE_MODEL(SKIN_STAIN_DETECTION)            ///<< 全脸多种色斑检测，模型:stain_v1.manis
#define MTAIENGINE_MODEL_SKIN_ACNE_DETECTION MTAIENGINE_MODEL(SKIN_ACNE_DETECTION)              ///<< 全脸多种痤疮检测，模型:acne_v1.manis
#define MTAIENGINE_MODEL_SKIN_META_API MTAIENGINE_MODEL(SKIN_META_API)                          ///<< json报告元数据，模型:SkinApi.json
#define MTAIENGINE_MODEL_SKIN_USER_SKIN_TYPE_CLASSIFIER MTAIENGINE_MODEL(SKIN_USER_SKIN_TYPE_CLASSIFIER)         ///<< 用户画像-肤质分类，模型:UserSkinType.manis
#define MTAIENGINE_MODEL_SKIN_USER_WRINKLE_SEG MTAIENGINE_MODEL(SKIN_USER_WRINKLE_SEG)          ///<< 用户画像-皱纹分割：模型：UserWrinkleSeg.manis
#define MTAIENGINE_MODEL_SKIN_FULL_FACE_SEG MTAIENGINE_MODEL(SKIN_FULL_FACE_SEG)                                 ///<< 全脸分割：模型：FullFaceSegMini.manis
#define MTAIENGINE_MODEL_SKIN_USER_SENSIVITITY_CLASSIFIER MTAIENGINE_MODEL(SKIN_USER_SENSIVITITY_CLASSIFIER)     ///<< 用户画像-敏感度判断：模型：user_sensitivity.manis
#define MTAIENGINE_MODEL_SKIN_USER_PANDA_EYE_CLASSIFIER MTAIENGINE_MODEL(SKIN_USER_PANDA_EYE_CLASSIFIER)         ///<< 用户画像 黑眼圈分类：模型：UserPandaEye.maniss
#define MTAIENGINE_MODEL_SKIN_ACNE_FLECK_DETECTION MTAIENGINE_MODEL(SKIN_ACNE_FLECK_DETECTION)  ///<< 痤疮色斑检测，模型：AcneFleck.manis
#define MTAIENGINE_MODEL_SKIN_PORE_BLACKHEAD_SEG MTAIENGINE_MODEL(SKIN_PORE_BLACKHEAD_SEG)  ///<< 全种族毛孔黑头模型，模型:PoreBlackheadSeg.manis
#define MTAIENGINE_MODEL_SKIN_SENS MTAIENGINE_MODEL(SKIN_SENS)                  ///<< 敏感DL，模型:SkinSens.manis

// 美食检测模型
#define MTAIENGINE_MODEL_FOOD_DETECTOR   MTAIENGINE_MODEL(FOOD_DETECTOR) //检测模型
#define MTAIENGINE_MODEL_FOOD_CLASSIFY   MTAIENGINE_MODEL(FOOD_CLASSIFY) //分类模型

// 素材跟随模型
#define MTAIENGINE_MODEL_MATERIAL_TRACKING   MTAIENGINE_MODEL(MATERIAL_TRACKING) //检测模型: mttm.bin

// 肩膀点检测模型
#define MTAIENGINE_MODEL_SHOULDER_DETECTION MTAIENGINE_MODEL(SHOULDER_DETECTION) // 模型：shoulder_point_detection.manis
#define MTAIENGINE_MODEL_SHOULDER_A MTAIENGINE_MODEL(SHOULDER_A) // 模型：ct_detectA.manis
#define MTAIENGINE_MODEL_SHOULDER_B MTAIENGINE_MODEL(SHOULDER_B) // 模型：ct_detectB.manis

// 实例分割检测模型
#define MTAIENGINE_MODEL_INSTANCESEG_BACKONE_DETECTION MTAIENGINE_MODEL(INSTANCESEG_BACKONE_DETECTION) // 模型：InstanceSeg_backone.manis
#define MTAIENGINE_MODEL_INSTANCESEG_MASK_DETECTION MTAIENGINE_MODEL(INSTANCESEG_MASK_DETECTION) // 模型：InstanceSeg_mask.manis
#define MTAIENGINE_MODEL_INSTANCESEG_A_DETECTION MTAIENGINE_MODEL(INSTANCESEG_A_DETECTION) // 模型：InstanceSeg_detectionA.manis
#define MTAIENGINE_MODEL_INSTANCESEG_B_DETECTION MTAIENGINE_MODEL(INSTANCESEG_B_DETECTION) // 模型：InstanceSeg_detectionB.manis
#define MTAIENGINE_MODEL_INSTANCESEG_MASK_EXTRA_DETECTION MTAIENGINE_MODEL(INSTANCESEG_MASK_EXTRA_DETECTION) // 模型：InstanceSeg_mask_extra.manis
#define MTAIENGINE_MODEL_INSTANCESEG_MASK_LOGITS_DETECTION MTAIENGINE_MODEL(INSTANCESEG_MASK_LOGITS_DETECTION) // 模型：InstanceSeg_mask_logits.manis
#define MTAIENGINE_MODEL_INSTANCESEG_MASK_SHARED_DETECTION MTAIENGINE_MODEL(INSTANCESEG_MASK_SHARED_DETECTION) // 模型：InstanceSeg_mask_shared.manis
#define MTAIENGINE_MODEL_INSTANCESEG_MASK_EXTRA_OTHER_DETECTION MTAIENGINE_MODEL(INSTANCESEG_MASK_EXTRA_OTHER_DETECTION) // 模型：InstanceSeg_mask_extra_other.manis
#define MTAIENGINE_MODEL_INSTANCESEG_MASK_LOGITS_OTHER_DETECTION MTAIENGINE_MODEL(INSTANCESEG_MASK_LOGITS_OTHER_DETECTION) // 模型：InstanceSeg_mask_logits_other.manis
#define MTAIENGINE_MODEL_INSTANCESEG_MASK_SHARED_OTHER_DETECTION MTAIENGINE_MODEL(INSTANCESEG_MASK_SHARED_OTHER_DETECTION) // 模型：InstanceSeg_mask_shared_other.manis

// 配饰检测模型
#define MTAIENGINE_MODEL_ORNAMENT_DETECT MTAIENGINE_MODEL(ORNAMENT_DETECT)  // 模型：epoch_22_1p0.manis


// 线稿模型 
#define MTAIENGINE_MODEL_CSKETCH_BODY           MTAIENGINE_MODEL(CSKETCH_BODY)            ///body_net.manis
#define MTAIENGINE_MODEL_CSKETCH_FACE           MTAIENGINE_MODEL(CSKETCH_FACE)            ///face_net.manis

// 发型分类模型
#define MTAIENGINE_MODEL_HAIR_CLASSIFIER_HCLF0      MTAIENGINE_MODEL(HAIR_CLASSIFIER_HCLF0)  //模型 mt_hclf_0.bin
#define MTAIENGINE_MODEL_HAIR_CLASSIFIER_HCLF1      MTAIENGINE_MODEL(HAIR_CLASSIFIER_HCLF1)  //模型 mt_hclf_1.bin

// 背景填充模型
#define MTAIENGINE_MODEL_PORTRAIT_INPAINTING    MTAIENGINE_MODEL(PORTRAIT_INPAINTING)  //模型 portrait_inpainting.manis

// 超清人像-(设置模型路径, 引擎会加载所有放置于该路径下的所有模型)
#define MTAIENGINE_MODEL_FACE_HD      MTAIENGINE_MODEL(FACE_HD)  // 模型路径

// 变小孩模型
#define MTAIENGINE_MODEL_TO_KID   MTAIENGINE_MODEL(TO_KID)     // 模型: adult2kid_model.manis

// 图片识别模型
#define MTAIENGINE_MODEL_IMAGE_RECOGNITION   MTAIENGINE_MODEL(IMAGE_RECOGNITION)     // 模型: ImageRecognition.manis
#define MTAIENGINE_MODEL_SCENE_BASE   MTAIENGINE_MODEL(SCENE_BASE)     // 模型: sceneBase.manis
#define MTAIENGINE_MODEL_SCENE_BASE_DETECT   MTAIENGINE_MODEL(SCENE_BASE_DETECT)     // 模型: sceneBaseDetect.manis
#define MTAIENGINE_MODEL_PREGNANT_WOMAN   MTAIENGINE_MODEL(PREGNANT_WOMAN)     // 模型: pregnantWoman.manis

// 锚点生成模型
#define MTAIENGINE_MODEL_ANCHOR_GENERATION_A   MTAIENGINE_MODEL(ANCHOR_GENERATION_A)     // 模型: AnchorGeneration_A.manis
#define MTAIENGINE_MODEL_ANCHOR_GENERATION_B   MTAIENGINE_MODEL(ANCHOR_GENERATION_B)     // 模型: AnchorGeneration_B.manis

// 三维物体识别模型
#define MTAIENGINE_MODEL_COCA_BOTTLE   MTAIENGINE_MODEL(COCA_BOTTLE)     // 模型: coca_bottle.bin

// 去水印模型
#define MTAIENGINE_MODEL_REMOVE_WATERMARK   MTAIENGINE_MODEL(REMOVE_WATERMARK)     // 模型: remove_watermark.manis

// 图像检测模型
#define MTAIENGINE_MODEL_IMAGE_DETECTION_A_HUMAN   MTAIENGINE_MODEL(IMAGE_DETECTION_A_HUMAN)     // 模型: image_detction_human_A.manis
#define MTAIENGINE_MODEL_IMAGE_DETECTION_B_HUMAN   MTAIENGINE_MODEL(IMAGE_DETECTION_B_HUMAN)     // 模型: image_detction_human_B.manis
#define MTAIENGINE_MODEL_IMAGE_DETECTION_A_SCENE   MTAIENGINE_MODEL(IMAGE_DETECTION_A_SCENE)     // 模型: image_detction_scene_A.manis
#define MTAIENGINE_MODEL_IMAGE_DETECTION_B_SCENE   MTAIENGINE_MODEL(IMAGE_DETECTION_B_SCENE)     // 模型: image_detction_scene_B.manis

// 拍后肢体模型
#define MTAIENGINE_MODEL_PHOTO_BODYPOSE   MTAIENGINE_MODEL(PHOTO_BODYPOSE)     // 模型: v0.9..16_d950.manis

// 3D人脸重建模型
#define MTAIENGINE_MODEL_DL3D_NET MTAIENGINE_MODEL(DL3D_NET)                // 模型： 网络模型 model.manis
#define MTAIENGINE_MODEL_DL3D_RIGGING MTAIENGINE_MODEL(DL3D_RIGGING)        // 模型： rigging模型 rigging.manis
#define MTAIENGINE_MODEL_DL3D_MESH MTAIENGINE_MODEL(DL3D_MESH)              // 模型： 3D模型 CoreTensor.bin

// 美牙模型
#define MTAIENGINE_MODEL_TEETH_REFINE MTAIENGINE_MODEL(TEETH_REFINE) // 模型：n2n_teeth.manis
#define MTAIENGINE_MODEL_TEETH_CLASSIFY MTAIENGINE_MODEL(TEETH_CLASSIFY) // 模型：squeezenet_teeth_classify.manis

#define MTAIENGINE_MODEL_EVE_SKIN_FAKE_RBX_CONFIG MTAIENGINE_MODEL(EVE_SKIN_FAKE_RBX_CONFIG)   // 配置文件: rbx配置文件rbx.xml
#define MTAIENGINE_MODEL_EVE_SKIN_FLECK_ACNE_CONFIG MTAIENGINE_MODEL(EVE_SKIN_FLECK_ACNE_CONFIG)   //配置文件: 痘痘和斑配置文件 fleck_acne.xml
#define MTAIENGINE_MODEL_EVE_SKIN_ACNE_FD2 MTAIENGINE_MODEL(EVE_SKIN_ACNE_FD2)   // 模型: 痘痘检测 FD2.manis
#define MTAIENGINE_MODEL_EVE_SKIN_FLECK_SD MTAIENGINE_MODEL(EVE_SKIN_FLECK_SD)   // 模型: 斑检测 SD.manis
#define MTAIENGINE_MODEL_EVE_SKIN_FLECK_SC MTAIENGINE_MODEL(EVE_SKIN_FLECK_SC)   // 模型: 版分类 SC.manis
#define MTAIENGINE_MODEL_EVE_SKIN_KERATINPLUG_PORPHYRIN_CONFIG MTAIENGINE_MODEL(EVE_SKIN_KERATINPLUG_PORPHYRIN_CONFIG)   // 配置文件: 紫质和角质栓配置文件 keratinplug_porphyrin.xml(包含肤质配置信息)
#define MTAIENGINE_MODEL_EVE_SKIN_KERATINPLUG MTAIENGINE_MODEL(EVE_SKIN_KERATINPLUG)   // 模型: 角质栓检测 keratinplug.manis
#define MTAIENGINE_MODEL_EVE_SKIN_SENSITIVITY_CONFIG MTAIENGINE_MODEL(EVE_SKIN_SENSITIVITY_CONFIG)   // 配置文件: 敏感性 sensitivity.xml
#define MTAIENGINE_MODEL_EVE_SKIN_TONE_CONFIG MTAIENGINE_MODEL(EVE_SKIN_TONE_CONFIG)   // 配置文件: 肤色 skincolor.xml
#define MTAIENGINE_MODEL_EVE_SKIN_TONE MTAIENGINE_MODEL(EVE_SKIN_TONE)   // 模型: 肤色 PSL107.manis
#define MTAIENGINE_MODEL_EVE_SKIN_TYPE_CONFIG MTAIENGINE_MODEL(EVE_SKIN_TYPE_CONFIG)   // 配置文件: 肤质 keratinplug_porphyrin.xml(包含肤质配置信息)

// 皮肤癌检测模型: bcc.manis
#define MTAIENGINE_MODEL_SKINBCC   MTAIENGINE_MODEL(SKINBCC)

// 3DFace模型
#define MTAIENGINE_MODEL_3DFACE_CONTOURVERTEX MTAIENGINE_MODEL(3DFACE_CONTOURVERTEX) // 模型：  ContourVertex.bin
#define MTAIENGINE_MODEL_3DFACE_EXPRESSMAT_INITPARAM MTAIENGINE_MODEL(3DFACE_EXPRESSMAT_INITPARAM) // 模型： ExpressMat_InitParam.bin
#define MTAIENGINE_MODEL_3DFACE_LANMARK MTAIENGINE_MODEL(3DFACE_LANMARK) // 模型： Lanmark.bin
#define MTAIENGINE_MODEL_3DFACE_MODELCORE MTAIENGINE_MODEL(3DFACE_MODELCORE) // 模型： ModelCore.bin
#define MTAIENGINE_MODEL_3DFACE_UVMAP_3DOBJ MTAIENGINE_MODEL(3DFACE_UVMAP_3DOBJ) // 模型： UVmap_3DObj.bin

// Kiev3DMake
#define MTAIENGINE_MODEL_KIEV3D_IMAGE_ALPHA MTAIENGINE_MODEL(KIEV3D_IMAGE_ALPHA) // 模型：  alpha.jpg
#define MTAIENGINE_MODEL_KIEV3D_IMAGE_NOSETRIL MTAIENGINE_MODEL(KIEV3D_IMAGE_NOSETRIL) // 模型： nosTril.jpg
#define MTAIENGINE_MODEL_KIEV3D_IMAGE_LINEMASK MTAIENGINE_MODEL(KIEV3D_IMAGE_LINEMASK) // 模型： line_mask.jpg

#define MTAIENGINE_MODEL_KIEV3D_IMAGE_LINEMASK_1 MTAIENGINE_MODEL(KIEV3D_IMAGE_LINEMASK_1) // 模型： lineMask1.png
#define MTAIENGINE_MODEL_KIEV3D_IMAGE_LINEMASK_2 MTAIENGINE_MODEL(KIEV3D_IMAGE_LINEMASK_2) // 模型： lineMask2.png
#define MTAIENGINE_MODEL_KIEV3D_IMAGE_MASKALL MTAIENGINE_MODEL(KIEV3D_IMAGE_MASKALL) // 模型： MaskAll.png
#define MTAIENGINE_MODEL_KIEV3D_IMAGE_MASKALL_1 MTAIENGINE_MODEL(KIEV3D_IMAGE_MASKALL_1) // 模型： MaskAll1.png

#define MTAIENGINE_MODEL_KIEV3D_NOSE_DELTA MTAIENGINE_MODEL(KIEV3D_NOSE_DELTA) // 模型： noseDelta.bin
#define MTAIENGINE_MODEL_KIEV3D_HUMERUS_DELTA MTAIENGINE_MODEL(KIEV3D_HUMERUS_DELTA) // 模型： humerusDelta.bin
#define MTAIENGINE_MODEL_KIEV3D_NEW_FACE_INDEX MTAIENGINE_MODEL(KIEV3D_NEW_FACE_INDEX) // 模型： newFacesIndex.bin

//BodyInOne
#define MTAIENGINE_MODEL_BODYINONE_BOX_A MTAIENGINE_MODEL(BODYINONE_BOX_A) // 模型： boxA.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_B MTAIENGINE_MODEL(BODYINONE_BOX_B) // 模型： boxB.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_A_LARGE MTAIENGINE_MODEL(BODYINONE_BOX_A_LARGE) // 模型： boxA.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_B_LARGE MTAIENGINE_MODEL(BODYINONE_BOX_B_LARGE) // 模型： boxB.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_A_SMALL MTAIENGINE_MODEL(BODYINONE_BOX_A_SMALL) // 模型： boxA.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_B_SMALL MTAIENGINE_MODEL(BODYINONE_BOX_B_SMALL) // 模型： boxB.manis
#define MTAIENGINE_MODEL_BODYINONE_POSE MTAIENGINE_MODEL(BODYINONE_POSE) // 模型： pose.manis
#define MTAIENGINE_MODEL_BODYINONE_POSE_CAMERA MTAIENGINE_MODEL(BODYINONE_POSE_CAMERA) // 模型： pose.manis
#define MTAIENGINE_MODEL_BODYINONE_POSE_33 MTAIENGINE_MODEL(BODYINONE_POSE_33) // 模型： pose.manis
#define MTAIENGINE_MODEL_BODYINONE_POSE_IMAGE MTAIENGINE_MODEL(BODYINONE_POSE_IMAGE) // 模型： pose.manis
#define MTAIENGINE_MODEL_BODYINONE_POSE_VIDEO MTAIENGINE_MODEL(BODYINONE_POSE_VIDEO) // 模型： pose.manis
#define MTAIENGINE_MODEL_BODYINONE_POSE_LARGE_COREML  MTAIENGINE_MODEL(BODYINONE_POSE_LARGE_COREML)  // 模型： neck_large.manisc
#define MTAIENGINE_MODEL_BODYINONE_POSE_MIDDLE_COREML MTAIENGINE_MODEL(BODYINONE_POSE_MIDDLE_COREML) // 模型： neck_middle.manisc
#define MTAIENGINE_MODEL_BODYINONE_POSE_SMALL_COREML  MTAIENGINE_MODEL(BODYINONE_POSE_SMALL_COREML)  // 模型： neck_small.manisc
#define MTAIENGINE_MODEL_BODYINONE_CONTOUR MTAIENGINE_MODEL(BODYINONE_CONTOUR) // 模型： contour.manis
#define MTAIENGINE_MODEL_BODYINONE_SHOULDER MTAIENGINE_MODEL(BODYINONE_SHOULDER) // 模型： shoulder.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_A_MULTI MTAIENGINE_MODEL(BODYINONE_BOX_A_MULTI) // 模型： 0_128_detectionA_357c.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_B_MULTI MTAIENGINE_MODEL(BODYINONE_BOX_B_MULTI) // 模型： 0_128_detectionB_a719.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_YOLO MTAIENGINE_MODEL(BODYINONE_BOX_YOLO) // 模型： yolo.manis
#define MTAIENGINE_MODEL_BODYINONE_BOX_YOLO_COREML MTAIENGINE_MODEL(BODYINONE_BOX_YOLO_COREML) // 模型： yolo.manisc
#define MTAIENGINE_MODEL_BODYINONE_NECK        MTAIENGINE_MODEL(BODYINONE_NECK) // 模型： neck_middle.manis
#define MTAIENGINE_MODEL_BODYINONE_NECK_LARGE  MTAIENGINE_MODEL(BODYINONE_NECK_LARGE)  // 模型： neck_large.manis
#define MTAIENGINE_MODEL_BODYINONE_NECK_MIDDLE MTAIENGINE_MODEL(BODYINONE_NECK_MIDDLE) // 模型： neck_middle.manis
#define MTAIENGINE_MODEL_BODYINONE_NECK_SMALL  MTAIENGINE_MODEL(BODYINONE_NECK_SMALL)  // 模型： neck_small.manis
#define MTAIENGINE_MODEL_BODYINONE_NECK_LARGE_COREML  MTAIENGINE_MODEL(BODYINONE_NECK_LARGE_COREML)  // 模型： neck_large.manisc
#define MTAIENGINE_MODEL_BODYINONE_NECK_MIDDLE_COREML MTAIENGINE_MODEL(BODYINONE_NECK_MIDDLE_COREML) // 模型： neck_middle.manisc
#define MTAIENGINE_MODEL_BODYINONE_NECK_SMALL_COREML  MTAIENGINE_MODEL(BODYINONE_NECK_SMALL_COREML)  // 模型： neck_small.manisc
#define MTAIENGINE_MODEL_BODYINONE_NECK_IMAGE        MTAIENGINE_MODEL(BODYINONE_NECK_IMAGE) // 模型： neck_middle.manis
#define MTAIENGINE_MODEL_BODYINONE_NECK_VIDEO        MTAIENGINE_MODEL(BODYINONE_NECK_VIDEO) // 模型： neck_middle.manis
#define MTAIENGINE_MODEL_BODYINONE_BREAST  MTAIENGINE_MODEL(BODYINONE_BREAST)  // 模型： breast_middle.manis
#define MTAIENGINE_MODEL_BODYINONE_BREAST_LARGE  MTAIENGINE_MODEL(BODYINONE_BREAST_LARGE)  // 模型： breast_large.manis
#define MTAIENGINE_MODEL_BODYINONE_BREAST_MIDDLE  MTAIENGINE_MODEL(BODYINONE_BREAST_MIDDLE)  // 模型： breast_middle.manis
#define MTAIENGINE_MODEL_BODYINONE_BREAST_SMALL  MTAIENGINE_MODEL(BODYINONE_BREAST_SMALL)  // 模型： breast_small.manis
#define MTAIENGINE_MODEL_BODYINONE_BREAST_LARGE_COREML  MTAIENGINE_MODEL(BODYINONE_BREAST_LARGE_COREML)  // 模型： breast_large.manisc
#define MTAIENGINE_MODEL_BODYINONE_BREAST_MIDDLE_COREML MTAIENGINE_MODEL(BODYINONE_BREAST_MIDDLE_COREML) // 模型： breast_middle.manisc
#define MTAIENGINE_MODEL_BODYINONE_BREAST_SMALL_COREML  MTAIENGINE_MODEL(BODYINONE_BREAST_SMALL_COREML)  // 模型： breast_small.manisc
#define MTAIENGINE_MODEL_BODYINONE_BREAST_IMAGE  MTAIENGINE_MODEL(BODYINONE_BREAST_IMAGE)  // 模型： breast_middle.manis
#define MTAIENGINE_MODEL_BODYINONE_BREAST_VIDEO  MTAIENGINE_MODEL(BODYINONE_BREAST_VIDEO)  // 模型： breast_middle.manis
#define MTAIENGINE_MODEL_BODYINONE_MULTI MTAIENGINE_MODEL(BODYINONE_MULTI)
#define MTAIENGINE_MODEL_BODYINONE_MULTI_LARGE MTAIENGINE_MODEL(BODYINONE_MULTI_LARGE)
#define MTAIENGINE_MODEL_BODYINONE_MULTI_MIDDLE MTAIENGINE_MODEL(BODYINONE_MULTI_MIDDLE)
#define MTAIENGINE_MODEL_BODYINONE_MULTI_SMALL MTAIENGINE_MODEL(BODYINONE_MULTI_SMALL)
#define MTAIENGINE_MODEL_BODYINONE_MULTI_LARGE_COREML MTAIENGINE_MODEL(BODYINONE_MULTI_LARGE_COREML)
#define MTAIENGINE_MODEL_BODYINONE_MULTI_MIDDLE_COREML MTAIENGINE_MODEL(BODYINONE_MULTI_MIDDLE_COREML)
#define MTAIENGINE_MODEL_BODYINONE_MULTI_SMALL_COREML MTAIENGINE_MODEL(BODYINONE_MULTI_SMALL_COREML)
#define MTAIENGINE_MODEL_BODYINONE_REID MTAIENGINE_MODEL(BODYINONE_REID)
#define MTAIENGINE_MODEL_BODYINONE_REID_LARGE MTAIENGINE_MODEL(BODYINONE_REID_LARGE)
#define MTAIENGINE_MODEL_BODYINONE_REID_MIDDLE MTAIENGINE_MODEL(BODYINONE_REID_MIDDLE)
#define MTAIENGINE_MODEL_BODYINONE_REID_SMALL MTAIENGINE_MODEL(BODYINONE_REID_SMALL)
#define MTAIENGINE_MODEL_BODYINONE_REID_LARGE_COREML MTAIENGINE_MODEL(BODYINONE_REID_LARGE_COREML)
#define MTAIENGINE_MODEL_BODYINONE_REID_MIDDLE_COREML MTAIENGINE_MODEL(BODYINONE_REID_MIDDLE_COREML)
#define MTAIENGINE_MODEL_BODYINONE_REID_SMALL_COREML MTAIENGINE_MODEL(BODYINONE_REID_SMALL_COREML)


//WrinkleDetection
#define MTAIENGINE_MODEL_WRINKLEDETECTION_FOREHEAD MTAIENGINE_MODEL(WRINKLEDETECTION_FOREHEAD) // 模型： MTWrinkleDetection_forehead.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_FOREHEAD_MEDIUM MTAIENGINE_MODEL(WRINKLEDETECTION_FOREHEAD_MEDIUM) // 模型： MTWrinkleDetection_forehead_medium.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_FOREHEAD_LARGE MTAIENGINE_MODEL(WRINKLEDETECTION_FOREHEAD_LARGE) // 模型： MTWrinkleDetection_forehead_large.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_EYE MTAIENGINE_MODEL(WRINKLEDETECTION_EYE) // 模型： MTWrinkleDetection_eye.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_EYE_MEDIUM MTAIENGINE_MODEL(WRINKLEDETECTION_EYE_MEDIUM) // 模型： MTWrinkleDetection_eye_medium.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_EYE_LARGE MTAIENGINE_MODEL(WRINKLEDETECTION_EYE_LARGE) // 模型： MTWrinkleDetection_eye_large.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_NASO MTAIENGINE_MODEL(WRINKLEDETECTION_NASO) // 模型： MTWrinkleDetection_naso.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_NASO_MEDIUM MTAIENGINE_MODEL(WRINKLEDETECTION_NASO_MEDIUM) // 模型： MTWrinkleDetection_naso_medium.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_NECK MTAIENGINE_MODEL(WRINKLEDETECTION_NECK) // 模型： MTWrinkleDetection_neck.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_NECK_MEDIUM MTAIENGINE_MODEL(WRINKLEDETECTION_NECK_MEDIUM) // 模型： MTWrinkleDetection_neck_medium.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_NECK_REALTIME MTAIENGINE_MODEL(WRINKLEDETECTION_NECK_REALTIME) // 模型： MTWrinkleDetection_neck_realtime.manis
#define MTAIENGINE_MODEL_NECKWRINKLESEG_NECK MTAIENGINE_MODEL(NECKWRINKLESEG_NECK) // 模型： neck_wrinkle_net.manis

#define MTAIENGINE_MODEL_WRINKLEDETECTION_EYE_SEG MTAIENGINE_MODEL(WRINKLEDETECTION_EYE_SEG) // 模型： MTWrinkleDetection_eye_seg.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_NASO_SEG MTAIENGINE_MODEL(WRINKLEDETECTION_NASO_SEG) // 模型： MTWrinkleDetection_naso_seg.manis
#define MTAIENGINE_MODEL_WRINKLEDETECTION_SILKWORM_ESG MTAIENGINE_MODEL(WRINKLEDETECTION_SILKWORM_ESG) // 模型： MTWrinkleDetection_silkworm_seg.manis

//SkinMicro皮肤镜模型
#define MTAIENGINE_MODEL_SKINMICRO_PORE MTAIENGINE_MODEL(SKINMICRO_PORE)        //毛孔，模型：key_pore.manis
#define MTAIENGINE_MODEL_SKINMICRO_GROOVE MTAIENGINE_MODEL(SKINMICRO_GROOVE)    //皮沟皮脊，模型：key_skingroove.manis
#define MTAIENGINE_MODEL_SKINMICRO_FLECK MTAIENGINE_MODEL(SKINMICRO_FLECK)      //色沉，模型：key_fleck.manis

//DenseHair
#define MTAIENGINE_MODEL_DENSEHAIR_DISTILLATION MTAIENGINE_MODEL(DENSEHAIR_DISTILLATION)                //稀疏区域判断，模型：fastscnnv2_distillation_18epoch_nomax.manis
#define MTAIENGINE_MODEL_DENSEHAIR_FASTSCNNV2_14EPOCH MTAIENGINE_MODEL(DENSEHAIR_FASTSCNNV2_14EPOCH)    //稀疏头发区域检测模型， 模型：fastscnnv2_14epoch.manis
#define MTAIENGINE_MODEL_DENSEHAIR_DIVID MTAIENGINE_MODEL(DENSEHAIR_DIVID)                              //头发加密p2p模型，模型：divid_0.0.8_local.manis

//CgStyle
#define MTAIENGINE_MODEL_CGSTYLE_BIG_NET MTAIENGINE_MODEL(CGSTYLE_BIG_NET)  //模型： big_model.manis
#define MTAIENGINE_MODEL_CGSTYLE_SMALL_NET MTAIENGINE_MODEL(CGSTYLE_SMALL_NET)  //模型： small_model.manis

//FoodStyle
#define MTAIENGINE_MODEL_FOODSTYLE_N2N  MTAIENGINE_MODEL(FOODSTYLE_N2N)    //模型：N2N_extremely_tiny_res_768_85350.manis

//Smile
#define MTAIENGINE_MODEL_SMILE  MTAIENGINE_MODEL(SMILE)    //模型：Chuckle.manis
#define MTAIENGINE_MODEL_SMILE_REF  MTAIENGINE_MODEL(SMILE_REF)    //模型：ChuckleRef.png

//新版五官分析模型
#define MTAIENGINE_MODEL_FACE_ANAX_CHEEK_BONE_TYPE  MTAIENGINE_MODEL(FACE_ANAX_CHEEK_BONE_TYPE)    //模型：FacialAnalysis_CheekboneType.manis
#define MTAIENGINE_MODEL_FACE_ANAX_CHIN_SHAPE  MTAIENGINE_MODEL(FACE_ANAX_CHIN_SHAPE)    //模型：FacialAnalysis_ChinShape.manis
#define MTAIENGINE_MODEL_FACE_ANAX_EYEBAG  MTAIENGINE_MODEL(FACE_ANAX_EYEBAG)    //模型：FacialAnalysis_EyeBag.manis
#define MTAIENGINE_MODEL_FACE_ANAX_EYELID_TYPE  MTAIENGINE_MODEL(FACE_ANAX_EYELID_TYPE)    //模型：FacialAnalysis_EyelidType.manis
#define MTAIENGINE_MODEL_FACE_ANAX_FACE_SHAPE_DL  MTAIENGINE_MODEL(FACE_ANAX_FACE_SHAPE_DL)    //模型：FacialAnalysis_FaceShapeDL.manis
#define MTAIENGINE_MODEL_FACE_ANAX_RISORIUS  MTAIENGINE_MODEL(FACE_ANAX_RISORIUS)    //模型：FacialAnalysis_Risorius.manis
#define MTAIENGINE_MODEL_FACE_ANAX_TEMPLE_TYPE  MTAIENGINE_MODEL(FACE_ANAX_TEMPLE_TYPE)    //模型：FacialAnalysis_TempleType.manis

//眼睛分割模型
#define MTAIENGINE_MODEL_EYESEGMENT  MTAIENGINE_MODEL(EYESEGMENT)    //模型：eye_segment.manis

//视频识别
#define MTAIENGINE_MODEL_VIDEO_RECOGNITION MTAIENGINE_MODEL(VIDEO_RECOGNITION)
#define MTAIENGINE_MODEL_VIDEO_RCGNZ_SCENE_BASE MTAIENGINE_MODEL(VIDEO_RCGNZ_SCENE_BASE)
#define MTAIENGINE_MODEL_VIDEO_RCGNZ_SCENE_BASE_DTCT MTAIENGINE_MODEL(VIDEO_RCGNZ_SCENE_BASE_DTCT)
#define MTAIENGINE_MODEL_VIDEO_RCGNZ_PREGNANT MTAIENGINE_MODEL(VIDEO_RCGNZ_PREGNANT)

//单图多自由度-双眼皮实时
#define MTAIENGINE_MODEL_EYELID_REALTIME_KAI MTAIENGINE_MODEL(EYELID_REALTIME_KAI) //模型：eyelid_rt_kai.manis
#define MTAIENGINE_MODEL_EYELID_REALTIME_DAN MTAIENGINE_MODEL(EYELID_REALTIME_DAN) //eyelid_rt_dan.manis
#define MTAIENGINE_MODEL_EYELID_REALTIME_OU MTAIENGINE_MODEL(EYELID_REALTIME_OU) //eyelid_rt_ou.manis
#define MTAIENGINE_MODEL_EYELID_REALTIME_PING MTAIENGINE_MODEL(EYELID_REALTIME_PING) //eyelid_rt_ping.manis
#define MTAIENGINE_MODEL_EYELID_REALTIME_KAI_COREML MTAIENGINE_MODEL(EYELID_REALTIME_KAI_COREML) //模型：eyelid_rt_kai.manisc
#define MTAIENGINE_MODEL_EYELID_REALTIME_DAN_COREML MTAIENGINE_MODEL(EYELID_REALTIME_DAN_COREML) //eyelid_rt_dan.manisc
#define MTAIENGINE_MODEL_EYELID_REALTIME_OU_COREML MTAIENGINE_MODEL(EYELID_REALTIME_OU_COREML) //eyelid_rt_ou.manisc
#define MTAIENGINE_MODEL_EYELID_REALTIME_PING_COREML MTAIENGINE_MODEL(EYELID_REALTIME_PING_COREML) //eyelid_rt_ping.manisc
//双眼皮拍后Merak
#define MTAIENGINE_MERAK_EYELID_IMAGE_GL MTAIENGINE_MERAK(EYELID_IMAGE_GL) //eyelid_paihou_gl.dtu

 //视频画质优化
#define MTAIENGINE_MODEL_VIDEO_OPTIMIZER MTAIENGINE_MODEL(VIDEO_OPTIMIZER)   //模型：传目录，算法根据视频分辨率选择模型
 
// 鼻头融合模型
#define MTAIENGINE_MODEL_NOSEBLEND_NOSE  MTAIENGINE_MODEL(NOSEBLEND_NOSE)    //模型：blend_nose.manis

//3d人体检测
#define MTAIENGINE_MODEL_HUMAN3D_DETECTION MTAIENGINE_MODEL(HUMAN3D_DETECTION) //模型：human_detection.manis
#define MTAIENGINE_MODEL_HUMAN3D_DETECTION_COREML MTAIENGINE_MODEL(HUMAN3D_DETECTION_COREML) //模型：human_detection.manisc
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_SMALL MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_SMALL) //模型：human_params_estimation.manis
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_MIDDLE MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_MIDDLE) //模型：human_params_estimation.manis
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_BIG MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_BIG) //模型：human_params_estimation.manis
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_SUPER_BIG MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_SUPER_BIG) //模型：human_params_estimation.manis
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_SMALL_COREML MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_SMALL_COREML) //模型：human_params_estimation.manisc
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_MIDDLE_COREML MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_MIDDLE_COREML) //模型：human_params_estimation.manisc
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_BIG_COREML MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_BIG_COREML) //模型：human_params_estimation.manisc
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_SUPER_BIG_COREML MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_SUPER_BIG_COREML) //模型：human_params_estimation.manisc
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_ESTIMATION_AIDISPATH MTAIENGINE_MODEL(HUMAN3D_PARAMS_ESTIMATION_AIDISPATH) //模型：配合天枢自动
#define MTAIENGINE_MODEL_HUMAN3D_2D_POSE_ESTIMATION MTAIENGINE_MODEL(HUMAN3D_2D_POSE_ESTIMATION)
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_PARSING_SPARSE MTAIENGINE_MODEL(HUMAN3D_PARAMS_PARSING_SPARSE) //模型：smplbodyneutral_sparse.model
#define MTAIENGINE_MODEL_HUMAN3D_PARAMS_PARSING_DENSE MTAIENGINE_MODEL(HUMAN3D_PARAMS_PARSING_DENSE) //模型：smplbodyneutral.model
#define MTAIENGINE_MODEL_HUMAN3D_SEARCH MTAIENGINE_MODEL(HUMAN3D_SEARCH) //模型：human_search.manis
#define MTAIENGINE_MODEL_HUMAN3D_SEARCH_COREML MTAIENGINE_MODEL(HUMAN3D_SEARCH_COREML) //模型：human_search.manisc
#define MTAIENGINE_MODEL_HUMAN3D_HAND_POSE MTAIENGINE_MODEL(HUMAN3D_HAND_POSE)
#define MTAIENGINE_MODEL_HUMAN3D_HAND_POSE_SMALL MTAIENGINE_MODEL(HUMAN3D_HAND_POSE_SMALL)
#define MTAIENGINE_MODEL_HUMAN3D_HAND_LMK MTAIENGINE_MODEL(HUMAN3D_HAND_LMK)
#define MTAIENGINE_MODEL_HUMAN3D_HAND_RECROP MTAIENGINE_MODEL(HAND_RECROP)

// 独立新版痣检测
#define MTAIENGINE_MODEL_NEVUS_DETECTION  MTAIENGINE_MODEL(NEVUS_DETECTION)    //模型：NEY.bin
#define MTAIENGINE_MODEL_NEVUS_DETECTION_SMALL  MTAIENGINE_MODEL(NEVUS_DETECTION_SMALL)    //模型：NEYSM.bin

// 宜肤肤色识别检测
#define MTAIENGINE_MODEL_EVE_AUTO_SKIN_COLOR_DETECTION  MTAIENGINE_MODEL(EVE_AUTO_SKIN_COLOR_DETECTION)    //模型：color_white_rt.bin

// 宜肤预检测模块-eye
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_EYE  MTAIENGINE_MODEL(EVE_PRE_DETECTION_EYE)    //模型：eyeopenmodel.manis

// 宜肤预检测模块-mouth
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_MOUTH  MTAIENGINE_MODEL(EVE_PRE_DETECTION_MOUTH)    //模型：mouthopenmodel.manis

// 宜肤预检测模块-face_occlusion
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_FACE_OCCLUSION  MTAIENGINE_MODEL(EVE_PRE_DETECTION_FACE_OCCLUSION)    //模型：faceocclusionmodel.manis

// 宜肤预检测模块-EVE_PRE_DETECTION_BLURFACEOCCMAKEUP
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_BLURFACEOCCMAKEUP  MTAIENGINE_MODEL(EVE_PRE_DETECTION_BLURFACEOCCMAKEUP)    //模型：blurfaceoccmakeupmodel.manis

// 宜肤预检测模块-EVE_PRE_DETECTION_CHEEKMAKEUP
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_CHEEKMAKEUP  MTAIENGINE_MODEL(EVE_PRE_DETECTION_CHEEKMAKEUP)    //模型：cheekmakeupmodel.manis

// 宜肤预检测模块-EVE_PRE_DETECTION_EYEMAKEUP
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_EYEMAKEUP  MTAIENGINE_MODEL(EVE_PRE_DETECTION_EYEMAKEUP)    //模型：eyemakeupmodel.manis

// 宜肤预检测模块-EVE_PRE_DETECTION_SCARF
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_SCARF  MTAIENGINE_MODEL(EVE_PRE_DETECTION_SCARF)    //模型：scarfmodel.manis

// 宜肤预检测模块-EVE_PRE_DETECTION_LIGHT
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_LIGHT  MTAIENGINE_MODEL(EVE_PRE_DETECTION_LIGHT)     //模型： lightmodel.manis

// 宜肤预检测模块-EVE_PRE_DETECTION_CLOTHES
#define MTAIENGINE_MODEL_EVE_PRE_DETECTION_CLOTHES  MTAIENGINE_MODEL(EVE_PRE_DETECTION_CLOTHES)   //模型： clothesmodel.manis

// 匀肤
#define MTAIENGINE_MODEL_SNOOPY_BEST  MTAIENGINE_MODEL(SNOOPY_BEST)   //模型：snoopy_best
#define MTAIENGINE_MODEL_SNOOPY_BEST_IOS17  MTAIENGINE_MODEL(SNOOPY_BEST_IOS17)   //模型：snoopy_best
#define MTAIENGINE_MODEL_SNOOPY_BEST_CAMERA  MTAIENGINE_MODEL(SNOOPY_BEST_CAMERA)   //模型：snoopy_best
#define MTAIENGINE_MODEL_SNOOPY_BEST_CAMERA_IOS17  MTAIENGINE_MODEL(SNOOPY_BEST_CAMERA_IOS17)   //模型：snoopy_best

#define MTAIENGINE_MODEL_SNOOPY_FAST  MTAIENGINE_MODEL(SNOOPY_FAST)   //模型：snoopy_fast
#define MTAIENGINE_MODEL_SNOOPY_PH  MTAIENGINE_MODEL(SNOOPY_PH)       //模型：snoopy_ph
#define MTAIENGINE_MODEL_SNOOPY_RT  MTAIENGINE_MODEL(SNOOPY_RT)       //模型：snoopy_rts
#define MTAIENGINE_MODEL_SNOOPY_RT_WINK  MTAIENGINE_MODEL(SNOOPY_RT_WINK)       //模型：snoopy_rts
#define MTAIENGINE_MODEL_DOUBLECHINFIX_DIONYSOS  MTAIENGINE_MODEL(DOUBLECHINFIX_DIONYSOS)   //双下巴修复模型：dinoysos.bin


//头发增长Merak
#define MTAIENGINE_MERAK_HAIRGROUTH_GL MTAIENGINE_MERAK(HAIRGROUTH_GL) //hairgrouth_gl.dtu

#define MTAIENGINE_MODEL_PORTRAIT_DETECTION MTAIENGINE_MODEL(PORTRAIT_DETECTION)
#define MTAIENGINE_MODEL_PORTRAIT_POSE      MTAIENGINE_MODEL(PORTRAIT_POSE)
// 瞳孔
#define MTAIENGINE_MODEL_GAZE_MANIPULATION_EYE_SEGMENT MTAIENGINE_MODEL(GAZE_MANIPULATION_EYE_SEGMENT)  //model_eye_segment.manis
#define MTAIENGINE_MODEL_GAZE_MANIPULATION_NGF16_IMGF128_SPAECT_PAD MTAIENGINE_MODEL(GAZE_MANIPULATION_NGF16_IMGF128_SPAECT_PAD)  //DPTN_gaze_64x128_IN_ngf16_imgf128_spect_pad.manis
#define MTAIENGINE_MODEL_GAZE_MANIPULATION_NGF32_IMGF256_SPAECT_PAD MTAIENGINE_MODEL(GAZE_MANIPULATION_NGF32_IMGF256_SPAECT_PAD)  //DPTN_gaze_64x128_IN_ngf16_imgf128_spect_pad.manis

// 染发
#define MTAIENGINE_MODEL_HAIRDYE_VIDEO          MTAIENGINE_MODEL(HAIRDYE_VIDEO)
#define MTAIENGINE_MODEL_HAIRDYE_VIDEO_COREML   MTAIENGINE_MODEL(HAIRDYE_VIDEO_COREML)
#define MTAIENGINE_MODEL_HAIRDYE_IMAGE          MTAIENGINE_MODEL(HAIRDYE_IMAGE)
#define MTAIENGINE_MODEL_HAIRDYE_RT             MTAIENGINE_MODEL(HAIRDYE_RT)

// 卸妆
#define MTAIENGINE_MODEL_DL_BEAUTY_BASE       MTAIENGINE_MODEL(DL_BEAUTY_BASE)
#define MTAIENGINE_MODEL_DL_BEAUTY_FAST       MTAIENGINE_MODEL(DL_BEAUTY_FAST)
#define MTAIENGINE_MODEL_DL_BEAUTY_BASE_GLOOM       MTAIENGINE_MODEL(DL_BEAUTY_BASE_GLOOM)
#define MTAIENGINE_MODEL_DL_BEAUTY_BASE_RAICHU      MTAIENGINE_MODEL(DL_BEAUTY_BASE_RAICHU)

// AI滤镜增强
#define MTAIENGINE_MODEL_COLORTONINGEW_EVA_P_CC    MTAIENGINE_MODEL(COLORTONINGEW_EVA_P_CC)
#define MTAIENGINE_MODEL_COLORTONINGEW_TEST        MTAIENGINE_MODEL(COLORTONINGEW_TEST)
#define MTAIENGINE_MODEL_COLORTONINGEW_ULTRAMAN_N  MTAIENGINE_MODEL(COLORTONINGEW_ULTRAMAN_N)
#define MTAIENGINE_MODEL_COLORTONINGEW_ULTRAMAN_P  MTAIENGINE_MODEL(COLORTONINGEW_ULTRAMAN_P)
#define MTAIENGINE_MODEL_COLORTONINGEW_UNICORN     MTAIENGINE_MODEL(COLORTONINGEW_UNICORN)
#define MTAIENGINE_MODEL_COLORTONINGEW_PONYTA      MTAIENGINE_MODEL(COLORTONINGEW_PONYTA)

// 色调专项
#define MTAIENGINE_MODEL_COLOR_AC_GL_GOLDUCK    MTAIENGINE_MODEL(COLOR_AC_GL_GOLDUCK)
#define MTAIENGINE_MODEL_COLOR_AC_GL_NIDOKING   MTAIENGINE_MODEL(COLOR_AC_GL_NIDOKING)
#define MTAIENGINE_MODEL_COLOR_AC_GL_NIDOQUEEN  MTAIENGINE_MODEL(COLOR_AC_GL_NIDOQUEEN)
#define MTAIENGINE_MODEL_COLOR_AC_GL_PSYDUCK    MTAIENGINE_MODEL(COLOR_AC_GL_PSYDUCK)
#define MTAIENGINE_MODEL_COLOR_AC_GL_PNGN       MTAIENGINE_MODEL(COLOR_AC_GL_PNGN)
#define MTAIENGINE_MODEL_COLOR_TRANSFER_METAPOD MTAIENGINE_MODEL(COLOR_TRANSFER_METAPOD)   //metapod.bin
#define MTAIENGINE_MODEL_COLOR_TRANSFER_WEEDLE  MTAIENGINE_MODEL(COLOR_TRANSFER_WEEDLE)    //weedle.bin
#define MTAIENGINE_MODEL_COLOR_TRANSFER_HIPPOPOTAS  MTAIENGINE_MODEL(COLOR_TRANSFER_HIPPOPOTAS)    //hippopotas.bin
#define MTAIENGINE_MODEL_COLOR_TRANSFER_WEEDLEV3  MTAIENGINE_MODEL(COLOR_TRANSFER_WEEDLEV3)    //weedle.bin

// 上眼睑提拉
#define MTAIENGINE_MODEL_EYELIFTING_SEGMENT      MTAIENGINE_MODEL(EYELIFTING_SEGMENT)
#define MTAIENGINE_MODEL_EYELIFTING_INPAINTING   MTAIENGINE_MODEL(EYELIFTING_INPAINTING)
#define MTAIENGINE_MODEL_EYELIFTING_SHADERS      MTAIENGINE_MODEL(EYELIFTING_SHADERS)

//
#define MTAIENGINE_MERAK_RT_TEETHRETOUCH_DTU MTAIENGINE_MERAK(RT_TEETHRETOUCH_DTU)    //rtteethretouch_video_cpu_with_rendering.dtu
#define MTAIENGINE_MERAK_RT_TEETHRETOUCH_DTU_GPU MTAIENGINE_MERAK(RT_TEETHRETOUCH_DTU_GPU)    //rtteethretouch_video_gpu_with_rendering.dtu
#define MTAIENGINE_MODEL_RT_TEETHRETOUCH MTAIENGINE_MODEL(RT_TEETHRETOUCH)    //RTTeethRetouch.mlmodelc
#define MTAIENGINE_MODEL_RT_TEETHRETOUCH_SEG MTAIENGINE_MODEL(RT_TEETHRETOUCH_SEG)    //RTTeethRetouch_Seg.manis
#define MTAIENGINE_MODEL_RT_TEETHRETOUCH_GEN MTAIENGINE_MODEL(RT_TEETHRETOUCH_GEN)    //RTTeethRetouch_Gen.manis

#define MTAIENGINE_MERAK_RESTORETEETH_DTU MTAIENGINE_MERAK(RESTORETEETH_DTU)    //
#define MTAIENGINE_MODEL_RESTORETEETH MTAIENGINE_MODEL(RESTORETEETH)    //

#define MTAIENGINE_MERAK_RT_DENSEHAIR_DTU_COREML   MTAIENGINE_MERAK(RT_DENSEHAIR_DTU_COREML)
#define MTAIENGINE_MODEL_RT_DENSEHAIR              MTAIENGINE_MODEL(RT_DENSEHAIR)

// 图片智能融合着色器
#define MTAIENGINE_MODEL_INTELLIGENT_FUSION_SHADERS MTAIENGINE_MODEL(INTELLIGENT_FUSION_SHADERS)
#define MTAIENGINE_MODEL_EFFECT_SHADERS MTAIENGINE_MODEL(INTELLIGENT_FUSION_SHADERS)

//头发变直
#define MTAIENGINE_MERAK_HAIR_STRAIGHT MTAIENGINE_MERAK(HAIR_STRAIGHT)    //模型：/MerakInnovationHairStraightModel/hairstraight_gl.dtu
//头发变卷
#define MTAIENGINE_MERAK_HAIR_CURLY MTAIENGINE_MERAK(HAIR_CURLY)    //模型：/MerakInnovationHairCurlyModel/hairFluffy_gl.dtu

//头发增长
#define MTAIENGINE_MERAK_HAIR_FLUFFY MTAIENGINE_MERAK(HAIR_FLUFFY)    //模型：/MerakInnovationHairFluffyModel/hairFluffy_gl.dtu

//美颜组模型
#define MTAIENGINE_MODEL_MTVENUS_FELIX_FELICIS MTAIENGINE_MODEL(MTVENUS_FELIX_FELICIS) //felix_felicis.bin
#define MTAIENGINE_MODEL_MTVENUS_POLYJUICE_POTION MTAIENGINE_MODEL(MTVENUS_POLYJUICE_POTION) //polyjuice_potion.bin
#define MTAIENGINE_MODEL_MTVENUS_HERCULES MTAIENGINE_MODEL(MTVENUS_HERCULES) //hercules.bin
#define MTAIENGINE_MODEL_MTVENUS_POSEIDON MTAIENGINE_MODEL(MTVENUS_POSEIDON) //poseidon.bin
#define MTAIENGINE_MODEL_MTVENUS_SHRINKING_SOLUTION MTAIENGINE_MODEL(MTVENUS_SHRINKING_SOLUTION) //shrinking_solution.bin
#define MTAIENGINE_MODEL_MTVENUS_HICCUPING_SOLUTION MTAIENGINE_MODEL(MTVENUS_HICCUPING_SOLUTION) //hiccuping_solution.bin
#define MTAIENGINE_MODEL_MTVENUS_VERITASERUM MTAIENGINE_MODEL(MTVENUS_VERITASERUM) //veritaserum.bin
#define MTAIENGINE_MODEL_MTVENUS_DITTANY MTAIENGINE_MODEL(MTVENUS_DITTANY) //dittany.bin
#define MTAIENGINE_MODEL_MTVENUS_HORUS_EYE_EGYPT MTAIENGINE_MODEL(MTVENUS_HORUS_EYE_EGYPT) //horus_sys_egypt.bin
#define MTAIENGINE_MODEL_MTVENUS_EOS MTAIENGINE_MODEL(MTVENUS_EOS) //eos.bin
#define MTAIENGINE_MODEL_MTVENUS_HOWLER MTAIENGINE_MODEL(MTVENUS_HOWLER) //howler.bin
#define MTAIENGINE_MODEL_MTVENUS_VERITASERUM_BEST MTAIENGINE_MODEL(MTVENUS_VERITASERUM_BEST) //veritaserum_best.bin

//
#define MTAIENGINE_MODEL_AI_BEAUTY_ZOOTOPIA MTAIENGINE_MODEL(AI_BEAUTY_ZOOTOPIA) //zootopia.bin or zootopia.manisc
#define MTAIENGINE_MODEL_AI_BEAUTY_ZOOTOPIA_SKIN MTAIENGINE_MODEL(AI_BEAUTY_ZOOTOPIA_SKIN) //zootopia_skin.bin or zootopia_skin.manisc
#define MTAIENGINE_MODEL_AI_BEAUTY_ZOOTOPIA_SKIN_LOW MTAIENGINE_MODEL(AI_BEAUTY_ZOOTOPIA_SKIN_LOW) //zootopia_skin_low.bin or zootopia_skin_low.manisc
#define MTAIENGINE_MODEL_AI_BEAUTY_MAD_MAX MTAIENGINE_MODEL(AI_BEAUTY_MAD_MAX) //mad_max1.bin


#define MTAIENGINE_MODEL_OPEN_EYE MTAIENGINE_MODEL(OPEN_EYE)  // emotion_model.manis

#define MTAIENGINE_MODEL_AURORA_FELIX_FELICIS MTAIENGINE_MODEL(AURORA_FELIX_FELICIS)  // felix_felicis.bin
#define MTAIENGINE_MODEL_AURORA_POLYJUICE_POTION MTAIENGINE_MODEL(AURORA_POLYJUICE_POTION)   //polyjuice_potion.bin
#define MTAIENGINE_MODEL_AURORA_DITTANY MTAIENGINE_MODEL(AURORA_DITTANY)  // dittany.bin
#define MTAIENGINE_MODEL_AURORA_HYSSOP MTAIENGINE_MODEL(AURORA_HYSSOP)   //hyssop.bin

#define MTAIENGINE_MODEL_AURORA_FELIX_FELICIS_CAMERA MTAIENGINE_MODEL(AURORA_FELIX_FELICIS_CAMERA)  // felix_felicis.bin
#define MTAIENGINE_MODEL_AURORA_POLYJUICE_POTION_CAMERA MTAIENGINE_MODEL(AURORA_POLYJUICE_POTION_CAMERA)   //polyjuice_potion.bin
#define MTAIENGINE_MODEL_AURORA_DITTANY_CAMERA MTAIENGINE_MODEL(AURORA_DITTANY_CAMERA)  // dittany.bin
#define MTAIENGINE_MODEL_AURORA_HYSSOP_CAMERA MTAIENGINE_MODEL(AURORA_HYSSOP_CAMERA)   //hyssop.bin

#define MTAIENGINE_MODEL_MTVENUS_ACHILLES MTAIENGINE_MODEL(VENUS_ACHILLES)   //polyjuice_potion.bin
#define MTAIENGINE_MODEL_MTVENUS_CHEYENNE MTAIENGINE_MODEL(VENUS_CHEYENNE)   //polyjuice_potion.bin

#define MTAIENGINE_MODEL_FRAME_SELECT_AUTOTRIAGE MTAIENGINE_MODEL(FRAME_SELECT_AUTOTRIAGE)
#define MTAIENGINE_MODEL_FRAME_SELECT_EMOTION MTAIENGINE_MODEL(FRAME_SELECT_EMOTION)
#define MTAIENGINE_MODEL_FRAME_SELECT_HANDDET MTAIENGINE_MODEL(FRAME_SELECT_HANDDET)
#define MTAIENGINE_MODEL_FRAME_SELECT_IMGQUALITY MTAIENGINE_MODEL(FRAME_SELECT_IMGQUALITY)
#define MTAIENGINE_MODEL_FRAME_SELECT_SIMILARITY MTAIENGINE_MODEL(FRAME_SELECT_SIMILARITY)

//__END_OF_MODEL__

namespace mtai
{

    /**
     * 检测模式
     */
    enum MTAiEngineMode {
        MTAiEngineMode_Image        = 0,    ///< 图片检测
        MTAiEngineMode_Video        = 1,    ///< 视频检测
        MTAiEngineMode_Video_Image  = 2,    ///< 视频+图片检测(跟踪状态中的某一帧做静态图检测 提高精度)
    };

    enum MTAI_LOG_LEVEL {
        MTAI_LOG_LEVEL_INFO = 0,
        MTAI_LOG_LEVEL_DEBUG = 1,
        MTAI_LOG_LEVEL_TIME,  //打印时间戳
        MTAI_LOG_LEVEL_THREAD, //打印线程id
        MTAI_LOG_LEVEL_WARN,
        MTAI_LOG_LEVEL_ERROR,
		
		MTAI_NEW_LOG_LEVEL_TRACE = 100,
        MTAI_NEW_LOG_LEVEL_DEBUG,
        MTAI_NEW_LOG_LEVEL_INFO,
        MTAI_NEW_LOG_LEVEL_WARN,
        MTAI_NEW_LOG_LEVEL_ERROR,
        MTAI_NEW_LOG_LEVEL_CRITICAL,
        MTAI_NEW_LOG_LEVEL_OFF,
    };

    enum MTAI_ERROR_TYPE {
        ModelNotFoundError  = 0, //模型找不到
        DependNotFoundError = 1, //依赖找不到
    };

    enum MTAI_INFERENCE_TYPE {
        MTAI_INFERENCE_NULL                = 0,
        MTAI_INFERENCE_CPU_NCHW_FP32       = 1,
        MTAI_INFERENCE_CPU_NCHWC4_FP32     = 2,
        MTAI_INFERENCE_CPU_NCHWC8_FP16     = 3,
        MTAI_INFERENCE_OPENGL              = 4,
        MTAI_INFERENCE_GLCS                = 5,
        MTAI_INFERENCE_OPENCL              = 6,
        MTAI_INFERENCE_METAL               = 7,
        MTAI_INFERENCE_CUDA                = 8,
        MTAI_INFERENCE_OPENVINO            = 9,
        MTAI_INFERENCE_HIAI_NPU            = 10,
        MTAI_INFERENCE_COREML              = 11,
        MTAI_INFERENCE_QNN                 = 12,
    };

    enum MTAI_LOG_TYPE {
        MTAI_LOG_TYPE_Console = 0,              // 控制台日志
        MTAI_LOG_TYPE_File = 1,                 // 文件日志
        MTAI_LOG_TYPE_Both = 2,                 // 控制台 + 文件日志
    };

    // 当前业务模式
    enum MTAI_BUSINESS_MODE {
        MTAI_BUSINESS_MODE_NONE = 0,       // 默认
        MTAI_BUSINESS_MODE_IMAGE = 1,      // 图片
        MTAI_BUSINESS_MODE_VIDEO = 2,      // 视频
        MTAI_BUSINESS_MODE_CAMERA = 3,      // 相机
    };


    /**
     * 检测件
     */
    enum MTAiEngineType {
        MTAiEngineType_FaceModule           = 0,
        MTAiEngineType_HandModule           = 1,
        MTAiEngineType_AnimalModule         = 2,
        MTAiEngineType_BodyModule           = 3,
        MTAiEngineType_SegmentModule        = 4,
        MTAiEngineType_SkinModule           = 5,
        MTAiEngineType_FoodModule           = 6,
        MTAiEngineType_BoundaryLineModule   = 7,
        MTAiEngineType_MaterialTrackingModule = 8,
        MTAiEngineType_ShoulderModule       = 9,
        MTAiEngineType_OrnamentModule       = 10,
        MTAiEngineType_CsketchModule        = 11,
        MTAiEngineType_HairModule           = 12,
        MTAiEngineType_InstanceSegmentModule = 13,
        MTAiEngineType_MakeupModule         = 14,
        MTAiEngineType_FaceHDModule         = 15,
        MTAiEngineType_PortraitInpaintingModule = 16,
        MTAiEngineType_ToKidModule          = 17,
        MTAiEngineType_ImageRecognitionModule = 18,
        MTAiEngineType_AnchorGenerationModule = 19,
        MTAiEngineType_SkinMicroModule      = 20,
        MTAiEngineType_RemoveWatermarkModule = 21,
        MTAiEngineType_ImageDetectionModule = 22,
        MTAiEngineType_PhotoBodyposeModule = 23,
        MTAiEngineType_DL3DModule           = 24,
        MTAiEngineType_LandmarkModule       = 25,
        MTAiEngineType_TeethModule       = 26,
        MTAiEngineType_SkinBCCModule        = 27,
		MTAiEngineType_3DFaceModule      = 28,
        MTAiEngineType_EveSkinModule     = 29,
        MTAiEngineType_BodyInOneModule   = 30,
        MTAiEngineType_WrinkleDetectionModule = 31,
        MTAiEngineType_DenseHairModule = 32,
        MTAiEngineType_CgStyleModule   = 33,
        MTAiEngineType_FoodStyleModule   = 34,
        MTAiEngineType_SmileModule   = 35,
        MTAiEngineType_EveQualityModule = 36,
        MTAiEngineType_FaceAnalysisXModule = 37,
        MTAiEngineType_Kiev3DMakeModule = 38,
        MTAiEngineType_SkinToneMappingModule = 39,
        MTAiEngineType_EyeSegmentModule = 40,
        MTAiEngineType_VideoStabilizationModule = 41,
        MTAiEngineType_VideoRecognitionModule = 42,
        MTAiEngineType_HighDofEyelidModule = 43,
        MTAiEngineType_EyelidRealtimeModule = 44,
        MTAiEngineType_VideoOptimizerModule = 45,
        MTAiEngineType_FaceBlitModule = 46,
        MTAiEngineType_AIKitModule = 47,
        MTAiEngineType_SkinARModule = 48,
        MTAiEngineType_NoseBlendModule = 49,
        MTAiEngineType_EyelidImageModule = 50,
        MTAiEngineType_NevusDetectionModule = 51,
        MTAiEngineType_EveAutoSkinColorModule = 52,
        MTAiEngineType_EvePreDetectModule = 53,
        MTAiEngineType_DoubleChinFixModule = 54,
		MTAiEngineType_HairGrouthModule = 55,
        MTAiEngineType_PortraitDetectionModule = 56,
		MTAiEngineType_Human3dModule = 57,
        MTAiEngineType_HairDyeModule = 58,
        MTAiEngineType_RTTeethRetouchModule = 59,
        MTAiEngineType_RestoreTeethModule = 60,
        MTAiEngineType_HairStraightModule = 61,
        MTAiEngineType_HairFluffyModule = 62,
        MTAiEngineType_HairCurlyModule = 63,
//__END_OF_MODULE_TYPE_HEADER__

        MTAiEngineType_MAX,
    };

    const char* GetModuleName(MTAiEngineType moduleType);
}

#endif //MTAIENGINE_MTMODULETYPE_H
