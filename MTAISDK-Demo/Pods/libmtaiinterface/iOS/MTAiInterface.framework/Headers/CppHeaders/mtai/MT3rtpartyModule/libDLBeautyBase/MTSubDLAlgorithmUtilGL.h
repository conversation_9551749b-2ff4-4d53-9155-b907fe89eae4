
#ifndef _MTLIBAI_SUB_DL_ALGORITHM_UTIL_GL_H_
#define _MTLIBAI_SUB_DL_ALGORITHM_UTIL_GLL_H_


#include <mtai/Common/MTAiEngineDefine.h>
#include "MTSubDLAlgorithmDefined.h"

extern "C" {

    typedef struct mtlabai_sub_DL_algorithm_GL_handle_t mtlabai_sub_DL_algorithm_GL_handle_t;
    
    MTAIENGINE_API mtlabai_sub_DL_algorithm_GL_handle_t *mtlabai_sub_DL_algorithm_GL_create_handle();
    
    MTAIENGINE_API void mtlabai_sub_DL_algorithm_GL_release(mtlabai_sub_DL_algorithm_GL_handle_t **handle);
    
    /*@brief 初始化*/
    MTAIENGINE_API void mtlabai_sub_DL_algorithm_GL_init(mtlabai_sub_DL_algorithm_GL_handle_t *handle);
    
    /*
     @brief加载模型
     @deviceType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_CPU
     @dataType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_STRING_DATA_TYPE_FLOAT
     @nModelProType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_RM_BEST
     */
    MTAIENGINE_API bool mtlabai_sub_DL_algorithm_GL_load_model(mtlabai_sub_DL_algorithm_GL_handle_t *handle, const char* pModelPath,
                           const mtlabai_sub_DL_algorithm_device_type deviceType,
                           const mtlabai_sub_DL_algorithm_data_type dataType,
                           const mtlabai_sub_DL_algorithm_proc_type nModelProType
                           );
   /*
    @brief 从内存加载模型
    @deviceType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_CPU
    @dataType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_STRING_DATA_TYPE_FLOAT
    */
    MTAIENGINE_API bool mtlabai_sub_DL_algorithm_GL_load_data_model(mtlabai_sub_DL_algorithm_GL_handle_t *handle, const char* pModelData,
                               const long nModelSize,
                               const mtlabai_sub_DL_algorithm_device_type deviceType,
                               const mtlabai_sub_DL_algorithm_data_type dataType
                      );
   
   /*
    @brief  运行算法
    @param  pImage              [in/out]        输入图像数据
    @param  nWidth              [in]            图像宽
    @param  nHeight             [in]            图像高
    @param  pfFacePoints        [in]            人脸点，130个点，非归一化, x,y,...
    @param  nNumFacedth         [in]            人脸个数
    @param  nNumFacePoints      [in]            人脸点个数
    */
    MTAIENGINE_API bool mtlabai_sub_DL_algorithm_GL_run(mtlabai_sub_DL_algorithm_GL_handle_t *handle, unsigned char* pImage,
                     const int nWidth,
                     const int nHeight,
                     const float* pfFacePoints,
                     const int nNumFace,
                     const int nNumFacePoints);

    /*@brief GL初始化
     @pShaderFile：如果使用默认，设置成 pShaderFile = 0 （没有引号的0）
     @nTextureFloatBits：如果使用默认，设置成 pShaderFile = 32
     @bEnableVertexFlag：如果使用默认，设置成 pShaderFile = false
    */
    MTAIENGINE_API void mtlabai_sub_DL_algorithm_GL_init_GL(mtlabai_sub_DL_algorithm_GL_handle_t *handle, char* pShaderFile,
                        const int nTextureFloatBits,
                        const bool bEnableVertexFlag);
    /*@brief 退出GL*/
    MTAIENGINE_API void mtlabai_sub_DL_algorithm_GL_exit_GL(mtlabai_sub_DL_algorithm_GL_handle_t *handle);
    
    /*@brief 设置上下文,coreml用*/
    MTAIENGINE_API void mtlabai_sub_DL_algorithm_GL_set_GL_context(mtlabai_sub_DL_algorithm_GL_handle_t *handle, void* pGLContext);
    
    /*@brief 获取fbo*/
    MTAIENGINE_API GLuint mtlabai_sub_DL_algorithm_GL_get_fbo(mtlabai_sub_DL_algorithm_GL_handle_t *handle);
    
    /*@brief 获取GL数据*/
    MTAIENGINE_API void mtlabai_sub_DL_algorithm_GL_read_pixels(mtlabai_sub_DL_algorithm_GL_handle_t *handle, const GLuint nTextureID,
                    unsigned char* data,
                    const int nWidth,
                    const int nHeight);
    
    /*
        @brief 跑算法
        @param nInputTextureID     [in]    输入纹理
        @param nOutputTextureID    [in]    输出纹理
        @param nWidth              [in]    纹理宽
        @param nHeight             [in]    纹理长
        @param pfFacePoints        [in]    人脸点，非归一化，x,y,...
        @param nFace               [in]    人脸个数
        @param nFacePoints         [in]    人脸点个数
        @param bBindTextureToFbo   [in]    是否将纹理绑定到Fbo，如果使用默认，设置成false
        */
    MTAIENGINE_API int mtlabai_sub_DL_algorithm_GL_run_GL(mtlabai_sub_DL_algorithm_GL_handle_t *handle, const GLuint nInputTextureID,
                        const GLuint nOutputTextureID,
                        const int nWidth,
                        const int nHeight,
                        const float* pfFacePoints,
                        const int nFace,
                        const int nFacePoints,
                        const bool bBindTextureToFbo);
}

#endif
