#ifndef _MTSUB_COLORTONING_EW_H_
#define _MTSUB_COLORTONING_EW_H_

#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/MT3rdpartyModule/MTSubColorToning/MTSubColorToningType.h>

#ifdef __cplusplus
extern "C"
#endif
{

// 一键同款
typedef struct mtlabai_sub_colortoningew_handle* mtlabai_sub_colortoningew_handle_t;

/**
 * \brief 第0步:创建底层SDK句柄
 * \return handle 算法句柄
 */
MTAIENGINE_API mtlabai_sub_colortoningew_handle_t
mtlabai_sub_colortoningew_create_handle();

/**
 * \brief 第0步:初始化底层对象
 */
MTAIENGINE_API void mtlabai_sub_colortoningew_init(
    mtlabai_sub_colortoningew_handle_t handle);

/**
 * \brief 销毁底层SDK句柄,最后调用
 * \param[in] handle 算法句柄
 */
MTAIENGINE_API void mtlabai_sub_colortoningew_release_handle(
    mtlabai_sub_colortoningew_handle_t* handle);

/**
 * \brief 第一步:从路径加载模型
 * \param[in] handle 算法句柄
 * \param[in] modelPath 模型路径
 * \param[in] deviceType
 *设备类型，当前支持CORLORTONING_DEVICE_CPU、CORLORTONING_DEVICE_CPU_C4、CORLORTONING_DEVICE_COREML
 *CORLORTONING_DEVICE_AUTO:ios能跑CoreML优先跑CoreML；其他机型设置CORLORTONING_DEVICE_CPU_C4
 *注意，去雾模型当前仅支持CORLORTONING_DEVICE_CPU_C4
 * \param[in] dataType 数据类型，当前仅支持CORLORTONING_DATA_TYPE_FLOAT
 * \param[in] optionType 模型类型 MTSubColorToningEWOptionType，一次只能加载一个模型
 * \param[in] assetManager assetManager
 */
MTAIENGINE_API MTSubColorToningEWStatus mtlabai_sub_colortoningew_load_model(
    mtlabai_sub_colortoningew_handle_t handle, const char* modelPath,
    MTSubColorToningDeviceType deviceType, MTSubColorToningDataType dataType,
    MTSubColorToningEWOptionType optionType, void* assetManager);

/**
 * \brief 第一步:从内存加载模型(注：CoreML模型无法通过内存加载)
 * \param[in] handle 算法句柄
 * \param[in] modelData 模型数据
 * \param[in] modelSize 模型大小
 * \param[in] deviceType
 *设备类型，当前支持CORLORTONING_DEVICE_CPU、CORLORTONING_DEVICE_CPU_C4、CORLORTONING_DEVICE_COREML
 *CORLORTONING_DEVICE_AUTO:ios能跑CoreML优先跑CoreML；其他机型设置CORLORTONING_DEVICE_CPU_C4
 *注意，去雾模型当前仅支持CORLORTONING_DEVICE_CPU_C4
 * \param[in] dataType 数据类型，当前仅支持CORLORTONING_DATA_TYPE_FLOAT
 * \param[in] optionType 模型类型 MTSubColorToningEWOptionType，一次只能加载一个模型
 */
MTAIENGINE_API MTSubColorToningEWStatus mtlabai_sub_colortoningew_load_data_model(
    mtlabai_sub_colortoningew_handle_t handle, const char* modelData, long modelSize,
    MTSubColorToningDeviceType deviceType, MTSubColorToningDataType dataType,
    MTSubColorToningEWOptionType optionType);

/**
 * \brief 第一步:从天枢加载模型
 * \param[in] handle 算法句柄
 * \param[in] deviceType 设备类型
 *设备类型，当前支持CORLORTONING_DEVICE_CPU、CORLORTONING_DEVICE_CPU_C4、CORLORTONING_DEVICE_COREML
 *CORLORTONING_DEVICE_AUTO:ios能跑CoreML优先跑CoreML；其他机型设置CORLORTONING_DEVICE_CPU_C4
 *注意，去雾模型当前仅支持CORLORTONING_DEVICE_CPU_C4
 * \param[in] dataType 数据类型，当前仅支持CORLORTONING_DATA_TYPE_FLOAT
 * \param[in] optionType 模型类型 MTSubColorToningEWOptionType，一次只能加载一个模型
 * \param[in] assetManager assetManager
 */
MTAIENGINE_API MTSubColorToningEWStatus mtlabai_sub_colortoningew_load_model_Aidispatch(
    mtlabai_sub_colortoningew_handle_t handle, MTSubColorToningDeviceType deviceType,
    MTSubColorToningDataType dataType, MTSubColorToningEWOptionType optionType,
    void* assetManager);

/**
 * \brief 第一步:加载模型(优先使用智枢)
 * \param[in] handle 算法句柄
 * \param[in] modelDir 模型根路径，内部自动拼接
 * \param[in] deviceType
 *设备类型，当前支持CORLORTONING_DEVICE_CPU、CORLORTONING_DEVICE_CPU_C4、CORLORTONING_DEVICE_COREML
 *CORLORTONING_DEVICE_AUTO:ios能跑CoreML优先跑CoreML；其他机型设置CORLORTONING_DEVICE_CPU_C4
 *注意，去雾模型当前仅支持CORLORTONING_DEVICE_CPU_C4
 * \param[in] dataType 数据类型，当前仅支持CORLORTONING_DATA_TYPE_FLOAT
 * \param[in] enablOption 开启的功能
 */
MTAIENGINE_API MTSubColorToningEWStatus mtlabai_sub_colortoningew_load_model_common(
    mtlabai_sub_colortoningew_handle_t handle, const char* modelDir,
    MTSubColorToningDeviceType deviceType, MTSubColorToningDataType dataType,
    int enablOption, void* assetManager);


/**
 * \brief 第二步:初始化算法(需要在GL环境下)
 * \param[in] handle 算法句柄
 */
MTAIENGINE_API void mtlabai_sub_colortoningew_initGL(
    mtlabai_sub_colortoningew_handle_t handle);

/**
 * \brief 第三步:运行(需要在GL环境下)
 * \param[in] handle 算法句柄
 * \param[in] inputTexture 输入纹理id
 * \param[in out] outputTexture 输出纹理id
 * \param[in] outputWidth 输出纹理的宽
 * \param[in] outputHeight 输出纹理的高
 * \param[in] optionType 模型类型
 * MTSubColorToningEWOptionType，可进行|操作，同时运行多个模型
 */
MTAIENGINE_API void mtlabai_sub_colortoningew_runGL(
    mtlabai_sub_colortoningew_handle_t handle, unsigned int inputTexture,
    unsigned int outputTexture, int outputWidth, int outputHeight, int optionType);

/**
 * \brief 第四步:释放内部GL环境(需要在GL环境下)
 * \param[in] handle 算法句柄
 */
MTAIENGINE_API void mtlabai_sub_colortoningew_exitGL(
    mtlabai_sub_colortoningew_handle_t handle);
}

/**
 * \brief run之前设置
 * \param[in] handle 算法句柄
 * \param[in] param 设置智能曝光、智能白平衡、智能去雾程度
 */
MTAIENGINE_API void mtlabai_sub_colortoningew_set_enhance_param(
    mtlabai_sub_colortoningew_handle_t handle, MTSubColorEWParam param);


#endif  // _MTSUB_COLORTONING_EW_H_