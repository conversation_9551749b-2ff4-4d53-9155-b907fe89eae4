/*
 * @Author: your name
 * @Date: 2021-08-10 19:28:12
 * @LastEditTime: 2021-08-12 20:16:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /mtstitch/AIEngine/mtstitch_caption.h
 */


#ifndef _MTSTITCH_CAPTION_H_
#define _MTSTITCH_CAPTION_H_

#include "mtstitch_MTAiEngineDefine.h"


extern "C" {

    typedef struct stitch_caption_handle_t stitch_caption_handle_t;
    typedef struct stitch_caption_result_t stitch_caption_result_t;
    typedef struct stitch_caption_image_t stitch_caption_image_t;

    enum stitch_caption_return {
        CAPTION_RETURN_FAILURE = -1,
        CAPTION_RETURN_SUCCESS = 0,
    };

    enum stitch_caption_format {
        stitch_caption_format_gray,
        stitch_caption_format_rgba
    };

    /**
     * @description: 创建image句柄，只支持gray、rgba两种格式
     * @param 无
     */
    MTAIENGINE_API stitch_caption_image_t *
    mtlabai_sub_stitch_caption_image_adaptor(unsigned char *image_data, int height, int width, stitch_caption_format format);

    /**
     * @description: 释放images句柄
     * @param {in} images 句柄
     */
    MTAIENGINE_API void
    mtlabai_sub_stitch_caption_image_release(stitch_caption_image_t *image);

    /**
     * @description: 创建台词合并句柄
     * @param 无
     */
    MTAIENGINE_API stitch_caption_handle_t *
    mtlabai_sub_stitch_caption_handle_create();

    /**
     * @description: 释放台词合并句柄
     * @param {in} handel 创建的句柄
     */
    MTAIENGINE_API void
    mtlabai_sub_stitch_caption_handle_release(stitch_caption_handle_t *handle);

    /**
     * @description: 创建result句柄
     * @param 无
     */
    MTAIENGINE_API stitch_caption_result_t *
    mtlabai_sub_stitch_caption_result_create();

    /**
     * @description: 释放result
     * @param {in} result 创建的句柄
     */
    MTAIENGINE_API void
    mtlabai_sub_stitch_caption_result_release(stitch_caption_result_t *result);

    /**
     * @description: 台词合并 run
     * @param {in} handle 创建的句柄
     * @param {in} result 结果句柄
     * @param {in} images 图片信息数组，image为gray格式
     * @param {in} image_num 传入的图片个数
     */
    MTAIENGINE_API int 
    mtlabai_sub_stitch_caption_run(stitch_caption_handle_t *handle,
                                    stitch_caption_result_t *result,
                                    stitch_caption_image_t **images, int image_num);

    /**
     * @description: 台词合并接获取结果
     * @param {in} handle 创建的句柄
     * @param {in} result 结果句柄
     * @param {out} upper 每个图片的上界
     * @param {out} lower 每个图片的下届
     * @param {out} num 个数
     * @param {out} nnrmalize 是否归一化
     * @return -1：失败；0:成功
     */
    MTAIENGINE_API int 
    mtlabai_sub_stitch_caption_result_get_bounds(stitch_caption_result_t *result, float **upper, float **lower, int *num, int *normalize);

}



#endif   /* _MTSTITCH_CAPTION_H_ */