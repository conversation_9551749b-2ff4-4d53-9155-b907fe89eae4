//
// Created by IF on 2019-04-15.
//

#ifndef MTAIENGINE_MTMODULESUPPORT_H
#define MTAIENGINE_MTMODULESUPPORT_H

#include <mtai/Common/MTAiEngineDefine.h>
#include <mtai/Common/MTAiEngineType.h>
#define INFO_NAME_LENGTH 48

namespace mtai {

    struct device_info {
        // 设备名称
        char device_name[INFO_NAME_LENGTH] = {};

        /**
         * cpu能力评级
         * android设备 32bit 级别1-8，64bit 级别最低为11
         * iOS设备 级别最低为17
         * 无法匹配输出 -1
         */
        int cpu_level = -1;

        // cpu品牌（比如高通、MTK、麒麟）
        char cpu_vendor[INFO_NAME_LENGTH] = {};
        // cpu型号（比如麒麟990）
        char cpu_soc[INFO_NAME_LENGTH] = {};
        // cpu架构（比如A77）
        char cpu_uarch[INFO_NAME_LENGTH] = {};

        /**
         * gpu能力评级
         * 1-6为低端其中1表示es版本1.0,2表示es版本为2.0,3表示es版本为3.0,大于3的es版本大于3.0
         * 7-9为中端
         * 10-13为高端
         * 大于13顶级
         */
        int gpu_level = -1;
        
        // gpu品牌（比如mali）
        char gpu_vendor[INFO_NAME_LENGTH] = {};
        // gpu型号（比如mali-G77）
        char gpu_renderer[INFO_NAME_LENGTH] = {};
    };

    class MTAIENGINE_API MTAiEngineSupport {

    public:

        enum SupportType {
            SupportType_None        = 0,    ///< 默认支持
            SupportType_CpuSegment  = 1,    ///< 默认支持
            SupportType_GpuSegment  = 2,    ///< 查询此项需要GPU环境
        };

    public:

        static bool IsSupport(SupportType type);

        //获取设备gpu相关信息
        //外部需要delete掉返回的内存空间
        static char* GetDeviceInfo(int& len);

        /**
         * 获取手机设备信息
         * 包括cpu品牌、型号、架构、评级
         * 以及gpu品牌、型号
         */
        static device_info GetCpuAndGpuInfo();

        // 当前设备支持的推理类型 （注：该判断只是硬件层面的判断，具体能支持的推理模式还是和模型相关）
        static bool IsSupportInference(mtai::MTAI_INFERENCE_TYPE type);

    };

}

#endif //MTAIENGINE_MTMODULESUPPORT_H
