//  Copyright © 2019 chenjinfu. All rights reserved.
//

#ifndef MTAIENGINE_TEXTUREUTILS_H
#define MTAIENGINE_TEXTUREUTILS_H

#include <mtai/Common/MTAiEnginePlatform.h>
#include <mtai/Common/MTAiEngineGLInclude.h>
#include <mtai/Common/MTAiEngineTexture.h>
#include <mtai/Common/MTAiEngineDefine.h>

#include <stdio.h>
#include <string>

namespace mtai
{
    // struct TextureInfo
    // {
    //     GLuint fbo_id = 0;
    //     GLuint texture_id = 0;
    //     int width = 0;
    //     int height = 0;
    // };
    
    struct TexPoint
    {
        int x;
        int y;
    };
    
    GLuint LoadShaderSource(GLenum shaderType, const char* pSource);
    GLuint CreateProgramSource(const char* pVertexSource, const char* pFragmentSource);
    
    std::string FragmentShader_Copy_0_3();
    std::string FragmentShader_Copy_0_2();
    std::string FragmentShader_Copy_3_3();
    std::string VertexShader();

    // TODO: 将TextureInfo改为MTAiEngineTexture
    // //纹理抠图
    // void SelectPictureFromTexture(TextureInfo input_texture, TextureInfo output_texture,
    //                               TexPoint left_up_point, TexPoint right_down_point, GLuint& shader_program);
    
    // //纹理贴图
    // void PictureMappingToTexture(TextureInfo input_texture, TextureInfo output_texture,
    //                              TexPoint left_up_point, TexPoint right_down_point, GLuint& shader_program);

    // 纹理拷贝，将小纹理拷贝到大纹理指定区域
    void CopyOpenGlSmallTexture2Texture(unsigned int input_texture_id, unsigned int output_texture_id, int outw, int outh, MTRect_<int>& mtRect, unsigned int& shader_program);

    //纹理拷贝
    void CopyOpenGlTexture(unsigned int input_texture_id, unsigned int output_texture_id, int outw, int outh, unsigned int& shader_program);

    //纹理拷贝
    //shader_program：第一次调用传入0，内部会自动生成一个脚本对象，并将脚本对象id传递出去，下次再调用时，再将这个id传递进来，则就不会重复生成脚本对象，提高效率
    //begin_ch，end_ch: 控制要拷贝的通道。目前支持【0，3】拷贝rgba4通道、【0，2】拷贝rgb3个通道、【3，3】拷贝a通道
    void CopyOpenGlTexture(MTAiEngineTexture& input, MTAiEngineTexture& output, unsigned int& shader_program, int begin_ch = 0, int end_ch = 3);

    //删除由CopyOpenGlTexture创建的着色器程序对象id，执行后shader_program会被置为0
    void DeleleShaderProgram(unsigned int& shader_program);

    void MatTransform(float* src, float* dst, float l, float r, float b, float t, float n, float f);
            
    void PrintTextureData(unsigned int texture_id, int width, int height);
    
    void PrintTextureDataPart(unsigned int texture_id, int width, int height, int start_x, int start_y);
    
    void EmptyTextureData(unsigned int texture_id, int width, int height);

    MTAiEngineImage ReadTextureToMTAiEngineImage(unsigned int texture_id, int width, int height, MTAiEngineImage::PIXEL_FORMAT format);

}


#endif //MTAIENGINE_TEXTUREUTILS_H
