//
// Created by IF on 2019/2/12.
//

#ifndef MTAIENGINE_MTDETECTRESULT_H
#define MTAIENGINE_MTDETECTRESULT_H

#include <cmath>
#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTVector.h"
#include "mtai/Common/MTAiEngineImage.h"

#include "mtai/MTFaceModule/MTFaceResult.h"
#include "mtai/MTHandModule/MTHandResult.h"
#include "mtai/MTAnimalModule/MTAnimalResult.h"
#include "mtai/MTBodyModule/MTBodyResult.h"
#include "mtai/MTSkinModule/MTSkinResult.h"
#include "mtai/MTSegmentModule/MTSegmentResult.h"
#include "mtai/MTMakeupModule/MTMakeupResult.h"
#include "mtai/MTFoodModule/MTFoodResult.h"
#include "mtai/MTSceneryBoundaryLineModule/MTSceneryBoundaryLineResult.h"
#include "mtai/MTMaterialTrackingModule/MTMaterialTrackingResult.h"
#include "mtai/MTShoulderModule/MTShoulderResult.h"
#include "mtai/MTInstanceSegmentModule/MTInstanceSegmentResult.h"
#include "mtai/MTOrnamentModule/MTOrnamentResult.h"
#include "mtai/MTCsketchModule/MTCsketchResult.h"
#include "mtai/MTHairModule/MTHairResult.h"
#include "mtai/MTPortraitInpaintingModule/MTPortraitInpaintingResult.h"
#include "mtai/MTFaceHDModule/MTFaceHDResult.h"
#include "mtai/MTToKidModule/MTToKidResult.h"
#include "mtai/MTImageRecognitionModule/MTImageRecognitionResult.h"
#include "mtai/MTAnchorGenerationModule/MTAnchorGenerationResult.h"
#include <mtai/MTSkinMicroModule/MTSkinMicroResult.h>
#include <mtai/MTLandmarkModule/MTLandmarkResult.h>
#include "mtai/MTRemoveWatermarkModule/MTRemoveWatermarkResult.h"
#include "mtai/MTImageDetectionModule/MTImageDetectionResult.h"
#include "mtai/MTDL3DModule/MTDL3DResult.h"
#include "mtai/MTTeethModule/MTTeethResult.h"
#include "mtai/MTEveSkinModule/MTEveSkinResult.h"
#include <mtai/MTSkinBCCModule/MTSkinBCCResult.h>
#include "mtai/MT3DFaceModule/MT3DFaceResult.h"
#include "mtai/MTBodyInOneModule/MTBodyInOneResult.h"
#include "mtai/MTWrinkleDetectionModule/MTWrinkleDetectionResult.h"
#include "mtai/MTDenseHairModule/MTDenseHairResult.h"
#include "mtai/MTCgStyleModule/MTCgStyleResult.h"
#include "mtai/MTFoodStyleModule/MTFoodStyleResult.h"
#include "mtai/MTSmileModule/MTSmileResult.h"
#include "mtai/MTEveQualityModule/MTEveQualityResult.h"
#include "mtai/MTFaceAnalysisXModule/MTFaceAnalysisXResult.h"
#include "mtai/MTKiev3DMakeModule/MTKiev3DMakeResult.h"
#include "mtai/MTSkinToneMappingModule/MTSkinToneMappingResult.h"
#include "mtai/MTEyeSegmentModule/MTEyeSegmentResult.h"
#include "mtai/MTVideoStabilizationModule/MTVideoStabilizationResult.h"
#include "mtai/MTVideoRecognitionModule/MTVideoRecognitionResult.h"
#include "mtai/MTHighDofEyelidModule/MTHighDofEyelidResult.h"
#include "mtai/MTEyelidRealtimeModule/MTEyelidRealtimeResult.h"
#include "mtai/MTVideoOptimizerModule/MTVideoOptimizerResult.h"
#include "mtai/MTFaceBlitModule/MTFaceBlitResult.h"
#include "mtai/MTAIKitModule/MTAIKitResult.h"
#include "mtai/MTSkinARModule/MTSkinARResult.h"
#include "mtai/MTNoseBlendModule/MTNoseBlendResult.h"
#include "mtai/MTHuman3dModule/MTHuman3dResult.h"
#include "mtai/MTEyelidImageModule/MTEyelidImageResult.h"
#include "mtai/MTNevusDetectionModule/MTNevusDetectionResult.h"
#include "mtai/MTEveAutoSkinColorModule/MTEveAutoSkinColorResult.h"
#include "mtai/MTEvePreDetectModule/MTEvePreDetectResult.h"
#include "mtai/MTDoubleChinFixModule/MTDoubleChinFixResult.h"
#include "mtai/MTHairGrouthModule/MTHairGrouthResult.h"
#include "mtai/MTPortraitDetectionModule/MTPortraitDetectionResult.h"
#include "mtai/MTHairDyeModule/MTHairDyeResult.h"
#include "mtai/MTRTTeethRetouchModule/MTRTTeethRetouchResult.h"
#include "mtai/MTRestoreTeethModule/MTRestoreTeethResult.h"
#include "mtai/MTHairStraightModule/MTHairStraightResult.h"
#include "mtai/MTHairFluffyModule/MTHairFluffyResult.h"
#include "mtai/MTHairCurlyModule/MTHairCurlyResult.h"
//__END_OF_RESULT_HEADER__

namespace mtai
{

    ////////////////////////////////////////////////////////////////////////////////////////
    //								EXIF方向示例
    //     1        2       3      4         5            6           7          8
    //
    //    888888  888888      88  88      8888888888  88                  88  8888888888
    //    88          88      88  88      88  88      88  88          88  88      88  88
    //    8888      8888    8888  8888    88          8888888888  8888888888          88
    //    88          88      88  88
    //    88          88  888888  888888
    ////////////////////////////////////////////////////////////////////////////////////////
    /**
     * 检测结果对象
     *
     * 如果需要旋转检测结果 需要自行对子结果调用相应 ConvertXXXResult 来进行转化
     *
     * 例如:
     *
     *      MTFaceResult dstFaceResult;
     *      dstFaceResult.normalize = true;             ///< 想要归一化的数据
     *      dstFaceResult.orientation = 1;              ///< 想要正方向的数据
     *      dstFaceResult.size = MTSize_<int>(1, 1);    ///< 归一化填(1,1)
     *      ConvertFaceResult(srcFaceResult, dstFaceResult);
     *
     *      MTHandResult dstHandResult;
     *      dstHandResult.normalize = false;                ///< 想要非归一化的数据
     *      dstHandResult.orientation = 5;                  ///< 想要xxx方向的数据
     *      dstHandResult.size = MTSize_<int>(1280, 720);   ///< 最终xxx方向的图像size
     *      ConvertHandResult(srcHandResult, dstHandResult);
     *
     */
	struct MTAIENGINE_API MTAiEngineResult {

    public:

        MTAiEngineResult();

        ~MTAiEngineResult();

        void Print() const ;

        MTFaceResult faceResult; ///< 人脸数据

        MTAnimalResult animalResult; ///< 动物检测数据

        MTHandResult handResult; ///< 手势数据

        MTBodyResult bodyResult; ///< 人体检测数据

        MTSegmentResult segmentResult; ///< 分割数据

        MTMakeupResult makeupResult; ///< 妆容识别数据

        MTSkinResult skinResult; ///< 皮肤分析数据

        MTFoodResult foodResult; ///< 美食检测数据

        MTSceneryBoundaryLineResult boundarylineResult; ///< 风景分界线

        MTMaterialTrackingFeatureResult materialtrackingfeatureResult; ///< 素材跟随
    
        MTShoulderResult shoulderResult; ///< 肩膀点检测数据
        
        MTInstanceSegmentResult instanceSegmentResult; ///< 实例分割数据

        MTOrnamentResult ornamentResult; ///< 配饰检测数据

        MTCsketchResult csketchResult; ///< 线稿结果

        MTHairResult hairResult; ///< 发型检测数据

        MTPortraitInpaintingResult portraitInpaintingResult; ///< 背景填充数据

        MTFaceHDResult faceHDResult; ///< 超清人像数据
        
        MTToKidFeatureResult toKidResult; ///< 变小孩数据

        MTImageRecognitionResult imageRecognitionResult; ///< 图像识别数据

        MTAnchorGenerationResult anchorGenerationResult; ///< 锚点生成数据

        MTSkinMicroResult skinMicroResult; ///<皮肤镜数据

        MTLandmarkResult landmarkResult; ///<三维物体识别数据
        
        MTRemoveWatermarkResult removeWatermarkResult; ///< 去水印数据

        MTImageDetectionResult imageDetectionResult; ///< 图像检测数据

		MTDL3DResult dL3DResult; ///< 3D人脸重建数据

        MTTeethResult teethResult; ///< 美牙数据

        MTEveSkinResult eveSkinResult; ///< 宜肤算法数据

        MTSkinBCCResult skinBCCResult; ///<皮肤癌数据
        
		MT3DFaceResult threeDFaceResult; ///< 3DFace数据

        MTBodyInOneResult bodyInOneResult; ///< 人体InOne数据

        MTWrinkleDetectionResult wrinkleDetectionResult; // 去皱检测数据 

        MTWrinkleDetectionRTResult   wrinkleDetectionRTResult;

        MTDenseHairResult denseHairResult; // 生发检测数据

        MTCgStyleResult cgStyleResult; // cg效果数据

        MTFoodStyleResult foodStyleResult; // FoodStyle效果数据

        MTSmileResult smileResult; // 微笑数据            
        
        MTEveQualityResult eveQualityResult; // eve图片质量判断
        
        MTFaceAnalysisXResult faceAnalysisXResult; // 新版五官分析结果数据

        MTKiev3DMakeResult kiev3DMakeResult; // 3D重建算法结果

        MTSkinToneMappingResult skinToneMappingResult; // 肤色校正
        
        MTEyeSegmentResult eyeSegmentResult; // 眼睛分割结果

        MTVideoStabilizationResult videoStabilizationResult; // 视频防抖

        MTVideoRecognitionResult videoRecognitionResult; // 视频识别

        MTHighDofEyelidResult highDofEyelidResult; // 单图多自由度-双眼皮

        MTEyelidRealtimeResult eyelidRealtimeResult; // 单图多自由度-双眼皮实时

        MTVideoOptimizerResult videoOptimizerResult; //

        MTFaceBlitResult faceBlitResult;
        
        MTAIKitResult aiKitResult;

        MTSkinARResult skinARResult;    // AR护肤

        MTNoseBlendResult noseBlendResult; // 鼻头融合

        MTHuman3dResult human3dResult; //

        MTEyelidImageResult eyelidImageResult; //

        MTNevusDetectionResult nevusDetectionResult;
        
        MTEveAutoSkinColorResult eveAutoSkinColorResult;

        MTEvePreDetectResult evePreDetectResult; //

        MTDoubleChinFixResult doubleChinFixResult; //

        MTHairGrouthResult hairGrouthResult; //

        MTPortraitDetectionResult portraitDetectionResult; //

        MTHairDyeResult hairDyeResult; //

        MTRTTeethRetouchResult rtTeethRetouchResult; //

        MTRestoreTeethResult restoreTeethResult; //

        MTHairStraightResult hairStraightResult; //

        MTHairFluffyResult hairFluffyResult; //

        MTHairCurlyResult hairCurlyResult; //


//__END_OF_RESULT_DECLARE__
    };


}

#endif //MTAIENGINE_MTDETECTRESULT_H
