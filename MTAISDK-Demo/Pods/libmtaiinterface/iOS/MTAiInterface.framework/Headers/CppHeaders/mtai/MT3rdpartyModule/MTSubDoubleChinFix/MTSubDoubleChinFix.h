//
// Created by 蔡文贤 on 2023/05/30.
//

/**
 * 双下巴修复
 */

#ifndef MTDETECTOR_MTSUBDOUBLECHINFIX_H
#define MTDETECTOR_MTSUBDOUBLECHINFIX_H

#include <mtai/Common/MTAiEngineMacro.h>

#ifdef __cplusplus
extern "C"
#endif
{
struct mtlabai_sub_double_chin_fix;

typedef struct mtlabai_sub_double_chin_fix_handle {
    mtlabai_sub_double_chin_fix *ptr;
} mtlabai_sub_double_chin_fix_handle;

/**
 * 第0步:创建底层SDK句柄
 * @return handle 算法句柄
 */
MTAIENGINE_API mtlabai_sub_double_chin_fix_handle mtlabai_sub_double_chin_fix_create();

/**
 * 销毁底层SDK句柄,最后调用
 * @param[in] handle 算法句柄指针
 */
MTAIENGINE_API void mtlabai_sub_double_chin_fix_release(
    mtlabai_sub_double_chin_fix_handle handle);

/**
 * 初始化
 *
 * @param handle 
 *
 * @return an integer indicating success (0) or failure (-1)
 *
 * @throws None
 */
MTAIENGINE_API int mtlabai_sub_double_chin_fix_init(mtlabai_sub_double_chin_fix_handle handle);

/**
 * 加载模型
 *
 * @param handle
 * @param model_path 模型完整路径
 * @param asset_manager
 *
 * @return an integer indicating success (0) or failure (-1)
 *
 * @throws None
 */
MTAIENGINE_API int mtlabai_sub_double_chin_fix_Load_model_path(
    mtlabai_sub_double_chin_fix_handle handle, const char *model_path, void *asset_manager);

/**
 * 运行
 *
 * @param handle
 * @param in_out_img      输入图(rgba格式）,返回结果也作用在该内存上
 * @param skin_mask       皮肤mask，单通道，分辨率与输入图相同
 * @param img_width       图像宽度
 * @param img_height      图像高度
 * @param face_points     非归一化人脸点
 * @param yaws            人脸姿态角度，个数和人脸个数相同
 * @param face_num        人脸个数
 * @param face_points_num 人脸点个数
 *
 * @return an integer indicating success (0) or failure (-1)
 *
 * @throws None
 */
MTAIENGINE_API int mtlabai_sub_double_chin_fix_run(
    mtlabai_sub_double_chin_fix_handle handle, unsigned char *in_out_img, unsigned char *skin_mask, int img_width, int img_height,
    const float *face_points, const float *yaws, int face_num, int face_points_num);
}

#endif // MTDETECTOR_MTSUBDOUBLECHINFIX_H
