//
// Created by l<PERSON><PERSON><PERSON><PERSON> on 2019/3/20.
//

#ifndef MTAIENGINE_MTDETECTIMAGE_H_
#define MTAIENGINE_MTDETECTIMAGE_H_

#include <mtai/Common/MTAiEngineDefine.h>

#include <cstddef>

namespace mtai
{
    class MTAIENGINE_API MTAiEngineImage {
        
    public:
        enum PIXEL_FORMAT
        {
            PIXEL_FORMAT_GRAY = 0,
            PIXEL_FORMAT_RGBA,
            PIXEL_FORMAT_BGRA,
            PIXEL_FORMAT_NV12,
            PIXEL_FORMAT_NV21,
            PIXEL_FORMAT_I420,
            PIXEL_FORMAT_MAX,
        };

    public:
        //创建空的iamge对象
        MTAiEngineImage();
        //根据参数创建image对象，同时在内部分配内存空间
        MTAiEngineImage(int _width, int _height, PIXEL_FORMAT _format, int _orientation = 1);
        //根据other的参数创建一个新的image对象，该image对象和other对象
        //指向同一个内存空间，如果other是内部创建的内存，则引用计数加一
        MTAiEngineImage(const MTAiEngineImage& other);
        //将other的参数赋值给image对象，该image对象和other对象指向同一
        //个内存空间，如果other是内部创建的内存，则引用计数加一
        MTAiEngineImage& operator=(const MTAiEngineImage& other);

        ~MTAiEngineImage();//析构，如果image内部分配了内存空间，且引用计数为1时，则释放掉。

        //创建各种格式的image对象，如果数据指针为空，则在image内部分配内存空间
        static MTAiEngineImage CreateGrayImage(int _width, int _height,
                                               unsigned char* _data, int _orientation = 1, int _stride = 0);
        static MTAiEngineImage CreateRgbaImage(int _width, int _height,
                                               unsigned char* _data, int _orientation = 1, int _stride = 0);
        static MTAiEngineImage CreateBgraImage(int _width, int _height,
                                               unsigned char* _data, int _orientation = 1, int _stride = 0);
        static MTAiEngineImage CreateNv12Image(int _width, int _height, unsigned char* _y, unsigned char* _uv, 
                                                    int _orientation = 1, int _ystride = 0, int _uvstride = 0);
        static MTAiEngineImage CreateNv21Image(int _width, int _height, unsigned char* _y, unsigned char* _vu, 
                                                    int _orientation = 1, int _ystride = 0, int _vustride = 0);
        static MTAiEngineImage CreateI420Image(int _width, int _height, unsigned char* _y, unsigned char* _u,
                unsigned char* _v, int _orientation = 1, int _ystride = 0, int _ustride = 0, int _vstride = 0);
   
        //如果dst已分配内存空间，则只需传入dst，会按照dst中的参数进行操作
        //如果dst未分配内存空间，则需要传入后面_dst开头的参数，且在内部会自动为dst分配内存空间
        MTAiEngineRet ResizeImageTo(MTAiEngineImage &dst, int _dst_width = 0, int _dst_height = 0) const;
        MTAiEngineRet ConvertImageTo(MTAiEngineImage &dst, PIXEL_FORMAT _ds_format = PIXEL_FORMAT_MAX) const;
        MTAiEngineRet RotateImageTo(MTAiEngineImage &dst, int _dst_orientation = 0) const;
        //CloneImage创建的image的stride是标准值
        MTAiEngineImage CloneImage() const;
        //判断当前image对象各参数是否是有效的
        bool IsValid() const;
        //将图片保存到文件中, file为文件路径：如/sdcard/mtai_test/input.png
        void SaveImageToFile(const char* file) const;
        //获取图片的大小
        size_t GetSize() const;

        void Release();
    public:
        union{
            unsigned char *y;       //y通道数据、RGB类型数据、GRAY
            unsigned char* data;
        };
        union{
            unsigned char *u;			//u通道数据
            unsigned char *uv;			//nv12中uv通道数据
            unsigned char *vu;			//nv21中vu通道数据
        };
        unsigned char *v;			//v通道数据
        union{
            int ystride;            //y通道stride
            int stride;
        };
        union{
            int ustride;                //u通道的stride
            int uvstride;
            int vustride;
        };
        int vstride;				//v通道的stride

        int width;					//图片宽度
        int height;					//图片高度
        int orientation;			//exif方向
        PIXEL_FORMAT format;

    private:
        void CreateInternalMem(int _width, int _height, PIXEL_FORMAT _format, int _orientation = 1, 
                               int _ystride = 0, int _ustride = 0, int _vstride = 0);
        void ReleaseInternalMem();
        int ConvertImageToGray(MTAiEngineImage &dst) const;
        int ConvertImageToRgba(MTAiEngineImage &dst) const;
        int ConvertImageToBgra(MTAiEngineImage &dst) const;
        MTAiEngineRet ResizeImage(MTAiEngineImage &dst) const;
    private:
        bool m_isInternalData;
        int* m_pRefcount;
    };

}


#endif //MTAIENGINE_MTDETECTIMAGE_H_
