//
// Created by xjk on 2021/12/30.
//

#ifndef MTDETECTOR_AIKITTOOL_H
#define MTDETECTOR_AIKITTOOL_H

#include <cstdint>
#include <mtai/Common/MTAiEngineDefine.h>


namespace mtai{

    class MTAIENGINE_API AiKitTool{
    public:
        static char * pbBinaryToJson(const char * pbBin,const uint64_t &binLen,uint64_t &outJsonLen);

        static char * pbBinaryToPbTxt(const char * pbBin,const uint64_t &binLen,uint64_t &outTxtLen);

        static char * pbTxtToJson(const char * pbTxt,const uint64_t &txtLen,uint64_t &outJsonLen);

        static char * jsonToPbTxt(const char * jsonStr,const uint64_t &json<PERSON>en,uint64_t &outTxtLen);

        static char * jsonToPbBin(const char * jsonStr, const uint64_t &json<PERSON><PERSON>,uint64_t &outBinLen);

        static char * pbTxtToBin(const char * pbTxt,const uint64_t &txtLen,uint64_t &outBinLen);

        static void releaseBuffer(char * buf);
    };
}

#endif //MTDETECTOR_AIKITTOOL_H
