/*
 * @Author: zlk
 * @Date: 2021-10-15 20:22:46
 * @Description: 
 */


#ifndef _MT_SUB_VIDEO_RECOGNITION_H_
#define _MT_SUB_VIDEO_RECOGNITION_H_


#include <mtai/Common/MTAiEngineDefine.h>


extern "C" {
    typedef struct mtlabai_sub_video_recognition_handle_t mtlabai_sub_video_recognition_handle_t;
    typedef struct mtlabai_sub_video_recognition_result_t mtlabai_sub_video_recognition_result_t;

    enum mtlabai_sub_recognition_type {
        MTLABAI_SUB_RECOGNITION_TYPE_VIDEO = 0,
        MTLABAI_SUB_RECOGNITION_TYPE_IMAGE = 1,
    };

    /**
     * @description: 创建 handle
     * @param {in} path：路径
     * @param {in} type：导入的类型：视频：0， 图片：1
     * @return handle
     */
    MTAIENGINE_API mtlabai_sub_video_recognition_handle_t *
    mtlabai_sub_video_recognition_handle_for_path_create(const char *path, int type, bool enable_GL);

    /**
     * @description: 销毁 handle
     * @param {in} handle
     * @return void
     */
    MTAIENGINE_API void
    mtlabai_sub_video_recognition_handle_release(mtlabai_sub_video_recognition_handle_t **handle);

    /**
     * @description: 创建ai引擎
     * @param {in} handle
     * @param {in} model_path：模型路径
     * @param {in} assetManager：安卓平台的资源管理器
     * @param {in} mode: 注册时选择加载模型模式
     * @param {in} device_type: 注册时选择设备级别类型
     * @return -1：失败；0：成功
     */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_create_AIEngine(mtlabai_sub_video_recognition_handle_t *handle, const char *model_path, void *assetManager, int mode, int device_type);

    /**
     * @description: 执行
     * @param {in} handle
     * @return -1：失败；0：成功
     */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_run(mtlabai_sub_video_recognition_handle_t *handle);

    /**
    * @description: 获取结果
    * @param {in} handle
    * @return 
    */
    MTAIENGINE_API mtlabai_sub_video_recognition_result_t *
    mtlabai_sub_video_recognition_get_result(mtlabai_sub_video_recognition_handle_t *handle);

    /**
    * @description: 设置起始位置
    * @param {in} handle
    * @param {in} time 开始时间, 毫秒:ms
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_set_start_time(mtlabai_sub_video_recognition_handle_t *handle, int64_t time);

    /**
    * @description: 设置持续时间
    * @param {in} handle
    * @param {in} time 时长, 毫秒:ms
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_set_duration_time(mtlabai_sub_video_recognition_handle_t *handle, int64_t time);

    /**
    * @description: 设置跳帧数
    * @param {in} handle
    * @param {in} skipFrame 跳帧数
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_set_skip_frame(mtlabai_sub_video_recognition_handle_t *handle, int skipFrame);

    /**
    * @description: 根据三级标签获取二级标签
    * @param {in} third_index 三级标签
    * @return 对应的二级序号，范围 0-34（不存在则返回35）
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_get_second_level(int third_index);
  
    /**
    * @description: 根据二级标签获取一级标签
    * @param {in} third_index 二级标签
    * @return 对应的一级序号，范围 0-6（不存在则返回7）
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_get_first_level(int second_index);
  
    /**
    * @description: 设置gl
    * @param {in} handle
    * @param {in} flag: 0 -> 不使用GL，1 -> 使用GL；默认0
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_set_GL(mtlabai_sub_video_recognition_handle_t *handle, int flag);

    /**
    * @description: 设置时间
    * @param {in} handle
    * @param {in} flag: 0 -> 不开启，1 -> 开启
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_open_runtime(mtlabai_sub_video_recognition_handle_t *handle, int flag);

    /**
    * @description: 输出为像素图像相对于媒体文件尺寸的缩放比例
    * @param {in} handle
    * @param scale 范围(0,1]
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_scale(mtlabai_sub_video_recognition_handle_t *handle, float scale);

    /**
    * @description: 只输出关键帧
    * @param {in} handle
    * @param {in} flag，0:关闭，1:开启 
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_set_enable_decode_key_frame_only(mtlabai_sub_video_recognition_handle_t *handle, int flag);

    /**
    * @description: 获取关键帧总数
    * @param {in} handle
    * @return 总数
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_get_video_key_frame_number(mtlabai_sub_video_recognition_handle_t *handle);

    /**
    * @description: stop
    * @param {in} handle
    * @return 
    */
    MTAIENGINE_API int
    mtlabai_sub_video_recognition_stop(mtlabai_sub_video_recognition_handle_t *handle);


}


#endif //_MT_SUB_VIDEO_RECOGNITION_H_
