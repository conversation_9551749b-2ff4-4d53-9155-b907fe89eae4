#pragma once

#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"


namespace mtai
{
    struct MTAIENGINE_API MTDenseHairResult
    {
        void Print() const ;

        bool normalize = true;                  ///< 是否归一化数据
        int orientation = 1;                    ///< 数据方向
        MTSize_<int> size = MTSize_<int>(1, 1); ///< 数据size
        float runTime = 0.0;                    ///< 运行耗时 单位ms
        
        //返回0，表示输入factorHeight为0，不用调整发际线；
        //返回-1，表示算法异常,不能调整发际线；
        //返回-2，表示用户没有露出完整额头，不能调整发际线；
        //返回-3，表示检测不到发际线、发际线太低等，如齐刘海，不能调整发际线；
        //返回-4，表示合理发际线高于用户实际发际线，不用调整。
        float factorHeight = -1.0f;             ///< 发际线高度预测
        
        MTAiEngineImage denseHairCropImage;     ///< 人脸裁图-发际线加密
        MTAiEngineImage p2pDataImage;           ///< p2p前向数据
        MTAiEngineImage p2pMaskImage;           ///< 头发稀疏区域mask
        
        unsigned int denseHairTextureId = 0;    ///< 发际线加密结果输出纹理
        int denseHairTextureWidth = 0;          ///< 发际线加密输出纹理宽
        int denseHairTextureHeight = 0;         ///< 发际线加密输出纹理高

        MTAiEngineImage addBangsCropImage;      ///< 人脸裁图-刘海生成
        unsigned int addBangsTextureId = 0;     ///< 刘海生成结果输出纹理
        int addBangsTextureWidth = 0;           ///< 刘海生成输出纹理宽
        int addBangsTextureHeight = 0;          ///< 刘海生成输出纹理高

        MTAiEngineImage sparseCropImage;        ///< 人脸裁图-稀疏补发
        MTAiEngineImage sparseFaceImage;        ///< 稀疏补发:待人脸与头发分割的人脸图
        MTAiEngineImage sparseHairMaskImage;    ///< 稀疏补发:头发稀疏区域检测mask
        bool sparseHairDetected = false;        ///< 稀疏补发:是否检测到稀疏区域，fasle为未检测到区域
        MTAiEngineImage sparseRgbaMaskImage;    ///< 稀疏补发:输出的512*512*4头发加密p2p的前向mask图
        MTAiEngineImage sparseRgbaP2pImage;     ///< 稀疏补发:输出的512*512*4头发加密p2p的前向人脸图
        MTAiEngineImage sparseRgbaMaskOutImage; ///< 稀疏补发:输出的512*512*4头发加密p2p输出mask图
        MTAiEngineImage sparseRgbaP2pOutImage;  ///< 稀疏补发:输出的512*512*4头发加密p2p输出人脸图
        int sparseTextureId = 0;                ///< 稀疏补发:结果输出纹理
        int sparseTextureWidth = 0;             ///< 稀疏补发:结果输出纹理宽
        int sparseTextureHeight = 0;            ///< 稀疏补发:结果输出纹理高

        ///// cpu接口（用于调试）//////
        MTAiEngineImage denseHairOutImage;      ///< 发际线加密结果
        MTAiEngineImage addBangsOutImage;       ///< 刘海生成结果
        MTAiEngineImage sparseOutImage;         ///< 稀疏补发生成结果
        //TODO 稀疏补发CPU结果
        ////////////////////////////

        int addBangsRet = 0;          ///< 刘海生成返回值，0为正常图片，1为额头缺失
    };

    /**
     * 检测结果转化
     * 需要在dst中先填充参数
     *
     * @param src   输入
     * @param dst   输出
     * @return      结果
     */
    MTAIENGINE_API
    MTAiEngineRet ConvertDenseHairResult(const MTDenseHairResult &src, MTDenseHairResult &dst);

}
