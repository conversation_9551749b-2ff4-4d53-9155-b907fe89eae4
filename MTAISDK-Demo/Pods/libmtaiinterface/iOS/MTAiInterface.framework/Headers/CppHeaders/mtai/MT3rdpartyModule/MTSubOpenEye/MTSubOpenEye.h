//
// Created by 蔡文贤 on 2022/12/12.
//

#ifndef MTDETECTOR_MTSUBOPENEYE_H
#define MTDETECTOR_MTSUBOPENEYE_H

#include <mtai/Common/MTAiEngineMacro.h>

#ifdef __cplusplus
extern "C"
#endif
{

// 人脸信息
typedef struct MTSubOpenEyeFeature {
    struct Point {
        float x;
        float y;
    };

    Point* pKeyPoints;  // 归一化人脸点
    int nKeyPoints;

    float* pVisibility;  // 人脸点可见性
    int nVisibility;

    float faceRects[4];  // 归一化人脸框

    // 人脸姿态角（弧度角）
    float pitch;
    float roll;
    float yaw;

    int faceId;
} MTSubOpenEyeFeature;

enum MTSubOpenEyeMode {
    CPU_MODE = 0,
    ANE_MODE,
    GL_MODE,
    METAL_MODE,
    CL_MODE
};


typedef struct mtlabai_sub_open_eye_handle* mtlabai_sub_open_eye_handle_t;

/**
 * \brief 第0步:创建底层SDK句柄
 * \return handle 算法句柄
 */
MTAIENGINE_API mtlabai_sub_open_eye_handle_t mtlabai_sub_open_eye_create_handle();

/**
 * \brief 销毁底层SDK句柄,最后调用
 * \param[in] pHandle 算法句柄指针
 */
MTAIENGINE_API void mtlabai_sub_open_eye_release_handle(
    mtlabai_sub_open_eye_handle_t* pHandle);

/**
 * \brief 初始化函数，候选图宽高改变，需要重新init
 * \param[in] nWidth 宽（和候选图一致）
 * \param[in] nHeight 高（和候选图一致）
 * \param[in] modelDir
 * 模型文件夹,路径传空则优先走天枢,否则走内置路径(安卓asserts；ios资源文件路径；其他平台需要设置路径),内部拼接：modelDir+"/MTAiModel/OpenEyeModel/模型名"）
 * \param[in] mode 运行模式
 * \param[in] assetManager 安卓平台需要，其他平台传空
 * \return 非0失败
 */
MTAIENGINE_API int mtlabai_sub_open_eye_Init(mtlabai_sub_open_eye_handle_t handle,
                                             int nWidth, int nHeight,
                                             const char* modelDir, MTSubOpenEyeMode mode, void* assetManager);

/**
 * \brief 添加候选图（底层异步执行）
 * \param[in] rgba 原图
 * \param[in] width 图宽
 * \param[in] height 图高
 * \param[in] features 人脸点信息
 * \param[in] nFeatures 人脸点信息个数
 * \return 非0失败
 */
MTAIENGINE_API int mtlabai_sub_open_eye_add_capture(mtlabai_sub_open_eye_handle_t handle,
                                                    const unsigned char* rgba,
                                                    const int width, const int height,
                                                    const MTSubOpenEyeFeature* features,
                                                    const int nFeatures);

/**
 * \brief 获取最终结果（rgba uint8 格式）内存外部申请，外部管理，大小和候选图一致
 * \param[out] result 输出图
 * \param[out] baseImage 合成底图
 * \return 返回doMerge的状态，1表示执行成功，并且有替换；0表示执行成功，但是没有替换(result=base)；-1表示执行失败
 */
MTAIENGINE_API int mtlabai_sub_open_eye_do_merge(mtlabai_sub_open_eye_handle_t handle,
                                                 unsigned char* result,
                                                 unsigned char* baseImage);
}

#endif  // MTDETECTOR_MTSUBOPENEYE_H
