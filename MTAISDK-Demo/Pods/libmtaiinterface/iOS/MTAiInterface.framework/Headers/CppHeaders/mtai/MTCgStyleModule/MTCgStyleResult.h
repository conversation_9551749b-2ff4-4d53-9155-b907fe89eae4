#pragma once

#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"
#include "mtai/Common/MTAiEngineTexture.h"

namespace mtai
{
    struct MTAIENGINE_API MTCgStyleResult
    {
        void Print() const ;

        void*                gpuSync     = nullptr;              ///< gpu同步锁，外部不用管这个值
        int                  orientation = 1;                    ///< 数据方向
        float                runTime     = 0.0;                  ///< 运行耗时 单位ms

        MTAiEngineTexture    cgImage;                            ///< cg效果图
        float                cgMatrix[9];                        ///< cg映射矩阵
        int                  cgFaceID    = 0;                    ///< cg效果对应的人脸id
    };

}
