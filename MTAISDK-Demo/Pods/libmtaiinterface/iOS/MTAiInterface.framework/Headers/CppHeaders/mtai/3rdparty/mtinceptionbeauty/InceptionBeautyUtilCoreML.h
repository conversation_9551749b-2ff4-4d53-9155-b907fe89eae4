/*****************************************************************
* CoreML接口
* Copyright (c) 2021 MEITU. All rights reserved.
*
* @version: 0.0.3.0
*
* @author:  gary_zou
*
* @email:  <EMAIL>
*
* @date: 2021-08-09
*
* @note: 目前实现功能：匀肤。
*
* @usage: 对外接口。包括CoreML加载模型和调用接口。
*
* @change:
*
******************************************************************/

/*
    若要编译CoreML相关功能，需在CMakeLists加入以下宏定义:
     if(APPLE AND IOS_DEPLOYMENT_TARGET)
         add_definitions(-DMTCV_IOS_COREML)
     endif()
*/
#pragma once
#ifndef _ADVANCED_INCEPTION_BEAUTY_UTIL_COREML_H_
#define _ADVANCED_INCEPTION_BEAUTY_UTIL_COREML_H_

#include "InceptionBeautyUtilGL.h"

namespace mtcvlite {
class Mat;
}

namespace mtai {
namespace mtdlbeauty
{
    class DL_INCEPTION_BEAUTY_EXPORT InceptionBeautyUtilCoreML : public InceptionBeautyUtilGL
    {
    public:
        InceptionBeautyUtilCoreML();
        virtual ~InceptionBeautyUtilCoreML();
        
        InceptionBeautyUtilCoreML(InceptionBeautyUtilCoreML& rhs) = delete;
        InceptionBeautyUtilCoreML& operator=(InceptionBeautyUtilCoreML& rhs) = delete;
        
        virtual void Init();
        
        virtual void ExitGL();
        
        
        /*
         @brief: cml需要传入gl的上下文，例子如下:
                EAGLRenderingAPI api = kEAGLRenderingAPIOpenGLES3;
                EAGLContext* _context = [[EAGLContext alloc] initWithAPI:api];
                pGLContext = (__bridge void*)_context;
        */
        virtual void SetGLContext(void* pGLContext);
        
        
        /*
         @brief: 调用函数，模型前向跑coreML，其他流程跑gl2.0或gl3.0，只适用于ios 14.0以上系统
         @param  nInputTextureID    [in]    待处理图像纹理ID
         @param  nOutputTextureID   [out]   处理完毕图像纹理ID
         @param  nWidth             [in]    图像宽
         @param  nHeight            [in]    图像高
         @param  pfFacePoints       [in]    图像非归一化人脸点
         @param  nFace              [in]    人脸数
         @param  nFacePoints        [in]    人脸点个数
         @param  bBindTextureToFbo  [in]    是否将纹理绑定到Fbo
         @param  bFuse              [in]    是否估算人脸区域并进行alpha融合
         */
        virtual int RunGL(const GLuint nInputTextureID,
                          const GLuint nOutputTextureID,
                          const int nWidth, const int nHeight,
                          const float* pfFacePoints,
                          const int nFace, const int nFacePoints,
                          const bool bBindTextureToFbo = false,
                          const bool bFuse=false);
        
        /*
         @brief: 前处理、模型前向跑coreML，后处理跑gl2.0或gl3.0，只适用于ios 14.0以上系统
         @param  nInputTextureID      [in]    待处理图像纹理ID
         @param  nOutputTextureID     [out]   处理完毕图像纹理ID
         @param  pInputCVPixelBuffer  [in]    待处理图像的cvpixelbuffer
         @param  pOutputCVPixelBuffer [out]   处理完毕图像的cvpixelbuffer，预留接口，目前默认传0
         @param  nWidth               [in]    图像宽
         @param  nHeight              [in]    图像高
         @param  pfFacePoints         [in]    图像非归一化人脸点
         @param  nFace                [in]    人脸数
         @param  nFacePoints          [in]    人脸点个数
         @param  bBindTextureToFbo    [in]    是否将纹理绑定到Fbo
         @param  bFuse              [in]    是否估算人脸区域并进行alpha融合
         */
        virtual int RunCoreML(const GLuint nInputTextureID,
                               const GLuint nOutputTextureID,
                               void* pInputCVPixelBuffer,
                               void* pOutputCVPixelBuffer,
                               const int nWidth, const int nHeight,
                               const float* pfFacePoints,
                               const int nFace, const int nFacePoints,
                               const bool bBindTextureToFbo = false,
                               const bool bFuse = false);
        
        //mtcvlite::Mat GetCVPixelBufferBalanceResult();
        //mtcvlite::Mat GetCVPixelBufferBalanceMask();
    
    };

}// end namespace mtdlbeauty
}// end namespace mtai
#endif // _INCEPTION_BEAUTY_UTIL_COREML_H_
