#pragma once

#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"
#include "mtai/Common/MTValueMap.h"
#include "mtai/MTFaceModule/MTFaceResult.h"
#include <map>


namespace mtai
{
    struct MTAIENGINE_API MTBodyPoint {
        void Print() const ;

        MTPoint2f     point;            ///< 人体点
        float         score;            ///< 人体点置信度
        float         occlu_score;      ///< 遮挡的置信度 
    };

    struct MTAIENGINE_API MTBodyInOne {
        void Print() const ;
        
        MTRect_<float>          box;                     ///< 人体框
        float                   box_score = 0.0f;        ///< 人体框置信度
        MTVector<MTBodyPoint>   pose;                    ///< 肢体
        MTVector<MTBodyPoint>   contour;                 ///< 轮廓点
        MTVector<MTBodyPoint>   shoulder;                ///< 肩膀点
        MTRect_<float>          shoulderBox;             ///< 肩膀框
        float                   shoulderBoxScore = 0.0f;     ///< 肩膀框置信度
        MTVector<MTBodyPoint>   neck;                    /// 脖子点
        MTAiEngineImage         neckMask;                ///脖子mask
        float                   neckMaskMatrix[6];       ///脖子mask的变换矩阵
        MTVector<MTBodyPoint>   breast;                    /// 胸部点
        MTAiEngineImage         breastMask;                ///胸部mask
        float                   breastMaskMatrix[6];       ///胸部mask的变换矩阵
        int                     id;                      ///< 人体id
    };

    struct MTAIENGINE_API MTBodyInOneResult
    {
        void Print() const ;

        bool                    normalize   = true;                 ///< 是否归一化数据
        int                     orientation = 1;                    ///< 数据方向
        MTSize_<int>            size        = MTSize_<int>(1, 1);   ///< 数据size
        float                   runTime     = 0.0;                  ///< 运行耗时 单位ms

        MTVector<MTBodyInOne>   body;                               ///< 人体数据
        MTVector<MTVector<int>> mergedID;                ///< 合并的人体id

        std::map<int, MTVector<MTVector<float>>> reidEmbeddings;
//        MTVector<MTVector<int>> mergedID;
        MTVector<MTVector<int>> matchedID;
    };
    
    /**
     * 检测结果转化
     * 需要在dst中先填充参数
     *
     * @param src   输入
     * @param dst   输出
     * @return      结果
     */
    MTAIENGINE_API
    MTAiEngineRet ConvertBodyInOneResult(const MTBodyInOneResult &src, MTBodyInOneResult &dst);

    /**
    * 人体(轮廓点)匹配人脸顺序， 需要开启人脸模块(简化版本，仅c++)
    * @param faceResult 输入,人脸数据，需要人脸框
    * @param bodyInOneResult 输入,人体数据，需要轮廓点
    * @return 返回人体与人脸匹配数组(个数与人脸一致)，未匹配项为-1, 否则为匹配的人体序号（例如:reslut[0]=1, 0号人脸匹配1号人体）
    */
    MTAIENGINE_API
    MTVector<int> BodyMatchFace(const MTFaceResult &faceResult, const MTBodyInOneResult &bodyInOneResult);

    /**
    * 人体(轮廓点)匹配人脸顺序， 需要开启人脸模块(通用版本，支持oc/java调用)
    * @param faceBounds 输入,人脸框数据
    * @param contourTop2s 输入,轮廓点数据，前两个点
    * @return 返回人体匹配人脸的序号数组(长度与人脸个数一致)，未匹配项为-1, 匹配的为人体序号（例如:reslut[0]=1, 0号人脸匹配1号人体）
    */
    MTAIENGINE_API
    MTVector<int> BodyMatchFace(MTVector<MTRect_<float>> faceBounds, MTVector<MTVector<MTPoint2f>> contourTop2s);

    MTAIENGINE_API MTVector<MTVector<int>> MatchReidEmbeddings(std::map<int, MTVector<MTVector<float>>> gallary, std::map<int, MTVector<MTVector<float>>> probe);

    MTAIENGINE_API MTVector<MTVector<float>> SimplifyReidEmbedding(MTVector<MTVector<float>> embeddingF, MTVector<MTVector<float>> embeddingS);
}
