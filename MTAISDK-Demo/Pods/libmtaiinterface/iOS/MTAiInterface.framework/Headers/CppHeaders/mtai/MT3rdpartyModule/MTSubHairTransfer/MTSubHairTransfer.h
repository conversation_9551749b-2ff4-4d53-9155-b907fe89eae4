//
// Created by 蔡文贤 on 2023/05/30.
//

/**
 * 百变发型，服务端模型，引擎只做前后处理，输出纹理
 */

#ifndef MTDETECTOR_MTSUBHAIRTRANSFER_H
#define MTDETECTOR_MTSUBHAIRTRANSFER_H

#include <mtai/Common/MTAiEngineMacro.h>

#ifdef __cplusplus
extern "C"
#endif
{

struct mtlabai_sub_hair_transfer;

typedef struct mtlabai_sub_hair_transfer_handle {
    mtlabai_sub_hair_transfer* ptr;
} mtlabai_sub_hair_transfer_handle;

/**
 * 第0步:创建底层SDK句柄
 * @return handle 算法句柄
 */
MTAIENGINE_API mtlabai_sub_hair_transfer_handle mtlabai_sub_hair_transfer_create();

/**
 * 销毁底层SDK句柄,最后调用
 * @param[in] pHandle 算法句柄指针
 */
MTAIENGINE_API void mtlabai_sub_hair_transfer_release(
    mtlabai_sub_hair_transfer_handle handle);

/**
 * Initializes the hair transfer subsystem for the MTLab AI Engine.
 *
 * @param handle a handle to the hair transfer subsystem
 * @param dtuPath a path to the DTU file used for hair transfer，nullptr means use default path or aidispath
 * @param assetManager a pointer to the Android asset manager
 *
 * @return an integer indicating success (0) or failure (-1)
 *
 * @throws None
 */
MTAIENGINE_API int mtlabai_sub_hair_transfer_init(mtlabai_sub_hair_transfer_handle handle,
                                                  const char* dtuPath,
                                                  void* assetManager);

/**
 * Processes an image for hair transfer.
 *
 * @param handle handle to the hair transfer object
 * @param img pointer to the image data,rgba,exif = 1
 * @param imgW width of the image
 * @param imgH height of the image
 * @param facePoints pointer to the facial keypoints
 * @param outCropImg pointer to the output cropped image data,rgba
 * @param outCropMask pointer to the output cropped mask data,rgba
 * @param outW pointer to the output width
 * @param outH pointer to the output height
 *
 * @return an integer indicating success (0) or failure (-1)
 *
 * @throws none
 */
MTAIENGINE_API int mtlabai_sub_hair_transfer_pre_process(
    mtlabai_sub_hair_transfer_handle handle, const unsigned char* img, int imgW, int imgH, int imgStride,
    const float* facePoints, unsigned char** outCropImg, unsigned char** outCropMask,
    int* outW, int* outH);

/**
 * Performs post-processing on the transferred hair image.
 *
 * @param handle handle to the hair transfer object
 * @param serverImg pointer to the server image data,access from MTOpenAI
 * @param serverMask pointer to the server mask data,access from MTOpenAI
 * @param width width of the image
 * @param height height of the image
 * @param outTexID pointer to the output texture ID,texture life cycle requires external management
 *
 * @return an integer indicating success (0) or failure (-1)
 *
 * @throws none
 */
MTAIENGINE_API int mtlabai_sub_hair_transfer_post_process(
    mtlabai_sub_hair_transfer_handle handle, const unsigned char* serverImg, int serverImgStride, 
    const unsigned char* serverMask, int serverMaskStride, int width, int height, int* outTexID);
}

#endif  // MTDETECTOR_MTSUBHAIRTRANSFER_H
