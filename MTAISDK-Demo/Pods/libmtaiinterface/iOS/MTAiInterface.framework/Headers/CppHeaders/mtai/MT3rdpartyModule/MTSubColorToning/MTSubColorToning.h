#ifndef _MTSUB_COLORTONING_H_
#define _MTSUB_COLORTONING_H_

#include <mtai/Common/MTAiEngineMacro.h>

#ifdef __cplusplus
extern "C"
#endif
{
	/**********************************************************************************/
	/*
	 dataType
     加载模型跑的计算数据类型精度，采用字符串传输的形式，以跟manis的数据类型保持同名
     data type string:
     "DATA_TYPE_FLOAT",
     "DATA_TYPE_FLOAT16",         //arm8
     "DATA_TYPE_BFLOAT16",        //联发科天机芯片支持
     "DATA_TYPE_INT8",            //定点8位的计算
     "DATA_TYPE_UINT8",
     */

    /*
	 deviceType
     加载模型跑的计算模式，采用字符串传输的形式
     device string:
     "DEVICE_CPU",
     "DEVICE_CPU_C4",                      //移动端可用C4，需实测
     "DEVICE_OPENGL",                      //模型前向和其他流程都跑gl3.0
     "DEVICE_OPENCL",
     "DEVICE_CUDA",
     "DEVICE_HEXAGON",
     “DEVICE_METAL”,                       //仅ios可用metal，
     "DEVICE_WEBGL",
     "DEVICE_GLCS",
     "DEVICE_HIAI_NPU",
     "DEVICE_COREML",                      //仅ios14.0及以上可用coreml，
     "DEVICE_OPENVINO",                    //服务端cpu使用
    */
	/**********************************************************************************/

	enum MTSubColorToningDataType
	{
		DATA_TYPE_FLOAT = 0,
		DATA_TYPE_FLOAT16 = 1,
		DATA_TYPE_BFLOAT16 = 2,
		DATA_TYPE_INT8 = 3,
		DATA_TYPE_UINT8 = 4,
	};

	enum MTSubColorToningDeviceType
	{
		DEVICE_CPU = 0,
		DEVICE_CPU_C4 = 1,
		DEVICE_OPENGL = 2,
		DEVICE_OPENCL = 3,
		DEVICE_CUDA = 4,
		DEVICE_HEXAGON = 5,
		DEVICE_METAL = 6,
		DEVICE_WEBGL = 7,
		DEVICE_GLCS = 8,
		DEVICE_HIAI_NPU = 9,
		DEVICE_COREML = 10,
		DEVICE_OPENVINO = 11,
		DEVICE_AUTO = 12,
	};

	/**
	* \brief 第0步:创建底层SDK句柄
	* \return handle 算法句柄
	*/
	MTAIENGINE_API void* mtlabai_sub_colortoning_create_handle();

	/**
	* \brief 销毁底层SDK句柄,最后调用
	* \param[in] handle 算法句柄
	*/
	MTAIENGINE_API void mtlabai_sub_colortoning_release_handle(void* handle);

	/**
	* \brief 第一步:加载模型
	* \param[in] handle 算法句柄
	* \param[in] modelPath 模型路径
	* \param[in] deviceType 设备类型
	* \param[in] dataType 数据类型
	* \param[in] assetManager assetManager
	*/
	MTAIENGINE_API bool mtlabai_sub_colortoning_load_model(void* handle, const char* modelPath, int deviceType,
														   int dataType, void* assetManager);

    /**
	* \brief 第一步:加载模型
	* \param[in] handle 算法句柄
	* \param[in] modelData 模型数据
	* \param[in] modelSize 模型大小
	* \param[in] deviceType 设备类型
	* \param[in] dataType 数据类型
	*/
	MTAIENGINE_API bool mtlabai_sub_colortoning_load_data_model(void* handle, const char* modelData, long modelSize,
														 		int deviceType, int dataType);

    /**
	* \brief 第二步:初始化算法(需要在GL环境下)
	* \param[in] handle 算法句柄
	*/
	MTAIENGINE_API void mtlabai_sub_colortoning_initGL(void* handle);

    /**
	* \brief 第三步:运行(需要在GL环境下)
	* \param[in] handle 算法句柄
	* \param[in] inputTexture 输入纹理id
	* \param[in out] outputTexture 输出纹理id
	* \param[in] outputWidth 输出纹理的宽
	* \param[in] outputHeight 输出纹理的高
	* \param[in] temValue 色温, 值范围：-100-100
	* \param[in] hueValue 色调, 值范围：-100-100
	*/
	MTAIENGINE_API void mtlabai_sub_colortoning_runGL(void* handle, int inputTexture, int outputTexture,
                            				int outputWidth, int outputHeight, int temValue, int hueValue);

    /**
	* \brief 第四步:释放内部GL环境(需要在GL环境下)
	* \param[in] handle 算法句柄
	*/
	MTAIENGINE_API void mtlabai_sub_colortoning_exitGL(void* handle);


}

#endif // _MTSUB_COLORTONING_H_