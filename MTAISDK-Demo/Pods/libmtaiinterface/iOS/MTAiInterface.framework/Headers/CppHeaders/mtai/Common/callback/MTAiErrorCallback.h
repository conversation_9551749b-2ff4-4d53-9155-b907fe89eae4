#pragma once
#include <mtai/Common/MTAiEngineType.h>
#include <mtai/Common/MTAiEnginePlatform.h>
#include <string>
#include <functional>
#ifdef MTAIENGINE_PLATFORM_ANDROID
#include <jni.h>
#endif

namespace mtai
{

    class MTAiErrorCallback {
    public:

        MTAiErrorCallback() = default;

        ~MTAiErrorCallback();

#ifdef MTAIENGINE_PLATFORM_ANDROID
        void SetErrorCallback(jobject callbackObj);

        void ClearCallback(JNIEnv *env);
#else
        void SetErrorCallback(std::function<void(MTAI_ERROR_TYPE, const char*)> callbackFunc);

        void ClearCallback();
#endif

        void ErrorCallback(mtai::MTAI_ERROR_TYPE type, const char * what);

    protected:

#ifdef MTAIENGINE_PLATFORM_ANDROID
        jobject m_callbackObj{nullptr};
        jmethodID m_callbackMid{nullptr};
#else
        std::function<void(MTAI_ERROR_TYPE, const char*)> m_callbackFunc{nullptr};
#endif

    };

}
