//
// Created by IF on 2019/3/6.
//

#ifndef MTAIENGINE_MTVALUEMAP_H
#define MTAIENGINE_MTVALUEMAP_H

#include <mtai/Common/MTAiEngineDefine.h>

namespace mtai
{
    class MTValueMapImpl;

    class MTAIENGINE_API MTValueMap {

    public:

        MTValueMap();

        MTValueMap(const MTValueMap& x);

        ~MTValueMap();

        bool GetValue(const char* key) const ;

        void SetValue(const char* key, bool value = true);

        void Clear();

        void Print() const ;

    protected:

        MTValueMapImpl* impl;

    };

    class MTValueMapFImpl;

    class MTAIENGINE_API MTValueMapF {

    public:

        MTValueMapF();

        MTValueMapF(const MTValueMapF& x);

        ~MTValueMapF();

        const MTValueMapF &operator=(const MTValueMapF &x);

        float GetValue(const char* key) const ;

        void SetValue(const char* key, float value = 0.0f);

        void Clear();

        void Print() const ;

    protected:

        MTValueMapFImpl* impl;

    };

    template<class VAL_TYPE>
    class MTValueMapNewImpl;

    template<class VAL_TYPE>
    class MTAIENGINE_API  MTValueMapNew {

    public:
        MTValueMapNew();

        MTValueMapNew(const MTValueMapNew<VAL_TYPE>& x);

        ~MTValueMapNew();

        MTValueMapNew<VAL_TYPE>& operator=(const MTValueMapNew<VAL_TYPE>& other);

        const VAL_TYPE* GetValue(const char* key) const;

        void SetValue(const char* key, const VAL_TYPE& value);

        void Clear();

        void Print() const;

        int Size() const;

    protected:
        MTValueMapNewImpl<VAL_TYPE>* impl;
    };
}

#endif //MTAIENGINE_MTVALUEMAP_H
