#ifndef _MTLIBAI_SUB_DL_BEAUTY_BASE_H_
#define _MTLIBAI_SUB_DL_BEAUTY_BASE_H_

#include <mtai/Common/MTAiEngineDefine.h>
#include "MTSubDLAlgorithmDefined.h"


extern "C" {

    typedef struct mtlabai_sub_DL_beauty_base_handle_t mtlabai_sub_DL_beauty_base_handle_t;

    /**
     * model_proc_type     设置模型的类型
     * default_device_type 默认运行模型，使用智枢时，该参数无效
     * default_data_type   默认数据类型，预留参数，当前没有作用，算法只支持float模式
     * model_path          模型文件夹路径
     * assetManager        android资源管理器
    */
    MTAIENGINE_API  mtlabai_sub_DL_beauty_base_handle_t *mtlabai_sub_DL_beauty_base_create_handle(mtlabai_sub_DL_algorithm_proc_type model_proc_type, mtlabai_sub_DL_algorithm_device_type default_device_type, mtlabai_sub_DL_algorithm_data_type default_data_type, const char *model_path, void *assetManager);

    MTAIENGINE_API void mtlabai_sub_DL_beauty_base_release(mtlabai_sub_DL_beauty_base_handle_t **handle);
    
    /*@brief 初始化*/
    MTAIENGINE_API void mtlabai_sub_DL_beauty_base_init(mtlabai_sub_DL_beauty_base_handle_t *handle);
   
   /*
    @brief  运行算法
    @param  pImage              [in/out]        输入图像数据
    @param  nWidth              [in]            图像宽
    @param  nHeight             [in]            图像高
    @param  pfFacePoints        [in]            人脸点，130个点，非归一化, x,y,...
    @param  nNumFacedth         [in]            人脸个数
    @param  nNumFacePoints      [in]            人脸点个数
    */
    MTAIENGINE_API bool mtlabai_sub_DL_beauty_base_run(mtlabai_sub_DL_beauty_base_handle_t *handle, unsigned char* pImage,
                     const int nWidth,
                     const int nHeight,
                     const float* pfFacePoints,
                     const int nNumFace,
                     const int nNumFacePoints);

    /*@brief GL初始化
     @pShaderFile：默认 pShaderFile = 0
     @nTextureFloatBits：如果使用默认，设置成 pShaderFile = 32
     @bEnableVertexFlag：如果使用默认，设置成 pShaderFile = false
    */
    MTAIENGINE_API void mtlabai_sub_DL_beauty_base_init_GL(mtlabai_sub_DL_beauty_base_handle_t *handle, char* pShaderFile,
                        const int nTextureFloatBits,
                        const bool bEnableVertexFlag);
    /*@brief 退出GL*/
    MTAIENGINE_API void mtlabai_sub_DL_beauty_base_exit_GL(mtlabai_sub_DL_beauty_base_handle_t *handle);
    
    /*@brief 设置上下文,coreml用*/
    MTAIENGINE_API void mtlabai_sub_DL_beauty_base_set_GL_context(mtlabai_sub_DL_beauty_base_handle_t *handle, void* pGLContext);
    
    /*@brief 获取fbo*/
    MTAIENGINE_API GLuint mtlabai_sub_DL_beauty_base_get_fbo(mtlabai_sub_DL_beauty_base_handle_t *handle);
    
    /*@brief 获取GL数据*/
    MTAIENGINE_API void mtlabai_sub_DL_beauty_base_read_pixels(mtlabai_sub_DL_beauty_base_handle_t *handle, const GLuint nTextureID,
                    unsigned char* data,
                    const int nWidth,
                    const int nHeight);
    
    /*
        @brief 跑算法
        @param nInputTextureID     [in]    输入纹理
        @param nOutputTextureID    [in]    输出纹理
        @param nWidth              [in]    纹理宽
        @param nHeight             [in]    纹理长
        @param pfFacePoints        [in]    人脸点，非归一化，x,y,...
        @param nFace               [in]    人脸个数
        @param nFacePoints         [in]    人脸点个数
        @param bBindTextureToFbo   [in]    是否将纹理绑定到Fbo，如果使用默认，设置成false
        */
    MTAIENGINE_API int mtlabai_sub_DL_beauty_base_run_GL(mtlabai_sub_DL_beauty_base_handle_t *handle, const GLuint nInputTextureID,
                        const GLuint nOutputTextureID,
                        const int nWidth,
                        const int nHeight,
                        const float* pfFacePoints,
                        const int nFace,
                        const int nFacePoints,
                        const bool bBindTextureToFbo);

    MTAIENGINE_API bool mtlabai_sub_DL_beauty_base_get_mode(mtlabai_sub_DL_beauty_base_handle_t *handle, mtlabai_sub_DL_algorithm_device_type &mode);

}


#endif
