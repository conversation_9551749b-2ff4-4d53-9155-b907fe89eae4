#pragma once
#include <mtai/Common/MTAiEngineOption.h>
#include <mtai/Common/MTVector.h>

namespace mtai
{
    enum MT3DFaceEnableEnum : uint64_t{
         MT_3DFACE_ENABLE_NONE                      = 0x0,                 // 无检测
         MT_3DFACE_ENABLE_3DFACE_2D                 = MT_MASK(0),          // 2.5D接口功能
         MT_3DFACE_ENABLE_3DFACE_3D                 = MT_MASK(1),          // 3D接口功能
         MT_3DFACE_ENABLE_TIME                      = MT_MASK(2),          // 获取运行耗时
         MT_3DFACE_ENABLE_DEPEND_OUTSIDE_FACE       = MT_MASK(3),           // 依赖外部人脸，需要传入外部人脸点(face_points_list)，人脸id(face_id_list)，图像宽(nImageWidth)，图像高(nImageHeight)，图像方向(nImageOrientation)，如果使用2.5D则需要多传俯仰角(pitch_angle_list)与yaw角(yaw_angle_list)
    };

    enum eReconstruct2DMode
	{
		MT_FACE_25D_V1 = MT_MASK(0),                     //2.5D重建(V1)
        MT_FACE_25D_V2 = MT_MASK(1),                     //2.5D重建(V2)
		MT_FACE_2D_BACKGROUND = MT_MASK(2),              //2D带背景网格
		MT_FACE_2D_MUITIBACKGROUND = MT_MASK(3),         //2D多层背景网格
        MT_FACE_25D_V3 = MT_MASK(4),                     //2.5D重建(V3)
	};

    class MTAIENGINE_API MT3DFaceModuleOption : public MTAiEngineOption{

    public:

        unsigned int MaxFaceCountFor2D = 0;        //2D模块最大人脸个数，0代表无限制，运行时设置
        int Reconstruct2DMode = MT_FACE_25D_V2;       //eReconstruct2DMode，运行时设置 

        unsigned int MaxFaceCountFor3D = 0;      //3D模块最大人脸个数，0代表无限制，运行时设置
        int SmoothModel = 3;                    //The smooth model by frame.(只用于预览，实时重建)，运行时设置
        MTVector<float> vecIdentityParam;       //If non-null,传入脸型系数，只fit表情（只用于新版单图重建），运行时设置
        bool FullHead = false;                  //True to full head（输出全头的模型），运行时设置
        float fovAngle = 12.f;                  //获取透视投影矩阵需要的fov角度，运行时设置
        int SmoothFrame = 1;                    //获取透视投影矩阵需要的平滑帧数，运行时设置

        // 单独设置某个开关
        void SetSigEnaOption(MT3DFaceEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MT3DFaceEnableEnum flag);//GetSingleEnableOptionStatus
 
        // 模块类型
        MTAiEngineType MuduleType() override;
        
        std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;

        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

    public:
        // bool isMultiThread_ = true;
    };

    inline MTAiEngineType MT3DFaceModuleOption::MuduleType() {return MTAiEngineType_3DFaceModule;}
    inline void MT3DFaceModuleOption::SetSigEnaOption(MT3DFaceEnableEnum flag) { enable_option_ |= flag; } //SetSingleEnableOption
    inline bool MT3DFaceModuleOption::GetSigEnaOptionStatus(MT3DFaceEnableEnum flag){ return ((enable_option_ & flag) == flag); }
}

