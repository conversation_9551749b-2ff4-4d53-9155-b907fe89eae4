#ifndef MTAIENGINE_MTAiEngineImageUtils_H_
#define MTAIENGINE_MTAiEngineImageUtils_H_

#include <mtai/Common/MTAiEngineImage.h>
#include <mtai/Common/MTVector.h>

#include <array>

namespace mtai
{

MTAiEngineImage TestDrawing(MTAiEngineImage& image, const MTVector<MTVector<MTPoint2f>> &masks, uint32_t color, int thickness);

MTAiEngineImage TestDrawing(MTAiEngineImage& image, const MTVector<MTRect2f> &rects, uint32_t color, int thickness);

MTAiEngineImage MapColorGray2RGBA(const MTAiEngineImage& image, const std::array<uint32_t, 256>& color_table);
    

}


#endif //MTAIENGINE_MTAiEngineImageUtils_H_
