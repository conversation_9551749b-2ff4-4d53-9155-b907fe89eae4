﻿//
// Created by l<PERSON><PERSON><PERSON><PERSON> on 2019/6/26.
//

#ifndef MTAIENGINE_MTPACKAGENAME_H
#define MTAIENGINE_MTPACKAGENAME_H

#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEnginePlatform.h"
#if defined(MTAIENGINE_PLATFORM_ANDROID)
#include <jni.h>
#endif

namespace mtai
{
    class MTAIENGINE_API MTPackageName {
        
        public:            
#if defined(MTAIENGINE_PLATFORM_ANDROID)
            static bool checkPackage(JNIEnv *env, jobject obj, jobject mContext);
#else
            static bool checkPackage();
#endif

            static bool isEnableUseMtai();
    };

}

#endif /* MTAIENGINE_MTPACKAGENAME_H */
