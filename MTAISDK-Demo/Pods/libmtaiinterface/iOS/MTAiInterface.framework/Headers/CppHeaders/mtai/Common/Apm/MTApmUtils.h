//
// Created by lin<PERSON>of<PERSON> on 2021/1/27.
//

#ifndef MTAIENGINE_APMUTILS_H
#define MTAIENGINE_APMUTILS_H

#include <mtai/Common/MTVector.h>
#include <string>
#include <vector>

namespace mtai
{
    /**
     * 读取文件中存放的全部json字符串
     * 
     * @param fileName  完整的文件路径，精确到文件名
     * @param result  存放读取到的json字符串
     */
    void GetJsonFromFile(const char* fileName, MTVector<std::string>& result);

    void GetModelRecordFromFile(const char* fileName, std::vector<std::string>& result);

    double GetPerformanceRatio(const char *mode, const char *moduleName, uint64_t diffTime);
}

#endif //MTAIENGINE_APMUTILS_H