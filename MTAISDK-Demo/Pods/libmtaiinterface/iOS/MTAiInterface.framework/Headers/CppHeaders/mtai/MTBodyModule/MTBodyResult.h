#pragma once
#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"

namespace mtai
{

    ////////////////////////////////////////////////////////////////////////////////////////
    //								EXIF方向示例
    //     1        2       3      4         5            6           7          8
    //
    //    888888  888888      88  88      8888888888  88                  88  8888888888
    //    88          88      88  88      88  88      88  88          88  88      88  88
    //    8888      8888    8888  8888    88          8888888888  8888888888          88
    //    88          88      88  88
    //    88          88  888888  888888
    ////////////////////////////////////////////////////////////////////////////////////////
	struct MTAIENGINE_API MTBody {
        void Print() const ;

        bool                    hasBound = false;       ///< 是否包含人体框
        MTRect_<float>          boundRect;              ///< 人体框
        float                   boundScore = 0.0f;      ///< 人体框置信度

        MTVector<MTPoint2f>     bodyPoints;             ///< 人体点数据
        MTVector<float>         bodyScores;             ///< 人体点置信度
    };

    struct MTAIENGINE_API MTBodyResult
    {
        void Print() const ;

        bool                    normalize = true;           ///< 是否归一化数据
        int                     orientation = 1;            ///< 数据方向
        MTSize_<int>            size = MTSize_<int>(1, 1);  ///< 数据size
        bool                    is_multy = false;           ///< 是否多人场景

        MTVector<MTBody>        poseBodys;                  ///< 肢体数据
        MTVector<MTBody>        contourBodys;               ///< 外轮廓数据
        MTVector<MTBody>        humanBodys;                 ///< 人体框数据

        float                   runTime     = 0.0;                  ///< 运行耗时 单位ms
    };

    /**
     * 人体结果转化
     * 需要在dst中先填充参数
     *
     * @param src   输入
     * @param dst   输出
     * @return      结果
     */
    MTAIENGINE_API
    MTAiEngineRet ConvertBodyResult(const MTBodyResult& src, MTBodyResult& dst);
}
