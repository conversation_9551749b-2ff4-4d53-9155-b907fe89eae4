//
// Created by IF on 2019/2/12.
//

#ifndef MTAIENGINE_MTFRAME_H
#define MTAIENGINE_MTFRAME_H

#include <mtai/Common/MTAiEngineDefine.h>
#include <mtai/Common/MTAiEngineImage.h>
#include <mtai/Common/MTValueMap.h>

struct cJSON;

namespace mtai
{
    class MTAIENGINE_API MTFrame {
    public:
        static constexpr const char * const LEFT_IMAGE = "LEFT_IMAGE";
        static constexpr const char * const RIGHT_IMAGE = "RIGHT_IMAGE";
        static constexpr const char * const MIDDLE_IMAGE = "MIDDLE_IMAGE";

        static constexpr const char * const LEFT_MASK = "LEFT_MASK";
        static constexpr const char * const RIGHT_MASK = "RIGHT_MASK";
        static constexpr const char * const MIDDLE_MASK = "MIDDLE_MASK";

        static constexpr const char * const NOSEBLEND_IMAGE_0_FACE = "NOSEBLEND_IMAGE_0_FACE";
        static constexpr const char * const NOSEBLEND_IMAGE_1_NOSE = "NOSEBLEND_IMAGE_1_NOSE";

    public:

        MTFrame();

        ~MTFrame();

        cJSON* GetParamsCapture() const;
        
        MTAiEngineImage image;  ///< 彩色图

        MTAiEngineImage gray;  ///< 彩色图对应的灰度图

        MTAiEngineImage image_1;  ///< 彩色图对应的方向值为1的图

        MTAiEngineImage gray_1;  ///< 彩色图对应的方向值为1的灰度图
        
        MTAiEngineImage imagePL;  ///< 偏振光图片 meitukey使用

        MTAiEngineImage imageUV;  ///< UV光图片 meitukey使用

        MTAiEngineImage p2pAlpha;  ///< 生发模块后处理使用（注意：需要传入单通道数据）

        //MTImage* imageFirst;

        //MTImage* imageSecond;

        bool firstFrame             = false;    ///< 视频流第一帧(MTAiEngineMode_Video & MTAiEngineMode_Video_Image)

        bool captureFrame           = false;    ///< 视频流中的拍照帧(仅MTAiEngineMode_Video_Image)

        //in_texture_w和in_texture_h如果不设置，则默认纹理尺寸和输入的image尺寸一样
        unsigned int in_texture_id = 0;    ///< 输入纹理id
        unsigned int in_texture_w = 0;     ///< 输入纹理的宽
        unsigned int in_texture_h = 0;     ///< 输入纹理的高
        unsigned int in_texture_exif = 1;  ///< 输入纹理的exif方向,默认为1

        void* gpuSync = nullptr;            ///< 同步锁，外部无需设置这个参数
        
        MTValueMapNew<MTAiEngineImage> image_dict;

        int frame_id = 0;
    };

}

#endif //MTAIENGINE_MTFRAME_H
