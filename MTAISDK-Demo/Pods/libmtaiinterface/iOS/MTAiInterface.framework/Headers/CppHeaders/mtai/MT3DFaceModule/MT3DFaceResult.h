#pragma once

#include "mtai/Common/MTAiEngineDefine.h"
#include "mtai/Common/MTAiEngineImage.h"
#include "mtai/Common/MTVector.h"

namespace mtai
{
    struct MTAIENGINE_API MTFace2DMesh
    {
        /// @brief	The reconstruct vertexs
		// MTVector<float> vecfVertexs;//[nVertex*3]
		float* pfVertexs = nullptr;

		/// @brief	The texture coordinates
		// MTVector<float> vecfTextureCoordinates;//[nVertex*2]
        float* pfTextureCoordinates = nullptr;

		/// @brief	The vertex num
		int nVertex = 0;

		/// @brief	Zero-based index of the triangle
		// MTVector<unsigned short> vecusTriangleIndex;//[nTriangle*3]
        unsigned short* pusTriangleIndex = nullptr;

		/// @brief	The triangle num
		int nTriangle = 0;
    };

    struct MTAIENGINE_API MT3DFace2D
    {
        //2.5D数据
		int faceID = -1; 
        MTFace2DMesh face2DMesh;
        // MTVector<float> vecfStandVerts;     //stand vertices. 点数与MTFace2DMesh.nVertex相同,这个点主要用于一些mask图的绘制，因为mask肯定是画在标准图上的。 [nVertex*2]
        float* pfStandVerts = nullptr;
    };

    struct MTAIENGINE_API MTFace3DMesh
	{
		/// @brief	The reconstruct vertexs
		MTVector<float> vecReconstructVertexs;//[nVertex*3]

		/// @brief	The texture coordinates
		MTVector<float> vecTextureCoordinates;//[nVertex*2]

		/// @brief	The texture coordinates for v1
		MTVector<float> vecTextureCoordinatesV1;//[nVertex*2]

	    /// @brief	The vertex normals
		MTVector<float> vecVertexNormals;//[nVertex*3]

		/// @brief	The vertex num
		int nVertex = 0;

		/// @brief	Zero-based index of the triangle
		MTVector<unsigned short> vecTriangleIndex;//[nTriangle*3]

		/// @brief	带嘴巴部分的三角网格
		int nTriangle = 0;

		/// @brief  没有嘴巴部分的三角网格
        int nTriangleNoLips = 0;
	};

    struct MTAIENGINE_API MTFace3DPosture
	{
		/// @brief	The matrix to ndc coordinates ,大小为4*4，按行存储
		MTVector<float> vecMatToNDC;//[16]

		/// @brief	The matrix to image ,大小为2*4，按行存储
		MTVector<float> vecMatToImage;//[8]

		/// @brief	The camera parameter 姿态预估的6个参数[Rx,Ry,Rz,Tx,Ty,S],其中R为角度
		MTVector<float> vecCameraParam;//[6]
	};

    struct MTAIENGINE_API MTFace3DLandmarkInfo
	{

		/// @brief	Zero-based index of the 3D
		MTVector<unsigned short> vec3DIndex;//[nLandmark]

		/// @brief	Zero-based index of the 2D
		MTVector<unsigned short> vec2DIndex;//[nLandmark]

		/// @brief	The landmark num;
		int nLandmark = 0;

		/// @brief	The image point 2D
		MTVector<float> vecImagePoint2D;//[nLandmark*2]
	};

    struct MTAIENGINE_API MTFace3DFitParam
	{

		/// @brief	The express 25
		MTVector<float> vecExpress25;//[25]

		/// @brief	The identity 35
		MTVector<float> vecIdentity35;//[35]

		/// @brief	The exress matrix 25 to 47
		MTVector<float> vecExressMat25To47;//[25*47]
	};

    struct MTAIENGINE_API MTFace3DReconstructData
	{

		/// @brief	The mesh
		MTFace3DMesh Mesh3D;

		/// @brief	The posture
		MTFace3DPosture Posture;

		/// @brief	Information describing the land mark
		MTFace3DLandmarkInfo LandMarkInfo;

		MTFace3DFitParam FitParam;

	};

    struct MTAIENGINE_API MT3DFace3D
    {
        //3D数据
		int faceID = -1; 

        MTFace3DReconstructData face3DReconstructData;      //3D重建相关数据

        MTVector<float> vecMeanFace;        				//获取数据库平均脸数据, 返回nVertex * 3 的数据
    
        MTVector<float> vecNeuFace;         				//获取当前用户的平均脸, 返回nVertex * 3 的数据

        MTVector<float> vecPerspectMVP;     				//获取透视投影矩阵，返回4 * 4 的数据

		MTVector<float> vecPerspectCameraParam;				//获取姿态预估，返回6个数据
    };

    struct MTAIENGINE_API MT3DFaceResult
    {
        void Print() const ;

        bool                    normalize   = true;                 ///< 是否归一化数据
        int                     orientation = 1;                    ///< 数据方向
        MTSize_<int>            size        = MTSize_<int>(1, 1);   ///< 数据size

        MTVector<MT3DFace2D>  	threeDFace25Ds_V1;               ///< 3DFace的2D数据MT_FACE_25D的结果(V1)
		MTVector<MT3DFace2D>  	threeDFace25Ds_V2;               ///< 3DFace的2D数据MT_FACE_25D的结果(V2)
		MTVector<MT3DFace2D>  	threeDFace25Ds_V3;               ///< 3DFace的2D数据MT_FACE_25D的结果(V3)
		MTVector<MT3DFace2D>  	threeDFace2DBackGrounds;         ///< 3DFace的2D数据MT_FACE_2D_BACKGROUND的结果
		MTVector<MT3DFace2D>  	threeDFace2DMuitiBackGrounds;    ///< 3DFace的2D数据MT_FACE_2D_MUITIBACKGROUND的结果
		MTVector<MT3DFace3D>	threeDFace3Ds;					 ///< 3DFace的3D数据

		float                   runTime     = 0.0;                  ///< 运行耗时 单位ms
    };

    /**
     * 检测结果转化
     * 需要在dst中先填充参数
     *
     * @param src   输入
     * @param dst   输出
     * @return      结果
     */
    MTAIENGINE_API
    MTAiEngineRet Convert3DFaceResult(const MT3DFaceResult &src, MT3DFaceResult &dst);

}
