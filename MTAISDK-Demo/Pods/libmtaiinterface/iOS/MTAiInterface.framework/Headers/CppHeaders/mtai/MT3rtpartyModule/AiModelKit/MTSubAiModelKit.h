#ifndef _MT_SUB_AI_MODEL_KIT_
#define _MT_SUB_AI_MODEL_KIT_

#include <mtai/Common/MTAiEngineMacro.h>

extern "C" {

    enum mtlabaiSubModelDeviceType {
        MT_SUB_AI_MODEL_KIT_DEVICE_AUTO = 0,
        MT_SUB_AI_MODEL_KIT_DEVICE_CPU = 1,
        MT_SUB_AI_MODEL_KIT_DEVICE_OPENGL = 2,
        MT_SUB_AI_MODEL_KIT_DEVICE_OPENCL = 3,
        MT_SUB_AI_MODEL_KIT_DEVICE_CUDA = 4,
        MT_SUB_AI_MODEL_KIT_DEVICE_HEXAGON = 5,
        MT_SUB_AI_MODEL_KIT_DEVICE_METAL = 6,
        MT_SUB_AI_MODEL_KIT_DEVICE_WEBGL = 7,
        MT_SUB_AI_MODEL_KIT_DEVICE_GLCS = 8,
        MT_SUB_AI_MODEL_KIT_DEVICE_HIAI_NPU = 9,
        MT_SUB_AI_MODEL_KIT_DEVICE_COREML = 10,
        MT_SUB_AI_MODEL_KIT_DEVICE_OPENVINO = 11,
        MT_SUB_AI_MODEL_KIT_DEVICE_QNN = 12,
        MT_SUB_AI_MODEL_KIT_DEVICE_LAST
    };
    enum mtlabaiSubModelFloatPrecision {
        MT_SUB_AI_MODEL_KIT_PRECISION_F32,
        MT_SUB_AI_MODEL_KIT_PRECISION_FP16,
        MT_SUB_AI_MODEL_KIT_PRECISION_BF16,
        MT_SUB_AI_MODEL_KIT_PRECISION_LAST
    };
    enum mtlabaiSubModelLayoutType {
        MT_SUB_AI_MODEL_KIT_NCHW = 0,
        MT_SUB_AI_MODEL_KIT_NHWC = 1,
        MT_SUB_AI_MODEL_KIT_NCHWC4 = 2,
        MT_SUB_AI_MODEL_KIT_NCHWC8 = 3,
        MT_SUB_AI_MODEL_KIT_LAST,
    };
    enum mtlabaiSubModelDataType {
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_UNDEFINED = 0,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_FLOAT = 1,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_UINT8 = 2,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_INT32 = 3,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_STRING = 4,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_BOOL = 5,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_INT8 = 6,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_UINT16 = 7,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_INT16 = 8,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_INT64 = 9,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_FLOAT16 = 10,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_DOUBLE = 11,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_UINT32 = 12,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_UINT64 = 13,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_BFLOAT16 = 14,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_TORCH_UINT8 = 15,
        MT_SUB_AI_MODEL_KIT_DATA_TYPE_LAST
    };

    // 类型对应上面的枚举
    typedef struct _mtlabaiSubModelStrategy{
        mtlabaiSubModelFloatPrecision floatPrecision; // 浮点型精度
        mtlabaiSubModelLayoutType layoutType;     // 布局格式，主要是manis内部使用；对于外部而言，只有一个可设置跑 NCHWC4模式
        mtlabaiSubModelDeviceType deviceType;     // 跑哪种模式，如cpu、opengl、metal、coreml、npu等等
        mtlabaiSubModelDataType dataType;       // manis内部使用，对于外部而言，只有一个判断是否支持fp16精度的作用
    } mtlabaiSubModelStrategy;

    typedef struct _mtlabaiSubModelStru {
        char *path;
        char *modelData;
        int modelDataLen;
        char *infoPath;
        char *infoData;
        int  infoDataLen;
        bool isDir;
        bool hasStrategy;
        mtlabaiSubModelStrategy strategy;
    } mtlabaiSubModelStru;

    //通过key获取模型路径
    MTAIENGINE_API mtlabaiSubModelStru *mtlabai_sub_AiDispatch_get_model_path_for_key(const char *key);

    //通过key获取模型data
    MTAIENGINE_API mtlabaiSubModelStru *mtlabai_sub_AiDispatch_get_model_data_for_key(const char *key, void *asset);

    //通过路径获取模型data(路径必须是文件名)
    MTAIENGINE_API mtlabaiSubModelStru *mtlabai_sub_AiDispatch_get_model_data_for_path(const char *path, void *asset);

    //coreml和manis模型通用接口，通过isDir判断类型：true->coreml, false->manis
    MTAIENGINE_API mtlabaiSubModelStru *mtlabai_sub_AiDispatch_get_model_for_key(const char *key, void *asset);

    //通过key获取模型路径+策略文件data
    MTAIENGINE_API mtlabaiSubModelStru *mtlabai_sub_AiDispatch_get_model_path_and_strategy_data_for_key(const char *key, void *asset);

    //通过key获取模型路径+策略文件data
    MTAIENGINE_API mtlabaiSubModelStru *mtlabai_sub_AiDispatch_get_model_path_and_dtu_data_for_key(const char *key, void *asset);

    //release
    MTAIENGINE_API void mtlabai_sub_AiDispatch_release(mtlabaiSubModelStru **handle);
}

#endif  //_MT_SUB_AI_MODEL_KIT_