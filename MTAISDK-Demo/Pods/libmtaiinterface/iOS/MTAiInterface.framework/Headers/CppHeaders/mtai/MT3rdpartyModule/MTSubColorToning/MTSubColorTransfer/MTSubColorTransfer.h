#ifndef _MT_SUB_COLOR_TRANSFER_H_
#define _MT_SUB_COLOR_TRANSFER_H_


#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/MT3rdpartyModule/MTSubColorToning/MTSubColorToningType.h>

#ifdef __cplusplus
extern "C"
#endif
{

    typedef struct mtlabai_sub_color_transfer_handle* mtlabai_sub_color_transfer_handle_t;

    MTAIENGINE_API mtlabai_sub_color_transfer_handle_t mtlabai_sub_color_transfer_create_handle();

    MTAIENGINE_API void mtlabai_sub_color_transfer_release_handle(mtlabai_sub_color_transfer_handle_t* handle);

    MTAIENGINE_API void mtlabai_sub_color_transfer_init(mtlabai_sub_color_transfer_handle_t handle);

    /*
        bEnableVertex 如果使用算法默认值，则设置为true
    */
    MTAIENGINE_API void mtlabai_sub_color_transfer_init_GL(mtlabai_sub_color_transfer_handle_t handle, const bool bEnableVertex);
    
    //GL初始化的线程里退出
    MTAIENGINE_API void mtlabai_sub_color_transfer_exit_GL(mtlabai_sub_color_transfer_handle_t handle);
    
    /*
        加载模型跑的计算数据类型精度，采用字符串传输的形式，以跟manis的数据类型保持同名
        data type string:
        "DATA_TYPE_FLOAT",
        "DATA_TYPE_FLOAT16",         //arm8
        "DATA_TYPE_BFLOAT16",        //联发科天机芯片支持
        "DATA_TYPE_INT8",            //定点8位的计算
        "DATA_TYPE_UINT8",
    */
    
    /*
        加载模型跑的计算模式，采用字符串传输的形式
        device string:
        "DEVICE_CPU",
        "DEVICE_CPU_C4",                      //移动端可用C4，需实测
        "DEVICE_OPENGL",                      //模型前向和其他流程都跑gl3.0
        "DEVICE_OPENCL",
        "DEVICE_CUDA",
        "DEVICE_HEXAGON",
        “DEVICE_METAL”,                       //仅ios可用metal，
        "DEVICE_WEBGL",
        "DEVICE_GLCS",
        "DEVICE_HIAI_NPU",
        "DEVICE_COREML",                      //仅ios14.0及以上可用coreml，
        "DEVICE_OPENVINO",                    //服务端cpu使用
    */

    /*
        pModelDir：模型根目录/AiModel/
        deviceType：
            使用天枢模型时，该变量不生效；
            使用本地模型时，设置CORLORTONING_DEVICE_COREML使用coreml，其他内部一律转成CORLORTONING_DEVICE_CPU_C4
        dataType 如果使用算法默认值，则设置为DATA_TYPE_FLOAT
        asset：android平台assetmanage
    */
    MTAIENGINE_API bool mtlabai_sub_color_transfer_load_models(mtlabai_sub_color_transfer_handle_t handle, const char* pModelDir,
                            MTSubColorToningDeviceType deviceType,
                            MTSubColorToningDataType dataType, void *asset);
    
    MTAIENGINE_API bool mtlabai_sub_color_transfer_set_ref_data(mtlabai_sub_color_transfer_handle_t handle, const GLuint gInputRefTexture,
                            const int nReTexfWidth,
                            const int nReTexfHeight);
    
    MTAIENGINE_API bool mtlabai_sub_color_transfer_run(mtlabai_sub_color_transfer_handle_t handle, unsigned char *pImage, const int nWidth, const int nHeight);

    MTAIENGINE_API bool mtlabai_sub_color_transfer_run_GL(mtlabai_sub_color_transfer_handle_t handle, const GLuint gInputTexture,
                        const GLuint gOutputTexture,
                        const int nWidth,
                        const int nHeight);
    
    MTAIENGINE_API void mtlabai_sub_color_transfer_read_pixels(mtlabai_sub_color_transfer_handle_t handle, const GLuint nTextureID,
                            unsigned char *pData,
                            const int nWidth,
                            const int nHeight);
    
    MTAIENGINE_API GLuint mtlabai_sub_color_transfer_get_fbo(mtlabai_sub_color_transfer_handle_t handle);


}

#endif //_MT_SUB_COLOR_TRANSFER_H_