#ifndef _MT_SUB_MASK_FEATHER_H_
#define _MT_SUB_MASK_FEATHER_H_

#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/Common/MTAiEngineConfig.h>
#include <stddef.h>

extern "C" {
    typedef struct mtlabai_sub_mask_feather_handle_t mtlabai_sub_mask_feather_handle_t;
    
    /**
     * \brief 构造Mask羽化效果处理对象
     * \param[in] radius 羽化半径
     * \param[in] weighted 使用mask作为权重(使用算法默认值设置为true)
     */
    MTAIENGINE_API mtlabai_sub_mask_feather_handle_t *mtlabai_sub_mask_feather_handle_create(float radius, bool weighted);

    /**
     * \brief release
     */
    MTAIENGINE_API void mtlabai_sub_mask_feather_handle_release(mtlabai_sub_mask_feather_handle_t **handle);

    /**
     * \brief 设置素材文件路径
     * \param[in] path 素材文件路径
     * \return 设置素材文件路径是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_mask_feather_set_material_for_path(mtlabai_sub_mask_feather_handle_t *handle, const char* path);

    /**
     * \brief 设置素材文件数据
     * \param[in] bytes 素材文件的字节数
     * \param[in] data 素材文件数据指针
     * \return 设置素材文件数据是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_mask_feather_set_material_for_data(mtlabai_sub_mask_feather_handle_t *handle, size_t bytes, const uint8_t* data);

    /**
     * \brief 通过天枢设置素材文件路径
     * \param[in] path 素材文件路径，如果天枢没获取到路径，则通过该路径查找内置模型
     * \return 设置素材文件路径是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_mask_feather_set_material_for_AiDispatch(mtlabai_sub_mask_feather_handle_t *handle, const char *materialDir, void *asset);

    /**
     * \brief 执行算法
     * \param[in] src 输入图像的内存指针，单通道
     * \param[in,out] dst 输出图像的内存指针，单通道
     * \param[in] cols 处理的宽度
     * \param[in] rows 处理的高度
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_mask_feather_process(mtlabai_sub_mask_feather_handle_t *handle, const uint8_t* src, uint8_t* dst, int cols, int rows);


    /**
     * \brief 执行OpenGL版本的算法
     * \note 需要提供OpenGL环境
     * \param[in] tex_src 输入图像的纹理Id
     * \param[in] fbo_src 输入图像的FrameBufferId
     * \param[in] tex_dst 输出图像的纹理Id
     * \param[in] fbo_dst 输出图像的FrameBufferId
     * \param[in] cols 处理的宽度
     * \param[in] rows 处理的高度
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_mask_feather_process_for_openGL(mtlabai_sub_mask_feather_handle_t *handle, uint32_t tex_src, uint32_t fbo_src, uint32_t tex_dst, uint32_t fbo_dst, int cols, int rows);

}

#endif