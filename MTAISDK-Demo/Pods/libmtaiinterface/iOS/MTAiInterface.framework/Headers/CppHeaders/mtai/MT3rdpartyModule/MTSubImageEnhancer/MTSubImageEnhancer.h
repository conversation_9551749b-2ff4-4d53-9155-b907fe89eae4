#ifndef _MTSUB_IMAGE_ENHANCER_H_
#define _MTSUB_IMAGE_ENHANCER_H_

#include <mtai/Common/MTAiEngineMacro.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C"
#endif
{

typedef struct mtlabai_sub_enhancer_handle* mtlabai_sub_enhancer_handle_t;

/**
 * \brief 创建底层SDK句柄
 * \return handle 算法句柄
 */
MTAIENGINE_API mtlabai_sub_enhancer_handle_t mtlabai_sub_enhancer_create_handle();

/**
 * \brief 销毁底层SDK句柄,最后调用
 * \param[in] pHandle 算法句柄指针
 */
MTAIENGINE_API void mtlabai_sub_enhancer_release_handle(
    mtlabai_sub_enhancer_handle_t* pHandle);


/*
 //////////////////////肤色统一接口////////////////////
 */

/**
 * \brief 设置肤色统一输入
 * \param [in]  handle       算法句柄
 * \param [in]  inputFgImg   原图像 【四通道】
 * \param [in]  inputSkinImg 人像皮肤图像 【单通道】
 * \param [in]  width        图像宽【前景图与皮肤mask图长宽一致】
 * \param [in]  height       图像高【前景图与皮肤mask图长宽一致】
 * \param [in]  landmark     人脸关键点【130个关键点】
 * \param [out] index        人像序号索引
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_set_uniform_skin_input_image(mtlabai_sub_enhancer_handle_t handle, unsigned char* inputFgImg, unsigned char* inputSkinImg, int width, int height, float* landmark, int* index);

/**
 * \brief 修改肤色统一set的数据
 * \param [in] inputFgImg   原图像 【四通道】
 * \param [in] inputSkinImg 人像皮肤图像 【单通道】
 * \param [in] width        图像宽【原图像与皮肤mask图长宽一致】
 * \param [in] height       图像高【原图像与皮肤mask图长宽一致】
 * \param [in] landmark     人脸关键点【130个关键点】
 * \param [in] index        需要修改的人像序号索引
 * \return 0成功，非0失败
*/
MTAIENGINE_API int mtlabai_sub_enhancer_mod_uniform_skin_input_image(mtlabai_sub_enhancer_handle_t handle, unsigned char* inputFgImg, unsigned char* inputSkinImg, int width, int height, float* landmark, int index);

/**
 * \brief 删除肤色统一set数据
 * \param [in] index        需要删除的人像序号索引
 * \return 0成功，非0失败
*/
MTAIENGINE_API int mtlabai_sub_enhancer_del_uniform_skin_input_image(mtlabai_sub_enhancer_handle_t handle, int index);

/**
 * \brief 运行肤色统一接口
 * \param [in] handle 算法句柄
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_run_uniform_skin(mtlabai_sub_enhancer_handle_t handle);

/**
 * \brief 获取肤色统一结果
 * \param [out] outputFgImg 肤色统一后的人像前景图【四通道】
 * \param [out] width       图像宽(宽高和输入一致)
 * \param [out] height      图像高
 * \param [in]  index       人像序号索引
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_get_uniform_skin_output_image(mtlabai_sub_enhancer_handle_t handle, unsigned char* outputFgImg, int width, int height, int index);

/**
 * \brief 清除肤色统一传入的数据
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_clear_uniform_skin_data(mtlabai_sub_enhancer_handle_t handle);


/*
 //////////////////图像增强和谐接口////////////////////
 */

/**
 * \brief 通过路径的方式加载和谐模型
 * \param [in] modelPath     模型路径
 * \param [in] assetManager  安卓需要传入；其他平台传空
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_init_model_path(mtlabai_sub_enhancer_handle_t handle, const char* modelPath, void* assetManager);

/**
 * \brief 通过内存的方式加载和谐模型
 * \param [in] modelData  模型二进制数据
 * \param [in] modelSize  模型大小
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_init_model_data(mtlabai_sub_enhancer_handle_t handle, const char *modelData, const size_t modelSize);

/**
 * \brief 通过天枢的方式加载和谐模型（客户端需要先接入天枢）
 * \param [in] assetManager  安卓需要传入；其他平台传空
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_init_model_aidispath(mtlabai_sub_enhancer_handle_t handle, void* assetManager);

/**
 * \brief 设置图像增强和谐输入
 * \param [in] inputMaskImg 需要融合图上对应的人像mask图【单通道】
 * \param [in] width        人像mask图宽
 * \param [in] height       人像mask图高
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_set_enhancer_input_image(mtlabai_sub_enhancer_handle_t handle, unsigned char* inputMaskImg, int width, int height);

/**
 * \brief 修改图像增强和谐输入
 * \param [in] inputMaskImg 需要融合图上对应的人像mask图【单通道】
 * \param [in] width        人像mask图宽
 * \param [in] height       人像mask图高
 * \param [in] index        需要修改的索引
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_mod_enhancer_input_image(mtlabai_sub_enhancer_handle_t handle, unsigned char* inputMaskImg, int width, int height, int index);

/**
 * \brief 删除图像增强和谐输入
 * \param [in] index  需要删除的索引
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_del_enhancer_input_image(mtlabai_sub_enhancer_handle_t handle, int index);

/**
 * \brief 运行图像增强和谐接口
 * \param [in] inputTemplate     需要增强的图像【四通道】
 * \param [in] width             图像宽(宽高和输入一致)
 * \param [in] height            图像高
 * \param [out] outputTemplate_1 增强效果1【四通道】
 * \param [out] outputTemplate_2 增强效果2【四通道】
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_run_enhancer(mtlabai_sub_enhancer_handle_t handle, unsigned char* inputTemplate, int width, int height, unsigned char* outputTemplate_1, unsigned char* outputTemplate_2);

/**
 * \brief 删除底层mask图的重叠区域，在Run_Enhancer之前调用一次【在set数据没改变的情况下只需要调用一次】
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_del_overlap_mask(mtlabai_sub_enhancer_handle_t handle);

/**
 * \brief 和谐融合【滑杆操作】
 * \param [in] outputTemplate_1 增强效果1【四通道】
 * \param [in] outputTemplate_2 增强效果2【四通道】
 * \param [out] output          增强融合效果
 * \param [in] MTThreshold      融合强度【默认为0.5】 0.0~1.0
 * \param [in] ColorThreshold   人像区域亮度抑制上限  0~255
 * \param [in] width            增强融合效果图像宽(宽高和输入一致)
 * \param [in] height           增强融合效果图像高
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_run_fusion(mtlabai_sub_enhancer_handle_t handle, unsigned char* outputTemplate_1, unsigned char* outputTemplate_2, int width, int height, unsigned char* output, float MTThreshold, int ColorThreshold);

/**
 * \brief 和谐融合【滑杆操作】，融合强度设置为0.5，人像区域亮度抑制上限设置为
 * \param [in] outputTemplate_1 增强效果1【四通道】
 * \param [in] outputTemplate_2 增强效果2【四通道】
 * \param [out] output          增强融合效果
 * \param [in] width            增强融合效果图像宽(宽高和输入一致)
 * \param [in] height           增强融合效果图像高
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_run_fusion_default(mtlabai_sub_enhancer_handle_t handle, unsigned char* outputTemplate_1, unsigned char* outputTemplate_2, int width, int height, unsigned char* output);

/**
 * \brief 清除和谐传入的数据
 * \return 0成功，非0失败
 */
MTAIENGINE_API int mtlabai_sub_enhancer_clear_enhancer_data(mtlabai_sub_enhancer_handle_t handle);

}

#endif  // _MTSUB_IMAGE_ENHANCER_H_