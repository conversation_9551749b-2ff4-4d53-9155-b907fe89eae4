#pragma once

#include <mtai/Common/MTAiEngineDefine.h>


extern "C" {

    /*
     * matting生成前景图(外部直接调用)
     * @param [width] 输入宽
     * @param [height] 输入高
     * @param [input_rgb] 输入图片,四通道rgba
     * @param [input_alpha] 输入mask,一通道
     * @param [output_rgb] 输出fgr,三通道rgb
     */
    MTAIENGINE_API bool mtlabai_sub_photo_segment_get_matting_fgr(int width, int height, unsigned char* input_rgba, unsigned char* input_alpha, unsigned char* output_rgb);
    /*
     * matting生成前景图(外部直接调用)
     * @param [width] 输入宽
     * @param [height] 输入高
     * @param [input_rgb] 输入图片,四通道rgba
     * @param [input_alpha] 输入mask,一通道
     * @param [output_rgb] 输出fgr,三通道rgb
     * @param [blur_size] blur size,默认100
     */
    MTAIENGINE_API bool mtlabai_sub_photo_segment_get_matting_fgr_size(int width, int height, unsigned char* input_rgba, unsigned char* input_alpha, unsigned char* output_rgb, int blur_size);
}