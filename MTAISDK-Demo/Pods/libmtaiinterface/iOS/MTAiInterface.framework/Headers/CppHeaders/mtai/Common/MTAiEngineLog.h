//
// Created by IF on 2019/2/13.
//

#ifndef MTAIENGINE_MTMODULELOG_H
#define MTAIENGINE_MTMODULELOG_H

#define __STDC_FORMAT_MACROS
#include <mtai/Common/MTAiEnginePlatform.h>
#include <mtai/Common/MTAiEngineTimer.h>
#include <mtai/Common/MTAiEngineType.h>
#include <cstdio>
#include <inttypes.h>
#include <memory>
#include <string.h>
#include <mutex>

#define MTAIENGINE_RESULT_POINTS_PRINT_LIMIT size_t(10)

namespace mtai
{
    struct MTApmReportData;
}

struct AuxMessage
{
    AuxMessage() = default;
    AuxMessage(int level, void* mtai_ptr, long long time_begin, const char* jni_log_tag, const char* host_name, bool apm_report, mtai::MTApmReportData* apm_data):
    level(level),mtai_ptr(mtai_ptr),time_begin(time_begin),apm_report(apm_report),apm_data(apm_data){strcpy(this->jni_log_tag, jni_log_tag);strcpy(this->host_name,host_name);}

    int level = mtai::MTAI_LOG_LEVEL_DEBUG; //打印等级
    void* mtai_ptr = nullptr;               //AI引擎对象
    long long time_begin = 0;               //起始时间戳
    char jni_log_tag[50];                   //android平台的log标签
    char host_name[50];                     //宿主APP名
    bool apm_report = false;                //是否需要记录数据
    mtai::MTApmReportData* apm_data = nullptr;
    std::mutex apm_mutex;
};

#ifndef NEW_LOG_MESSAGE
namespace mtai
{
    void mt_print_i(int level, const char* host_name, long long time_begin, void* mtai_ptr, const char *fmt, ...);
    void mt_print_d(int level, const char* host_name, long long time_begin, void* mtai_ptr, const char *fmt, ...);
    void mt_print_w(int level, const char* host_name, long long time_begin, void* mtai_ptr, const char *fmt, ...);
    void mt_print_e(int level, const char* host_name, long long time_begin, void* mtai_ptr, const char *fmt, ...);
    void mt_log_callback(int level, const char* tag, const char* content, bool need_print=true);
}

extern MTAIENGINE_API void (*g_printCallback)(int level, const char *, const char *);

#define  LOGV(...)  { mt_print_i(mtai::MTAI_LOG_LEVEL_INFO, "", 0, nullptr, __VA_ARGS__); }
#define  LOGD(...)  { mt_print_d(mtai::MTAI_LOG_LEVEL_DEBUG, "", 0, nullptr, __VA_ARGS__); }
#define  LOGI(...)  { mt_print_i(mtai::MTAI_LOG_LEVEL_INFO, "", 0, nullptr, __VA_ARGS__); }
#define  LOGW(...)  { mt_print_w(mtai::MTAI_LOG_LEVEL_WARN, "", 0, nullptr, __VA_ARGS__); }
#define  LOGE(...)  { mt_print_e(mtai::MTAI_LOG_LEVEL_ERROR, "", 0, nullptr, __VA_ARGS__); }
#define  LOGF(...)  { mt_print_e(mtai::MTAI_LOG_LEVEL_ERROR, "", 0, nullptr, __VA_ARGS__); }


//使用以下宏的作用域内一定要有AuxMessage m_logAM变量
#define  MTAI_LOGD(...)  mtai::mt_print_d(m_pAM->level, m_pAM->host_name, m_pAM->time_begin, m_pAM->mtai_ptr, __VA_ARGS__);
#define  MTAI_LOGI(...)  mtai::mt_print_i(m_pAM->level, m_pAM->host_name, m_pAM->time_begin, m_pAM->mtai_ptr, __VA_ARGS__);
#define  MTAI_LOGW(...)  mtai::mt_print_w(m_pAM->level, m_pAM->host_name, m_pAM->time_begin, m_pAM->mtai_ptr, __VA_ARGS__);
#define  MTAI_LOGE(...)  mtai::mt_print_e(m_pAM->level, m_pAM->host_name, m_pAM->time_begin, m_pAM->mtai_ptr, __VA_ARGS__);

//C接口的日志不需要传入AuxMessage m_logAM变量
#define  MTAI_SUB_LOGD(...)  mtai::mt_print_d(c_pAM.level, c_pAM.host_name, c_pAM.time_begin, c_pAM.mtai_ptr, __VA_ARGS__);
#define  MTAI_SUB_LOGI(...)  mtai::mt_print_i(c_pAM.level, c_pAM.host_name, c_pAM.time_begin, c_pAM.mtai_ptr, __VA_ARGS__);
#define  MTAI_SUB_LOGW(...)  mtai::mt_print_w(c_pAM.level, c_pAM.host_name, c_pAM.time_begin, c_pAM.mtai_ptr, __VA_ARGS__);
#define  MTAI_SUB_LOGE(...)  mtai::mt_print_e(c_pAM.level, c_pAM.host_name, c_pAM.time_begin, c_pAM.mtai_ptr, __VA_ARGS__);
#endif

#endif //MTAIENGINE_MTMODULELOG_H
