#ifndef _MT_SUB_INTELLIGENT_FUSION_H_
#define _MT_SUB_INTELLIGENT_FUSION_H_


#include <cstddef>

#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/Common/MTAiEngineConfig.h>

#ifndef uint8_t
    typedef unsigned char uint8_t;
#endif

#ifndef uint32_t
typedef unsigned int uint32_t;
#endif

extern "C" {
    typedef struct intelligent_fusion_handle_t intelligent_fusion_handle_t;

    /**
      *@description: 创建handle
      *@param [*] 无   
      *@return  handle
     */
    MTAIENGINE_API intelligent_fusion_handle_t *
    mtlabai_sub_intelligent_fusion_handle_create();

    /**
      *@description: 销毁handle
      *@param [in] handle
      *@return  无
     */
    MTAIENGINE_API void 
    mtlabai_sub_intelligent_fusion_handle_release(intelligent_fusion_handle_t *handle);

    /**
     *@description: 初始化智能融合图像的画布
     *@param[in] width 画布宽度
     *@param[in] height 画布高度
     *@return 
    */
    MTAIENGINE_API int 
    mtlabai_sub_intelligent_fusion_prepare(intelligent_fusion_handle_t *handle, int width, int height);

    /**
      *@description: 设置图像
      *@param[in] data 输入图像的RGBA数据
      *@param[in] width 输入图像的宽度
      *@param[in] height 输入图像的高度
      *@param[out] id 图像的标识Id
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int 
    mtlabai_sub_intelligent_fusion_set_image(intelligent_fusion_handle_t *handle, const uint8_t *data, int width, int height, uint32_t *id);

    /**
      *@description: 设置图像
      *@param[in] data 输入图像的RGBA数据
      *@param[in] width 输入图像的宽度
      *@param[in] height 输入图像的高度
      *@param[in] stride 输入图像的行字节长度
      *@param[out] id 图像的标识Id
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int 
    mtlabai_sub_intelligent_fusion_set_image_with_stride(intelligent_fusion_handle_t *handle, const uint8_t *data, int width, int height, int stride, uint32_t *id);

    /**
      *@description: 根据图像标识Id来移除图像
      *@param[in] id 图像标识Id
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_remove_image(intelligent_fusion_handle_t *handle, uint32_t id);

    /**
      *@description: 设置图像的位置
      *@param[in] id 图像标识Id
      *@param[in] x 水平偏移
      *@param[in] y 垂直偏移
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_set_image_position(intelligent_fusion_handle_t *handle, uint32_t id, int x, int y);

    /**
      *@description: 设置图像的大小
      *@param[in] id 图像标识Id
      *@param[in] width 图像的宽度
      *@param[in] height 图像的高度
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_set_image_size(intelligent_fusion_handle_t *handle, uint32_t id, int width, int height);

    /**
      *@description: 未实现
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_set_image_orientation(intelligent_fusion_handle_t *handle, uint32_t id, int orientation);

    /**
      *@description: 未实现
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_set_image_vertical_flip(intelligent_fusion_handle_t *handle, uint32_t id, int flip);

    /**
      *@description: 未实现
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_set_image_horizontal_flip(intelligent_fusion_handle_t *handle, uint32_t id, int flip);

    /**
      *@description: 设置对选定图像的mask
      *@param[in] id 图像标识Id
      *@param[in] data mask数据
      *@param[in] width mask的宽度
      *@param[in] height mask的高度
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_set_mask(intelligent_fusion_handle_t *handle, uint32_t id, uint8_t *data, int width, int height);

    /**
      *@description: 移除选定图像的mask
      *@param[in] id 图像标识Id
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_remove_mask(intelligent_fusion_handle_t *handle, uint32_t id);

    /**
      *@description: 获取已设置图像的数量
      *@param[out] num 已设置图像的数量
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_get_number_images(intelligent_fusion_handle_t *handle, int *num);

    /**
      *@description: 获取图层顺序
      *@param[out] ids 图层顺序(需外部分配内存)
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_get_orders(intelligent_fusion_handle_t *handle, uint32_t *ids);

    /**
      *@description: 设置图层顺序
      *@param[in] ids 图层顺序
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_set_orders(intelligent_fusion_handle_t *handle, const uint32_t *ids);

    /**
      *@description: 获取画布的宽度
      *@return 画布的宽度
     */
    MTAIENGINE_API int 
    mtlabai_sub_intelligent_fusion_get_width();

    /**
      *@description: 获取画布的高度
      *@return 画布的高度
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_get_height();

    /**
      *@description: 执行智能融合算法
      *@param dst 处理结果，RGBA格式，需要预先申请内存
      *@return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_process(intelligent_fusion_handle_t *handle, uint8_t *dst);

    /**
     * @description: 设置是否打印调试信息
     * @param[in] enable 使能开关，默认为0：关闭，1：开启
     * @return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_enable_debug_info(intelligent_fusion_handle_t *handle, int enable);

    /**
     * @description: 设置是否显示拼接边框
     * @param[in] enable 使能开关，默认为0：关闭，1：开启
     * @return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_enable_debug_border(intelligent_fusion_handle_t *handle, int enable);

    /**
     * @description: 设置金字塔层数
     * @param[in] num 金字塔层数，取值范围[1, 10]，默认为5
     * @return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_set_pyramid_layers(intelligent_fusion_handle_t *handle, int num);

    /**
     * @description: 设置是否使用缓存
     * @param[in] enable 使能开关，0：关闭，1：开启, 默认开启
     * @return 0：成功，-1：失败
     */
    MTAIENGINE_API int
    mtlabai_sub_intelligent_fusion_enable_cache(intelligent_fusion_handle_t *handle, int enable);

    /**
     * \brief 设置mask保护比例
     * \param id 图像标识Id
     * \param ratio mask保护比例，取值范围为[0, 1]，0表示关闭保护，1表示完全保护
     * \return 处理状态
     */
     MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_set_mask_protective_ratio(intelligent_fusion_handle_t *handle, uint32_t id, float ratio);

    /**
     * \brief 设置是否使用Alpha融合
     * \param[in] enable 使能开关，默认为false
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_enable_alpha_blending(intelligent_fusion_handle_t *handle, bool enable);

    /**
     * \brief 设置是否使用布局缓存
     * \param[in] enable 使能开关，默认为false
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_enable_layout_cache(intelligent_fusion_handle_t *handle, bool enable);

    /**
     * \brief 未实现
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_set_image_scale(intelligent_fusion_handle_t *handle, uint32_t id, float scale);

    /**
     * \brief 未实现
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_set_image_crop(intelligent_fusion_handle_t *handle, uint32_t id, int x, int y, int w, int h);


    /**
     * \brief 设置重要性图边缘内间距
     * \param[in] size 重要性图边缘内间距，size≥0，默认值为10
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_set_importance_map_padding_size(intelligent_fusion_handle_t *handle, int size);

    /**
     * \brief 获取图像的位置
     * \param[in] id 图像标识_id
     * \param[out] x 水平偏移
     * \param[out] y 垂直偏移
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_image_position(intelligent_fusion_handle_t *handle, uint32_t id, int& x, int& y);

    /**
     * \brief 获取图像的大小
     * \param[in] id 图像标识_id
     * \param[out] cols 图像的宽度
     * \param[out] rows 图像的高度
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_image_size(intelligent_fusion_handle_t *handle, uint32_t id, int& cols, int& rows);

    /**
     * \brief 未实现
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_image_orientation(intelligent_fusion_handle_t *handle, uint32_t id, int& orientation);

    /**
     * \brief 未实现
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_image_vertical_flip(intelligent_fusion_handle_t *handle, uint32_t id, bool& flip);

    /**
     * \brief 未实现
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_image_horizontal_flip(intelligent_fusion_handle_t *handle, uint32_t id, bool& flip);

    /**
     * \brief 未实现
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_image_scale(intelligent_fusion_handle_t *handle, uint32_t id, float& scale);

    /**
     * \brief 未实现
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_image_crop(intelligent_fusion_handle_t *handle, uint32_t id, int& x, int& y, int& w, int& h);

    /**
     * \brief 获取mask保护比例
     * \param[in] id 图像标识_id
     * \param[out] ratio mask保护比例，取值范围为[0, 1]，0表示关闭保护，1表示完全保护
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_mask_protective_ratio(intelligent_fusion_handle_t *handle, uint32_t id, float& ratio);

    /**
     * \brief 获取金字塔层数
     * \param[out] num 金字塔层数，取值范围[1, 10]
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_pyramid_layers(intelligent_fusion_handle_t *handle, int& num);

    /**
     * \brief 获取重要性图边缘内间距
     * \param[out] size 重要性图边缘内间距，size≥0
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_importance_map_padding_size(intelligent_fusion_handle_t *handle, int& size);

    /**
     * \brief 获取已设置图像的数量
     * \param[out] num 已设置图像的数量
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_get_number_of_images(intelligent_fusion_handle_t *handle, int& num);

    /**
     * \brief 绑定重要性图内存指针
     * \param[in] data 内存指针，用于存放重要性图，当指针为nullptr时，则取消绑定
     * \return 处理状态
     */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_bind_importance_map(intelligent_fusion_handle_t *handle, uint8_t* data);
    
    /**
    * \brief 绑定重叠图内存指针
    * \param[in] data 内存指针，用于存放重叠图图，当指针为nullptr时，则取消绑定
    * \return 处理状态
    */
    MTAIENGINE_API bool mtlabai_sub_intelligent_fusion_bind_overlap_map(intelligent_fusion_handle_t *handle, uint8_t* data);

    /**
     * \brief 获取画布的宽度
     * \return 画布的宽度
     */
    MTAIENGINE_API int mtlabai_sub_intelligent_fusion_cols(intelligent_fusion_handle_t *handle);

    /**
     * \brief 获取画布的高度
     * \return 画布的高度
     */
    MTAIENGINE_API int mtlabai_sub_intelligent_fusion_rows(intelligent_fusion_handle_t *handle);

    /**
     * \brief 获取版本及编译信息
     * \return 版本及编译信息
     */
    MTAIENGINE_API const char* mtlabai_sub_intelligent_fusion_version();


}



#endif  //_MT_SUB_INTELLIGENT_FUSION_H_