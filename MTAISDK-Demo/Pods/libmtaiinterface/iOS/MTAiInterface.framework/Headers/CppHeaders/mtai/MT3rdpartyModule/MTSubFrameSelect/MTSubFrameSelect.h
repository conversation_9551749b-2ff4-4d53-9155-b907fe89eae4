#pragma once

#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/Common/MTVector.h>
#include <mtai/Common/MTAiEngineDefine.h>
#include <vector>

#ifdef __cplusplus
extern "C"
#endif

{
    typedef struct mtlabai_sub_frame_select_handle mtlabai_sub_frame_select_t;

    /**
    * @brief 初始化
    *
    * @param assetManager [in] 安卓asset管理器，预留
    * @param modelDir     [in] 模型目录
    * @return mtlabai_sub_rt_dense_hair_handle_t
    */
    MTAIENGINE_API mtlabai_sub_frame_select_t* mtlabai_sub_frame_select_init(void *assetManager, const char* modelDir);

    /**
    * @brief 加载模型统一接口
    *
    * @param  modelDir     [in] 模型存放目录
    * @param  assetManager [in] 安卓asset管理器，预留
    *
    * @return int
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_load_model(mtlabai_sub_frame_select_t* handle, void* assetManager);

    /**
    * @brief 加载模型统一接口
    *
    * @param  modelDir     [in] 模型存放目录
    * @param  assetManager [in] 安卓asset管理器，预留
    *
    * @return int
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_load_local_model(mtlabai_sub_frame_select_t* handle, const char *model_path, void* assetManager);

    MTAIENGINE_API int mtlabai_sub_frame_select_load_local_model_by_path(mtlabai_sub_frame_select_t* handle, const char *model_path, void* assetManager);

/**
* @brief (切换视频时)初始化视频信息
*
* @param videoPath [in] 视频路径
* @param startTime [in] 视频检测起始时间
* @param selectNum [in] 待返回帧数
*
* @return int
*/
    MTAIENGINE_API int mtlabai_sub_frame_select_init_video(mtlabai_sub_frame_select_t* handle, \
                                                                   const char* videoPath, int startTime, int selectNum);

    /**
    * @description: 设置只输出关键帧
    * @param {in} handle
    * @param {in} keyFrame，0:关闭，1:开启
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_set_keyframe(mtlabai_sub_frame_select_t *handle, int keyFrame);

    /**
    * @description: 设置跳帧数
    * @param {in} handle
    * @param {in} frameInterval 跳帧数
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_set_frame_interval(mtlabai_sub_frame_select_t *handle, int frameInterval);

    /**
    * @description: 开启解码、选帧统计耗时
    * @param {in} handle
    * @param {in} flag: 0 -> 不开启，1 -> 开启
    * @return -1：失败；0：成功
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_set_timer(mtlabai_sub_frame_select_t *handle, int openTimer);

    /**
    * @brief 选帧算法处理AICODEC解码出的帧数据，
    *
    * @param
    *
    * @return int
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_process(mtlabai_sub_frame_select_t* handle);

    /**
    * @brief 选帧算法处理外部传入的图片数据
    *
    * @param rgba          图片数据，rgba格式
    * @param width         图片宽
    * @param height        图片高
    * @param orientaiton   图片方向
    * @param pts           图片标记位
    *
    * @return int
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_process_with_image(mtlabai_sub_frame_select_t* handle, unsigned char* rgba, const int width, const int height, const int orientation, const int pts);

/**
* @brief 获取时间戳
*
* @param
*
* @return float
*/
    MTAIENGINE_API int mtlabai_sub_frame_select_get_timestamp(mtlabai_sub_frame_select_t* handle, std::vector<float> &ts, int &size);

    /**
    * @brief 获取选帧数据
    *
    * @param  imgData [in/out] 图片数据二级指针，内部分配，外部释放
    * @param  imgW    [in/out] 图片宽度指针
    * @param  imgH    [in/out] 图片高度指针
    * @param  imgS    [in/out] 图片stride指针
    * @param  sp      [in]     测试用，保留
    *
    * @return int
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_get_image(mtlabai_sub_frame_select_t* handle, \
                                                              unsigned char** imgData, int *imgW, int *imgH, int *imgS, float timeStamp, const char* sp);

    /**
    * @brief AICODEC手动中断解码
    *
    * @param
    *
    * @return int
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_stop(mtlabai_sub_frame_select_t* handle);


    /**
    * @brief AICODEC手动中断解码
    *
    * @param
    *
    * @return int
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_release_video(mtlabai_sub_frame_select_t* handle);

    /**
    * @brief  释放接口
    *
    * @param
    *
    * @return int
    */
    MTAIENGINE_API int mtlabai_sub_frame_select_release(mtlabai_sub_frame_select_t* handle);

}

