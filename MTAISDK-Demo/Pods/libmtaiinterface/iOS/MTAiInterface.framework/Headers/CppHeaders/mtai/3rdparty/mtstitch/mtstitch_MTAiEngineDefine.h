#ifndef MTAIENGINE_MTMODULEDEFINE_H
#define MTAIENGINE_MTMODULEDEFINE_H

// 导入导出宏定义 
#if defined(_WIN32) || defined(_WIN32_) || defined(WIN32) || defined(_WIN64_) || defined(WIN64) || defined(_WIN64)
#ifdef MTAIENGINE_DYLIB
#define MTAIENGINE_API __declspec(dllexport)
#else
#ifdef IMPORT_MTAIENGINE_DYLIB
#define MTAIENGINE_API __declspec(dllimport)
#else
#define MTAIENGINE_API
#endif
#endif
#elif defined(_ADNROID_) || defined(ANDROID) || defined(__APPLE__) || defined(__linux__) 
#if defined(MTAIENGINE_DYLIB) || defined(EXPORT_CPP_INTERFACE)
#define MTAIENGINE_API __attribute__((visibility("default")))
#else
#define MTAIENGINE_API
#endif
#else
#define MTAIENGINE_API
#endif

#endif