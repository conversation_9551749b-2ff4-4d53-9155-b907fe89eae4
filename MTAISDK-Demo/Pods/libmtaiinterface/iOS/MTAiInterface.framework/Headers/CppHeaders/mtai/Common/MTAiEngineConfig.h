#ifndef MTAIENGINECONFIG_H
#define MTAIENGINECONFIG_H

#define LIBMTAI_VERSION "0.4.0.2010.29.4.0.3-OnlyIOS" 
#define LIBMTAI_BUILDTIME "2025-03-24 14:52"

/* #undef <PERSON><PERSON><PERSON>NGINE_LICENSE_ON */

#define M<PERSON><PERSON><PERSON><PERSON><PERSON>_OPEN_SKIN_MODULE
/* #undef <PERSON><PERSON><PERSON>NG<PERSON>E_OPEN_SKIN_EYE_WRINKLE_SUBMODULE */
/* #undef M<PERSON>IENGINE_OPEN_SKIN_CROWS_FEET_SUBMODULE */
/* #undef M<PERSON>IENGINE_OPEN_SKIN_NASOLABIAL_FOLDS_SUBMODULE */
/* #undef <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_O<PERSON>EN_SKIN_PORES_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_FOREHEAD_WRINKLE_SUBMODULE */
/* #undef <PERSON><PERSON><PERSON>NG<PERSON>E_OPEN_SKIN_NEVUS_SUBMODULE */
/* #undef <PERSON><PERSON>IE<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_ACNE_SUBMODULE */
/* #undef <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_O<PERSON>EN_SKIN_PANDA_EYE_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_ROSACEA_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_ACNE_MARK_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_FLAW_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_SKIN_TONE_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_TEAR_THROUGH_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_BLACK_HEAD_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_SHINY_SUBMODULE */
/* #undef MTAIENGINE_OPEN_SKIN_EYEBAG_SUBMODULE */

#define MTAIENGINE_OPEN_FACE_MODULE
    #define MTAIENGINE_OPEN_FACE_FACE_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_VISIBILITY_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_POSEESTIMATION_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_AGE_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_AGESEA_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_GENDER_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_RACE_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_GLASSES_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_BEAUTY_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_EYELID_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_MUSTACHE_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_EAR_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_EMOTION_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_NECK_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_CHEEK_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_JAW_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_LIPS_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_FR_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_PART_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_ACTION_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_QUALITY_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_PARSING_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_DL3D_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_HEAD_SUBMODULE
/* #undef MTAIENGINE_OPEN_FACE_PART_EYE_SUBMODULE */
/* #undef MTAIENGINE_OPEN_FACE_PART_MOUTH_SUBMODULE */
    #define MTAIENGINE_OPEN_FACE_OCCLUSION_SUBMODULE
/* #undef MTAIENGINE_OPEN_FACE_CVLITE_CROP_MODULE */
/* #undef MTAIENGINE_OPEN_FACE_CVLITE_PYRAMID_DETECT_MODULE */
/* #undef MTAIENGINE_OPEN_FACE_FR_CAMERA_SUBMODULE */
/* #undef MTAIENGINE_OPEN_FACE_3DFA_AVATAR_SUBMODULE */
    #define MTAIENGINE_OPEN_FACE_3DFA_SUBMODULE
    #define MTAIENGINE_OPEN_FACE_3DFA_CROP_SUBMODULE

#define MTAIENGINE_OPEN_SEGMENT_MODULE
    #define MTAIENGINE_OPEN_SEGMENT_REALTIME_HALF_BODY_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_REALTIME_WHOLE_BODY_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_REALTIME_HAIR_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_REALTIME_FACIAL_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_REALTIME_SKIN_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_REALTIME_SKY_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_REALTIME_CW_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_REALTIME_FACECONTOUR_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_PHOTO_HALF_BODY_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_PHOTO_WHOLE_BODY_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_PHOTO_HAIR_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_PHOTO_FACIAL_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_PHOTO_SKIN_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_PHOTO_SKY_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_PHOTO_CW_SUBMODULE
    #define MTAIENGINE_OPEN_SEGMENT_PHOTO_FACECONTOUR_SUBMODULE
/* #undef MTAIENGINE_OPEN_SEGMENT_PHOTO_DEPTH_ANYTHING_SUBMODULE */

#define MTAIENGINE_OPEN_BODY_MODULE
/* #undef MTAIENGINE_OPEN_BODY_MODULE_CONTOUR_SUBMODULE */
/* #undef MTAIENGINE_OPEN_BODY_MODULE_CONTOUR38_SUBMODULE */
/* #undef MTAIENGINE_OPEN_BODY_MODULE_CONTOUR38_SUBMODULE_GREATER2032 */
    #define MTAIENGINE_OPEN_BODY_MODULE_POSE_SUBMODULE
    #define MTAIENGINE_OPEN_BODY_MODULE_HUMAN_SUBMODULE
/* #undef MTAIENGINE_OPEN_BODY_MODULE_POSE_PHOTO_SUBMODULE */
/* #undef MTAIENGINE_OPEN_BODY_MODULE_HUMAN_SMALL_SUBMODULE */
/* #undef MTAIENGINE_OPEN_BODY_MODULE_HUMAN_MIDDLE_SUBMODULE */
/* #undef MTAIENGINE_OPEN_BODY_MODULE_HUMAN_LARGE_SUBMODULE */
    #define MTAIENGINE_OPEN_BODY_MODULE_OLD_LARGE_SUBMODULE
/* #undef MTAIENGINE_OPEN_BODYINONE_MODULE_BOX_MULTI_SUBMODULE */

/* #undef MTAIENGINE_OPEN_MAKEUP_MODULE */

#define MTAIENGINE_OPEN_MAKEUP_NEW_MODULE
    #define MTAIENGINE_OPEN_MAKEUP_NEW_MODULE_NORMAL
/* #undef MTAIENGINE_OPEN_MAKEUP_NEW_MODULE_HEAVY */
/* #undef MTAIENGINE_OPEN_MAKEUP_NEW_MODULE_EVE_NORMAL */
/* #undef MTAIENGINE_OPEN_MAKEUP_NEW_MODULE_EVE_HEAVY */

#define MTAIENGINE_OPEN_ANIMAL_MODULE

#define MTAIENGINE_OPEN_FACE_ANALYSIS_MODULE

#define MTAIENGINE_OPEN_HAND_MODULE
    #define MTAIENGINE_OPEN_HAND_GESTURE_SUBMODULE
    #define MTAIENGINE_OPEN_HAND_POSE_SUBMODULE
/* #undef MTAIENGINE_OPEN_HAND_NAIL_SUBMODULE */
/* #undef MTAIENGINE_OPEN_HAND_NAIL_SMALL_SUBMODULE */

/* #undef MTAIENGINE_OPEN_FOOD_MODULE */

/* #undef MTAIENGINE_OPEN_BOUNDARYLINE_MODULE */

#define MTAIENGINE_OPEN_MATERIAL_TRACKING_MODULE

#define MTAIENGINE_OPEN_SHOULDER_POINTS_DETECTION_MODULE

#define MTAIENGINE_OPEN_INSTANCE_SEGMENT_MODULE

#define MTAIENGINE_OPEN_ORNAMENTS_DETECT_MODULE

/* #undef MTAIENGINE_OPEN_CSKETCH_DETECT_MODULE */

#define MTAIENGINE_OPEN_HAIR_MODULE

#define MTAIENGINE_OPEN_PORTRAIT_INPAINTING_MODULE

/* #undef MTAIENGINE_OPEN_FACE_HD_MODULE */

/* #undef MTAIENGINE_OPEN_TO_KID_MODULE */

#define MTAIENGINE_OPEN_IMAGE_RECOGNITION_MODULE

/* #undef MTAIENGINE_OPEN_ANCHOR_GENERATION_MODULE */

/* #undef MTAIENGINE_OPEN_SKIN_MICRO_MODULE */

/* #undef MTAIENGINE_OPEN_LANDMARK_MODULE */

/* #undef MTAIENGINE_OPEN_REMOVE_WATERMARK_MODULE */

/* #undef MTAIENGINE_OPEN_IMAGE_DETECTION_MODULE */

#define MTAIENGINE_OPEN_DL3D_MODULE

#define MTAIENGINE_OPEN_TEETH_MODULE

#define MTAIENGINE_OPEN_DETECT_FRAMEWORK_MODULE

/* #undef MTAIENGINE_OPEN_EVE_SKIN_COMPONENT_MODULE */
/* #undef MTAIENGINE_OPEN_EVE_SKIN_COMPONENT_MODULE_FAKE_RBX */
/* #undef MTAIENGINE_OPEN_EVE_SKIN_COMPONENT_MODULE_SKIN_ACNE */
/* #undef MTAIENGINE_OPEN_EVE_SKIN_COMPONENT_MODULE_SKIN_KERATIN_PLUG */
/* #undef MTAIENGINE_OPEN_EVE_SKIN_COMPONENT_MODULE_SKIN_SENSITIVITY */
/* #undef MTAIENGINE_OPEN_EVE_SKIN_COMPONENT_MODULE_SKIN_TONE */
/* #undef MTAIENGINE_OPEN_EVE_SKIN_COMPONENT_MODULE_SKIN_TYPE */
    
#define MTAIENGINE_OPEN_SKINBCC_MODULE

#define MTAIENGINE_OPEN_3DFACE_MODULE
    #define MTAIENGINE_OPEN_3DFACE_2D_SUBMODULE
    #define MTAIENGINE_OPEN_3DFACE_3D_SUBMODULE
    
#define MTAIENGINE_OPEN_BROWSEG_MODULE

#define MTAIENGINE_OPEN_BODYINONE_MODULE
    #define MTAIENGINE_OPEN_BODYINONE_MODULE_BOX_SUBMODULE
/* #undef MTAIENGINE_OPEN_BODYINONE_MODULE_BOX_SMALL_SUBMODULE */
        #define MTAIENGINE_OPEN_BODYINONE_MODULE_BOX_LARGE_SUBMODULE
    #define MTAIENGINE_OPEN_BODYINONE_MODULE_POSE_SUBMODULE
/* #undef MTAIENGINE_OPEN_BODYINONE_MODULE_POSE_CAMARA_MIDDLE_SUBMODULE */
    #define MTAIENGINE_OPEN_BODYINONE_MODULE_CONTOUR_SUBMODULE
    #define MTAIENGINE_OPEN_BODYINONE_MODULE_SHOULDER_SUBMODULE
    #define MTAIENGINE_OPEN_BODYINONE_MODULE_NECK_SUBMODULE
    #define MTAIENGINE_OPEN_BODYINONE_MODULE_BREAST_SUBMODULE
    #define MTAIENGINE_OPEN_BODYINONE_MODULE_MULTI_SUBMODULE


#define MTAIENGINE_OPEN_WRINKLEDETECTION_MODULE
    #define MTAIENGINE_OPEN_WRINKLEDETECTION_MODULE_FOREHEAD
    #define MTAIENGINE_OPEN_WRINKLEDETECTION_MODULE_EYE
    #define MTAIENGINE_OPEN_WRINKLEDETECTION_MODULE_NASO
    #define MTAIENGINE_OPEN_WRINKLEDETECTION_MODULE_NECK
    #define MTAIENGINE_OPEN_WRINKLEDETECTION_MODULE_SILKWORM

#define MTAIENGINE_OPEN_DENSEHAIR_MODULE

/* #undef MTAIENGINE_OPEN_CGSTYLE_MODULE */

/* #undef MTAIENGINE_OPEN_FOODSTYLE_MODULE */

/* #undef MTAIENGINE_OPEN_SMILE_MODULE */

/* #undef MTAIENGINE_OPEN_EVEQUALITY_MODULE */

#define MTAIENGINE_OPEN_FACEANALYSISX_MODULE

/* #undef MTAIENGINE_OPEN_KIEV3DMAKE_MODULE */

/* #undef MTAIENGINE_OPEN_COLORCORRECTION_MODULE */

#define MTAIENGINE_OPEN_EYESEGMENT_MODULE

#define MTAIENGINE_OPEN_VIDEOSTABILIZATION_MODULE

#define MTAIENGINE_OPEN_VIDEO_RECOGNITION_MODULE

/* #undef MTAIENGINE_OPEN_HIGHDOFEYELID_MODULE */

/* #undef MTAIENGINE_OPEN_EYELIDREALTIME_MODULE */

/* #undef MTAIENGINE_OPEN_VIDEO_OPTIMIZER_MODULE */

/* #undef MTAIENGINE_OPEN_FACEBLIT_MODULE */

/* #undef MTAIENGINE_OPEN_AIKIT_MODULE */

#define MTAIENGINE_OPEN_NECKWRINKLESEG_MODULE
    #define MTAIENGINE_OPEN_NECKWRINKLESEG_MODULE_NECK

#define MTAIENGINE_OPEN_AICODEC_MODULE

/* #undef MTAIENGINE_OPEN_NOSEBLEND_MODULE */

/* #undef MTAIENGINE_OPEN_MTPHOTOTIMELAPSE_MODULE */

/* #undef MTAIENGINE_OPEN_HUMAN3D_MODULE */

/* #undef MTAIENGINE_OPEN_SKIN_AR_MODULE */

#define MTAIENGINE_OPEN_EYELIDIMAGE_MODULE

#define MTAIENGINE_OPEN_NEVUS_DETECTION_MODULE

/* #undef MTAIENGINE_OPEN_EVE_AUTO_SKIN_COLOR_MODULE */

/* #undef MTAIENGINE_OPEN_EVE_PRE_DETECT_MODULE */

#define MTAIENGINE_OPEN_DOUBLECHINFIX_MODULE

/* #undef MTAIENGINE_OPEN_HAIRGROUTH_MODULE */

/* #undef MTAIENGINE_OPEN_CURL */

/* #undef MTAIENGINE_OPEN_PORTRAIT_DETECTION_MODULE */
#define MTAIENGINE_OPEN_INTELLIGENT_FUSION_MODULE_USE_GALAXY
/* #undef MTAIENGINE_OPEN_INTELLIGENT_FUSION_MODULE */

/* #undef MTAIENGINE_OPEN_EYE_LIFTING_MODULE */

#define MTAIENGINE_OPEN_RT_TEETH_RETOUCH_MODULE

/* #undef MTAIENGINE_OPEN_RESTORE_TEETH_MODULE */

/* #undef MTAIENGINE_OPEN_IMAGE_ENHANCER_MODULE */

/* #undef MTAIENGINE_OPEN_MT_OPEN_EYE_MODULE */

/* #undef MTAIENGINE_OPEN_HAIR_DYE_MODULE */

#define MTAIENGINE_OPEN_SKIN_FULL_FACE_MINI

#define MTAIENGINE_OPEN_SKIN_ACNE_FLECK_MINI

#define MTAIENGINE_OPEN_MERAK_DATA_TOOL_UTILS

#define MTAIENGINE_OPEN_HAIR_STRAIGHT_MODULE

#define MTAIENGINE_OPEN_HAIR_FLUFFY_MODULE

/* #undef MTAIENGINE_HAIR_TRANSFER_MODULE */

#define MTAIENGINE_OPEN_HAIR_CURLY_MODULE

/* #undef MTAIENGINE_OPEN_CVPLUS */

/* #undef MTAIENGINE_OPEN_AI_BEAUTY */


//__END_OF_MODULE_DEFINE__

#endif

