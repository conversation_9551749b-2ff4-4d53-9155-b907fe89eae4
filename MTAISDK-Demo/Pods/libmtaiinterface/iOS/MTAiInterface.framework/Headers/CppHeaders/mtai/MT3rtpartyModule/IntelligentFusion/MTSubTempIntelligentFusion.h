#ifndef _MT_SUB_TEMP_INTELLIGEN_FUSION_H_
#define _MT_SUB_TEMP_INTELLIGEN_FUSION_H_

#include <cstddef>
#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/Common/MTAiEngineConfig.h>

extern "C" {

    typedef struct mtlabai_sub_temp_intelligent_fusion_handle_t mtlabai_sub_temp_intelligent_fusion_handle_t;

    
    /**
     * \brief 模板化智能融合的构造函数
     */
    MTAIENGINE_API mtlabai_sub_temp_intelligent_fusion_handle_t *mtlabai_sub_temp_intelligent_fusion_handle_create();

    /**
     * \brief release
     */
    MTAIENGINE_API void mtlabai_sub_temp_intelligent_fusion_handle_release(mtlabai_sub_temp_intelligent_fusion_handle_t **handle);

    /**
     * \brief 设置图像
     * \param[in] cols 图像的宽度
     * \param[in] rows 图像的高度
     * \param[in] data 图像的内存指针，_r_g_b_a数据类型
     * \param[in] reference 是否作为引用，默认为false
     * \return 图像id，当图像id为0时，说明设置失败
     */
    MTAIENGINE_API uint32_t mtlabai_sub_temp_intelligent_fusion_set_image(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows, const void* data, bool reference);

    /**
     * \brief 设置图像
     * \param[in] cols 图像的宽度
     * \param[in] rows 图像的高度
     * \param[in] stride 图像的stride(行字节长)
     * \param[in] data 图像的内存指针，_r_g_b_a数据类型
     * \param[in] reference 是否作为引用，默认为false
     * \return 图像id，当图像id为0时，说明设置失败
     */
    MTAIENGINE_API uint32_t mtlabai_sub_temp_intelligent_fusion_set_image_with_stride(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows, size_t stride, const void* data, bool reference);

    /**
     * \brief 设置图像，纹理格式
     * \note 需要 \ref mtlabai_sub_temp_intelligent_fusion_set_gpu_mode_enable 为true才会生效
     * \param[in] cols 图像的宽度
     * \param[in] rows 图像的高度
     * \param[in] texture 图像的纹理_id
     * \return 图像id，当图像id为0时，说明设置失败
     */
    MTAIENGINE_API uint32_t mtlabai_sub_temp_intelligent_fusion_set_image_with_texture(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows, uint32_t texture);

    /**
     * \brief 通过图像id移除图像
     * \param[in] image_id 图像id
     * \return 是否成功移除图像
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_remove_image(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id获取图像的宽度
     * \param[in] image_id 图像id
     * \return 图像的宽度，当图像不存在时，返回0
     */
    MTAIENGINE_API int mtlabai_sub_temp_intelligent_fusion_get_image_width(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id获取图像的高度
     * \param[in] image_id 图像id
     * \return 图像的高度，当图像不存在时，返回0
     */
    MTAIENGINE_API int mtlabai_sub_temp_intelligent_fusion_get_image_height(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id获取图像的stride(行字节长)
     * \param[in] image_id 图像id
     * \return 图像的stride(行字节长)，当图像不存在时，返回0
     */
    size_t mtlabai_sub_temp_intelligent_fusion_get_image_stride(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id获取图像的内存指针
     * \param[in] image_id 图像id
     * \return 图像的内存指针，当图像不存在时，返回nullptr
     */
    const void* mtlabai_sub_temp_intelligent_fusion_get_image_data(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id判断图像是否存在
     * \param[in] image_id 图像id
     * \return 图像是否存在
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_is_image_exists(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id判断图像是否绑定了模板
     * \param[in] image_id 图像id
     * \return 图像是否绑定了模板
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_is_image_bind_template(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id绑定模板
     * \param[in] image_id 图像id
     * \param[in] template_id 模板_id
     * \return 绑定是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_bind_image_template(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, uint32_t template_id);

    /**
     * \brief 通过图像id解除绑定的模板
     * \param[in] image_id 图像id
     * \return 解绑是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_unbind_image_template(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id获取绑定的模板_id
     * \param[in] image_id 图像id
     * \return 绑定的模板_id
     */
    MTAIENGINE_API uint32_t mtlabai_sub_temp_intelligent_fusion_get_image_bind_template_id(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id设置图像中心位置坐标
     * \param[in] image_id 图像id
     * \param[in] x 图像中心位置的横坐标
     * \param[in] y 图像中心位置的纵坐标
     * \return 设置图像位置是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_position(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, float x, float y);

    /**
     * \brief 通过图像id获取图像中心位置坐标
     * \param[in] image_id 图像id
     * \param[out] x 图像中心位置的横坐标
     * \param[out] y 图像中心位置的纵坐标
     * \return 获取图像中心位置坐标是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_image_position(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, float& x, float& y);

    /**
     * \brief 通过图像id设置图像大小
     * \param[in] image_id 图像id
     * \param[in] cols 图像的宽度，\f$cols \gt 0\f$
     * \param[in] rows 图像的高度，\f$rows \gt 0\f$
     * \return 设置图像大小是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_size(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, float cols, float rows);

    /**
     * \brief 通过图像id获取图像大小
     * \param[in] image_id 图像id
     * \param[out] cols 图像的宽度
     * \param[out] rows 图像的高度
     * \return 获取图像大小是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_image_size(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, float& cols, float& rows);

    /**
     * \brief 通过图像id设置图像绕中心的旋转角度
     * \param[in] image_id 图像id
     * \param[in] degree 图像绕中心的旋转角度
     * \return 设置图像绕中心的旋转角度是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_rotation(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, float degree);

    /**
     * \brief 通过图像id获取图像绕中心的旋转角度
     * \param[in] image_id 图像id
     * \param[out] degree 图像绕中心的旋转角度
     * \return 获取图像绕中心的旋转角度是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_image_rotation(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, float& degree);

    /**
     * \brief 通过图像id设置是否垂直翻转
     * \param[in] image_id 图像id
     * \param[in] flip 是否垂直翻转
     * \return 设置垂直翻转是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_vertical_flip(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, bool flip);

    /**
     * \brief 通过图像id获取是否垂直翻转
     * \param[in] image_id 图像id
     * \param[out] flip 是否垂直翻转
     * \return 获取垂直翻转是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_image_vertical_flip(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, bool& flip);

    /**
     * \brief 通过图像id设置是否水平翻转
     * \param[in] image_id 图像id
     * \param[in] flip 是否水平翻转
     * \return 设置水平翻转是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_horizontal_flip(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, bool flip);

    /**
     * \brief 通过图像id获取是否水平翻转
     * \param[in] image_id 图像id
     * \param[out] flip 是否水平翻转
     * \return 获取水平翻转是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_image_horizontal_flip(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, bool& flip);

    /**
     * \brief 通过图像id设置重要性mask
     * \param[in] image_id 图像id
     * \param[in] cols 重要性mask的宽度
     * \param[in] rows 重要性mask的高度
     * \param[in] data 重要性mask的内存地址，单通道数据格式
     * \param[in] reference 是否作为引用，默认为false
     * \return 设置重要性mask是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_importance_mask(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, int cols, int rows, const void* data, bool reference);

    /**
     * \brief 通过图像id设置重要性mask
     * \param[in] image_id 图像id
     * \param[in] cols 重要性mask的宽度
     * \param[in] rows 重要性mask的高度
     * \param[in] stride 重要性mask的stride(行字节长)
     * \param[in] data 重要性mask的内存地址，单通道数据格式
     * \param[in] reference 是否作为引用，默认为false
     * \return 设置重要性mask是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_importance_mask_with_stride(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, int cols, int rows, size_t stride, const void* data, bool reference);

    /**
     * \brief 通过图像id设置重要性mask
     * \param[in] image_id 图像id
     * \param[in] cols 重要性mask的纹理宽度
     * \param[in] rows 重要性mask的纹理高度
     * \param[in] texture 重要性mask的纹理_id
     * \return 设置重要性mask纹理是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_importance_mask_with_texture(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, int cols, int rows, uint32_t texture);

    /**
     * \brief 通过图像id移除其重要性mask
     * \param[in] image_id 图像id
     * \return 移除是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_remove_image_importance_mask(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id判断其是否包含重要性mask
     * \param[in] image_id 图像id
     * \return 图像是否包含重要性mask
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_is_image_has_importance_mask(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id);

    /**
     * \brief 通过图像id设置重要性mask保护比例
     * \param[in] image_id 图像id
     * \param[in] ratio 保护比例，默认值为1，取值范围为\f$[0, 1]\f$
     * \return 设置重要性mask保护比例是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_importance_mask_protective_ratio(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, float ratio);

    /**
     * \brief 通过图像id设获取重要性mask保护比例
     * \param[in] image_id 图像id
     * \param[out] ratio 保护比例
     * \return 获取重要性mask保护比例是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_image_importance_mask_protective_ratio(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, float& ratio);

    /**
     * \brief 设置重要性mask羽化半径
     * \param[in] image_id 图像id
     * \param[in] radius 羽化半径，默认值为8，取值范围为\f$radius \ge 0 \f$
     * \return 设置重要性mask羽化半径是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_image_importance_mask_feather_radius(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, int radius);

    /**
     * \brief 获取重要性mask羽化半径
     * \param[in] image_id 图像id
     * \param[out] radius 羽化半径
     * \return 获取重要性mask羽化半径是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_image_importance_mask_feather_radius(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t image_id, int& radius);

    /**
     * \brief 设置模板
     * \param[in] cols 模板的宽度
     * \param[in] rows 模板的高度
     * \param[in] data 模板的内存指针，单通道数据格式
     * \param[in] reference 是否作为引用，默认为false
     * \return 模板_id，当模板_id为0时，说明设置失败
     */
    MTAIENGINE_API uint32_t mtlabai_sub_temp_intelligent_fusion_set_template(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows, const void* data, bool reference);

    /**
     * \brief 设置模板
     * \param[in] cols 模板的宽度
     * \param[in] rows 模板的高度
     * \param[in] stride 模板的stride(行字节长)
     * \param[in] data 模板的内存指针，单通道数据格式
     * \param[in] reference 是否作为引用，默认为false
     * \return 模板_id，当模板_id为0时，说明设置失败
     */
    MTAIENGINE_API uint32_t mtlabai_sub_temp_intelligent_fusion_set_template_with_stride(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows, size_t stride, const void* data, bool reference);

    /**
     * \brief 设置模板，多边形格式
     * \param[in] num_of_anchors 多边形的锚点数量，锚点数量至少为3个
     * \param[in] polygon 多边形的锚点，内存格式为\f$\left \{ x_0,y_0,x_1,y_1,\cdots,x_n,y_n \right \}\f$
     * \param[in] ccw 多边形锚点是否为逆时针方向环绕，使用算法默认值，则设置为true
     * \return 模板_id，当模板_id为0时，说明设置失败
     */
    MTAIENGINE_API uint32_t mtlabai_sub_temp_intelligent_fusion_set_template_with_polygon(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, size_t num_of_anchors, const int* polygon, bool ccw);

    /**
     * \brief 设置模板，纹理格式
     * \note 需要 \ref mtlabai_sub_temp_intelligent_fusion_set_gpu_mode_enable 为true才会生效
     * \param[in] cols 模板的宽度
     * \param[in] rows 模板的高度
     * \param[in] texture 模板的纹理_id
     * \return 模板_id，当模板_id为0时，说明设置失败
     */
    MTAIENGINE_API uint32_t mtlabai_sub_temp_intelligent_fusion_set_template_with_texture(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows, uint32_t texture);

    /**
     * \brief 通过模板_id移除模板
     * \param[in] template_id 模板_id
     * \return 移除模板是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_remove_template(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id);

    /**
     * \brief 通过模板_id获取模板的宽度
     * \param[in] template_id 模板_id
     * \return 模板的宽度
     */
    MTAIENGINE_API int mtlabai_sub_temp_intelligent_fusion_get_template_width(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id);

    /**
     * \brief 通过模板_id获取模板的高度
     * \param[in] template_id 模板_id
     * \return 模板的高度
     */
    MTAIENGINE_API int mtlabai_sub_temp_intelligent_fusion_get_template_height(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id);

    /**
     * \brief 通过模板_id获取模板的stride(行字节长)
     * \param[in] template_id 模板_id
     * \return 模板的stride(行字节长)
     */
    size_t mtlabai_sub_temp_intelligent_fusion_get_template_stride(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id);

    /**
     * \brief 通过模板_id获取模板的内存指针
     * \param[in] template_id 模板_id
     * \return 模板的内存指针，当模板不存在时，返回nullptr
     */
    const void* mtlabai_sub_temp_intelligent_fusion_get_template_data(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id);

    /**
     * \brief 通过模板_id判断模板是否存在
     * \param[in] template_id 模板_id
     * \return 模板是否存在
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_is_template_exists(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id);

    /**
     * \brief 判断模板是否被绑定
     * \param[in] template_id 模板_id
     * \return 模板是否被绑定
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_is_template_binding(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id);

    /**
     * \brief 设置模板的位置坐标
     * \param[in] template_id 模板_id
     * \param[in] x 模板左上角的横坐标
     * \param[in] y 模板左上角的纵坐标
     * \return 设置模板的位置坐标是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_template_position(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id, int x, int y);

    /**
     * \brief 获取模板的位置坐标
     * \param[in] template_id 模板_id
     * \param[out] x 模板左上角的横坐标
     * \param[out] y 模板左上角的纵坐标
     * \return 获取模板的位置坐标是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_template_position(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id, int& x, int& y);

    /**
     * \brief 设置多边形模板
     * \note 当接口调用时，该模板自动标记为dirty
     * \param[in] template_id 模板Id
     * \param[in] num_of_anchors 多边形的锚点数量，锚点数量至少为3个
     * \param[in] polygon 多边形的锚点，内存格式为\f$\left \{ x_0,y_0,x_1,y_1,\cdots,x_n,y_n \right \}\f$
     * \param[in] ccw 多边形锚点是否为逆时针方向环绕，使用算法默认值，则设置为true
     * \return 设置多边形模板是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_polygon_template(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id, size_t num_of_anchors, const int* polygon, bool ccw);

    /**
     * \brief 通过模板Id标记其为dirty
     * \param[in] template_id 模板Id
     * \return 标记是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_mark_template_dirty(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t template_id);

    /**
     * \brief 设置融合区域大小
     * \param[in] size 融合区域大小，\f$size\in \left [ 0, 255 \right ]\f$，默认值为0
     * \return 设置融合区域大小是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_fusion_size(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int size);

    /**
     * \brief 获取融合区域大小
     * \param[out] size 融合区域大小
     * \return 获取融合区域大小是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_fusion_size(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int& size);

    /**
     * \brief 设置图像金字塔层数
     * \param[in] levels 图像金字塔层数，\f$levels\in \left [ 1, 10 \right ]\f$，默认值为6
     * \return 设置图像金字塔层数是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_pyramid_levels(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int levels);

    /**
     * \brief 获取图像金字塔层数
     * \param[out] levels 图像金字塔层数
     * \return 获取图像金字塔层数是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_pyramid_levels(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int& levels);

    /**
     * \brief 设置半分辨率模板使能状态
     * \param[in] enable 使能状态，默认为false
     * \return 设置半分辨率模板使能状态是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_halve_resolution_templates_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool enable);

    /**
     * \brief 获取半分辨率模板使能状态
     * \param[out] enable 使能状态
     * \return 获取半分辨率模板使能状态是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_halve_resolution_templates_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool& enable);

    /**
     * \brief 设置_g_p_u模式使能状态
     * \param[in] enable 使能状态，默认为false
     * \return 设置_g_p_u模式使能状态是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_gpu_mode_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool enable);

    /**
     * \brief 获取_g_p_u模式使能状态
     * \param[out] enable 使能状态
     * \return 获取_g_p_u模式使能状态是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_gpu_mode_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool& enable);

    /**
     * \brief 设置素材文件路径
     * \param[in] path 素材文件路径
     * \return 设置素材文件路径是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_material_for_path(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, const char* path);

    /**
     * \brief 设置素材文件数据
     * \param[in] bytes 素材文件的字节数
     * \param[in] data 素材文件数据指针
     * \return 设置素材文件数据是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_material_for_data(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, size_t bytes, const uint8_t* data);

    /**
     * \brief 设置素材文件数据，通过天枢
     * \param[in] bytes 素材文件的字节数
     * \param[in] data 素材文件数据指针
     * \return 设置素材文件数据是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_material_for_AiDispatch(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, const char *materialDir, void *asset);

    /**
     * \brief 获取处理宽度
     * \return 处理宽度
     */
    MTAIENGINE_API int mtlabai_sub_temp_intelligent_fusion_width(mtlabai_sub_temp_intelligent_fusion_handle_t *handle);

    /**
     * \brief 获取处理高度
     * \return 处理高度
     */
    MTAIENGINE_API int mtlabai_sub_temp_intelligent_fusion_height(mtlabai_sub_temp_intelligent_fusion_handle_t *handle);

    /**
     * \brief 预处理
     * \param[in] cols 处理宽度
     * \param[in] rows 处理高度
     * \return 预处理是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_prepare(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows);

    /**
     * \brief 绑定重叠图
     * \param[in,out] data 重叠图的内存指针，单通道数据类型
     * \param[in] stride 重叠图的stride(行字节长)，默认值为0，表示和处理宽度等长
     * \return 绑定重叠图是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_bind_overlap_map(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint8_t* data, size_t stride);

    /**
     * \brief 绑定重叠图
     * \param[in] cols 重叠图的宽度
     * \param[in] rows 重叠图的高度
     * \param[in] tex 重叠图的纹理_id
     * \param[in] fbo 重叠图的帧缓存_id
     * \return 绑定重叠图是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_bind_overlap_map_with_texture(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows, uint32_t tex, uint32_t fbo);

    /**
     * \brief 绑定重要性图
     * \param[in,out] data 重要性图的内存指针，单通道数据类型
     * \param[in] stride 重要性图的stride(行字节长)，默认值为0，表示和处理宽度等长
     * \return 绑定重要性图是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_bind_importance_map(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint8_t* data, size_t stride);

    /**
     * \brief 绑定重要性图
     * \param[in] cols 重要性图的宽度
     * \param[in] rows 重要性图的高度
     * \param[in] tex 重要性图的纹理_id
     * \param[in] fbo 重要性图的帧缓存_id
     * \return 绑定重要性图是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_bind_importance_map_with_texture(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int cols, int rows, uint32_t tex, uint32_t fbo);

    /**
     * \brief 执行算法
     * \param[in,out] dst 输出图像的内存指针，_r_g_b_a数据类型
     * \param[in] stride 输出图像的stride(行字节长)，当stride设置为0时，将通过处理宽度计算，否则该值必须<b>大于等于</b>4倍的处理宽度，默认值为0
     * \return 执行算法是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_process(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint8_t* dst, size_t stride);

    /**
     * \brief 执行算法
     * \note
     * 1. 需要 \ref mtlabai_sub_temp_intelligent_fusion_set_gpu_mode_enable 为true才会生效
     * 2. 纹理大小和处理大小保持一至
     * \param[in] tex_dst 输出图像的纹理_id
     * \return 执行算法是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_process_with_texture(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, uint32_t tex_dst);
    
    /**
     * \brief 通过图像Id标记其为dirty
     * \param[in] image_id 图像Id
     * \return 标记是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_mark_image_dirty(uint32_t image_id);

    /**
     * \brief 设置调试目录
     * \param[in] directory 调试目录
     * \return 设置调试目录是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_debug_directory(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, const char* directory);

    /**
     * \brief 获取调试目录
     * \param[out] directory 调试目录
     * \return 获取调试目录是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_debug_directory(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, const char*& directory);

    /**
    * \brief 设置调试信息输出使能状态
    * \param[in] enable 使能状态，默认为false
    * \return 设置调试信息输出使能状态是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_debug_info_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool enable);

    /**
    * \brief 获取调试信息输出使能状态
    * \param[out] enable 使能状态
    * \return 获取调试信息输出使能状态是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_debug_info_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool& enable);

    /**
    * \brief 设置绘制调试边框使能状态
    * \param[in] enable 使能状态，默认为false
    * \return 设置绘制调试边框使能状态是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_draw_debug_border_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool enable);

    /**
    * \brief 获取绘制调试边框使能状态
    * \param[out] enable 使能状态
    * \return 获取绘制调试边框使能状态是否成功
    */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_draw_debug_border_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool& enable);

    /**
     * \brief 设置多边形模板优化使能状态
     * \param[in] enable 使能状态，默认为false
     * \return 设置多边形模板优化使能状态是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_polygon_template_optimize_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool enable);

    /**
     * \brief 获取多边形模板优化使能状态
     * \param[out] enable 使能状态
     * \return 获取多边形模板优化使能状态是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_polygon_template_optimize_enable(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, bool& enable);

    /**
     * \brief 设置四路DrawCall优化开启阈值
     * \param[in] threshold 四路DrawCall优化开启阈值，当宽高小于等于阈值时，将开启该优化，默认值为0
     * \return 设置四路DrawCall优化开启阈值是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_set_quad_optimize_threshold(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int threshold);

    /**
     * \brief 获取四路DrawCall优化开启阈值
     * \param[out] threshold 阈值
     * \return 获取四路DrawCall优化开启阈值是否成功
     */
    MTAIENGINE_API bool mtlabai_sub_temp_intelligent_fusion_get_quad_optimize_threshold(mtlabai_sub_temp_intelligent_fusion_handle_t *handle, int &threshold);

    /**
     * \brief 获取版本信息
     * \return 版本信息
     */
    MTAIENGINE_API const char* mtlabai_sub_temp_intelligent_fusion_version();

}


#endif