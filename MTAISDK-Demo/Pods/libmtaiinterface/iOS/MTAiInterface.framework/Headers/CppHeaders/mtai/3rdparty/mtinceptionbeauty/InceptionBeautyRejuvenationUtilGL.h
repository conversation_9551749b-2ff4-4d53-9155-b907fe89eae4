//
//  InceptionRejuvenataion.h
//  MTInceptionBeauty
//
//  Created by 周勉 on 2021/3/22.
//  Copyright © 2021 zj-db1192. All rights reserved.
//

#ifndef InceptionBeautyRejuvenataionUtilGL_h
#define InceptionBeautyRejuvenataionUtilGL_h


#include "InceptionBeautyUtil.h"

#ifndef GLuint
typedef unsigned int GLuint;
#endif

#ifndef GLint
typedef int GLint;
#endif

#ifndef GLenum
typedef unsigned int GLenum;
#endif

namespace mtai {
namespace mtdlbeauty
{
    class DL_INCEPTION_BEAUTY_EXPORT InceptionBeautyRejuvenationUtilGL:public InceptionBeautyUtil
    {
    public:
        InceptionBeautyRejuvenationUtilGL();
        virtual ~InceptionBeautyRejuvenationUtilGL();
        enum resultType{
            IB_REJUVENATE_REJUVENATE = 0,
            IB_REJUVENATE_BEAUTY_SHAPE = 1
        };
        
        virtual void Init();
        
        /*
        @param pModelPath:      模型路径
        @param nProcType:       处理类型:                      对应模型：
                                IB_REJUVENATION_BEST      ->  pikachu_best
                                IB_BEAUTYSHAPE_PH         ->  pichu_ph
        @param nDeviceType:     设备类型:
                                IB_DEVICE_CPU
        */
        virtual bool LoadModels(const char* pModelPath,
                                const int nProcType = IB_REJUVENATION_BEST,
                                const int nDeviceType = IB_DEVICE_GL);
        
        
        /*
        @param pModelData:      模型数据流
        @param nModelDataSize:  模型数据流尺寸
         @param nProcType:       处理类型:                      对应模型：
                                 IB_REJUVENATION_BEST      ->  pikachu_best
                                 IB_BEAUTYSHAPE_PH      ->  pichu_ph
         @param nDeviceType:     设备类型:
                                 IB_DEVICE_CPU
        */
        virtual bool LoadModelsData(const char* pModelData, const long lModelDataSize,
                                    const int nProcType = IB_REJUVENATION_BEST,
                                    const int nDeviceType = IB_DEVICE_CPU);
        /**
            添加外部的标准脸相关纹理，纹理的非0部分将作为权重施加到美型结果上
         */
        virtual void AddOuterTexture(const GLuint nTextureId);
        
        /* 运行减龄
        @param nInputTextureID:       输入图(rgba格式）
        @param nOutputTextureID:       输入图(rgba格式）
        @param nWidth:       输入图宽
        @param nHeight:      输入图高
        @param pfFacePoints: 输入图人脸点
        @param nFace:        输入图人脸数
        @param nFacePoints:  输入单个人脸点个数,
        @param  bBindTextureToFbo  [in]    是否将纹理绑定到Fbo
        @param nResultType:  输出结果：0：减龄输出，1：美型输出
        */
        virtual int RunGL(
                         const GLuint nInputTextureID,
                         const GLuint nOutputTextureID,
                         const int nWidth, const int nHeight,
                         const float* pfFacePoints,
                         const int nFace, const int nFacePoints,
                         const bool bBindTextureToFbo = false,
                         const int nResultType=IB_REJUVENATE_BEAUTY_SHAPE);
        
        /*保留接口，暂不支持*/
        virtual int RunCoreML(
                         const GLuint nInputTextureID,
                         const GLuint nOutputTextureID,
                         const int nWidth, const int nHeight,
                         const float* pfFacePoints,
                         const int nFace, const int nFacePoints,
                         const bool bBindTextureToFbo = false,
                         const int nResultType=IB_REJUVENATE_BEAUTY_SHAPE);
        /*
         获取减龄/美型结果, 减龄模型不可调节脸形变大小，嘟唇程度。
        该函数会引用输出纹理的id
         @param fpAlphaFAce， 人脸形变系数
         @param fpAlphaFAce， 嘟嘟唇系数
         @param afOutterAlpha 外部mask的alpha值，每个outermask对应一个alpha，总长度为nFaceCount * nOutter
        */
        virtual void GetResult(
                       const float* fpFaceAlpha=NULL,
                       const float* fpMouthAlpha=NULL,
                       const float* afOutterAlpha=NULL);
        /*
         获取减龄/美型结果, 减龄模型不可调节脸形变大小，嘟唇程度
         @param fpAlphaFAce， 人脸形变系数
         @param fpAlphaFAce， 嘟嘟唇系数
         @param nOutTexture，将结果保存在这个Texture中
         @param afOutterAlpha 外部mask的alpha值，每个outermask对应一个alpha，总长度为nFaceCount * nOutter
        */
        virtual void GetResult(
                       const float* fpFaceAlpha,
                       const float* fpMouthAlpha,
                       GLuint nOutTexture,
                       const float* afOuerAlpha=NULL);
        
        /*
         获取减龄/美型结果, 减龄模型不可调节脸形变大小，嘟唇程度
         该函数会引用输出纹理的id
         @param fpAlphaFAce， 人脸形变系数
         @param fpAlphaFAce， 嘟嘟唇系数
         @param nInputTexture，输入Texture，其内容需与RunGL的保持一致
         @param nOutTexture，将结果保存在这个Texture中
         @param afOutterAlpha 外部mask的alpha值，每个outermask对应一个alpha，总长度为nFaceCount * nOutter
        */
        virtual void GetResult(
                       const float* fpFaceAlpha,
                       const float* fpMouthAlpha,
                       GLuint nInputTexture,
                       GLuint nOutTexture,
                       const float* afOutterAlpha=NULL);
        
        /**
                    设置GL模型参数
         */
        virtual void InitGL(char* pShaderFile = 0, const int nTextureFloatBits = 32, const bool bEnableVertexFlag = false);
        /*获取当前模块版本**/
        static const char* GetVersion();
        virtual const unsigned char* GetMatIn(int& w, int& h) const;
        virtual const unsigned char* GetMatOut(int& w, int& h) const;

        virtual void ExitGL();
         
        virtual void SetGLContext(void* pGLContext);
        
        virtual void ClearNet();

        GLuint GetFbo() const;
        virtual void ReadResultPixels(unsigned char* data) const;
        virtual void FreeMemory();
    };
}
}
#endif /* InceptionRejuvenataion_h */
