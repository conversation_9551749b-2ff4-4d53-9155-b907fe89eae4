#ifndef _MT_SUB_VIDEO_OPTIMIZE_H_
#define _MT_SUB_VIDEO_OPTIMIZE_H_

#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/Common/MTAiEngineImage.h>
#include <mtai/Common/MTAiEngineResult.h>

extern "C" {

    typedef struct mtlabai_sub_video_optimize_handle_t mtlabai_sub_video_optimize_handle_t;

    /**
    * \brief 初始化
    * \param[in] bIsPicture 是否是图片，true图片，false视频，默认false
    * \param[in] nWidth 视频图像宽度
    * \param[in] nHeight 视频图像高度
    * \param[in] nStride 视频图像一行步长
    * \param[in] bSupport4K 设备是否支持4K分辨率
    * \param[in] nFrameRate 视频帧率，若bIsPicture为true，则设置0
    */
    MTAIENGINE_API mtlabai_sub_video_optimize_handle_t* mtlabai_sub_video_optimize_handle_create(bool bIsPicture, int nWidth, int nHeight, int nStride, bool bSupport4K, float fFrameRate);

    /*
    * 释放
    * return void
    */
    MTAIENGINE_API void mtlabai_sub_video_optimize_handle_release(mtlabai_sub_video_optimize_handle_t** handle);


    /*
    * 特殊需求模式，0代表通用，1代表完整编辑模式，默认0
    * return bool
    */
    MTAIENGINE_API bool mtlabai_sub_video_optimize_set_extra_mode(mtlabai_sub_video_optimize_handle_t* handle, int mode);


    /**
    * \brief 执行检测算法
    * \param[in] pImage0 第一帧图片, rgba
    * \param[in] pImage1 第二帧图片，若bIsPicture为true，则可不传，设置为NULL
    * \param[in] pImage2 第三帧图片，若bIsPicture为true，则可不传，设置为NULL
    * \param[in] model_path 人脸内置模型路径：例：xxx/MTAiModel/FaceDetectModel/mtface_age_fast.bin，则xxx/MTAiModel
    * \param[in] asset_manager android资源管理器
    */
    MTAIENGINE_API const int* mtlabai_sub_video_optimize_run(mtlabai_sub_video_optimize_handle_t* handle,
                                                             const unsigned char* pImage0, const unsigned char* pImage1,
                                                             const unsigned char* pImage2, const char* model_path,
                                                             void* asset_manager);
    /**
    * \brief 获取类别数
    * \return 类别数，对应GetResult返回的数组长度
    */
    MTAIENGINE_API const int mtlabai_sub_video_optimize_get_class_num(mtlabai_sub_video_optimize_handle_t* handle);

}

#endif