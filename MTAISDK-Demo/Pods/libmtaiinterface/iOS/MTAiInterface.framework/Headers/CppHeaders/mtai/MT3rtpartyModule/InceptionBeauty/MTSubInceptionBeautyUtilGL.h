#ifndef _MT_SUB_INCEPTION_BEAUTY_UTIl_GL_H_
#define _MT_SUB_INCEPTION_BEAUTY_UTIl_GL_H_

#ifndef GLuint
typedef unsigned int GLuint;
#endif

#ifndef GLint
typedef int GLint;
#endif

#ifndef GLenum
typedef unsigned int GLenum;
#endif
#include <mtai/Common/MTAiEngineDefine.h>

extern "C" {
    typedef struct mtlabai_sub_inception_beauty_GL_handle_t mtlabai_sub_inception_beauty_GL_handle_t;

    MTAIENGINE_API mtlabai_sub_inception_beauty_GL_handle_t *mtlabai_sub_inception_beauty_GL_create_handle();
    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_release(mtlabai_sub_inception_beauty_GL_handle_t **handle);
    
    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_init(mtlabai_sub_inception_beauty_GL_handle_t *handle);
            
    /*
    @param pModelPath:      模型路径
    @param nProcType:       处理类型:                      对应模型：
                            MTLAB_SUB_IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                            MTLAB_SUB_IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                            MTLAB_SUB_IB_BALANCE_SKIN_BEST      ->  snoopy_best
    @param nDeviceType:     设备类型:
                            MTLAB_SUB_IB_DEVICE_CPU(该函数禁用cpu)
                            MTLAB_SUB_IB_DEVICE_CUDA(该函数禁用cuda)
                            MTLAB_SUB_IB_DEVICE_GL
                            MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU
    */
    MTAIENGINE_API bool mtlabai_sub_inception_beauty_GL_load_models(mtlabai_sub_inception_beauty_GL_handle_t *handle, const char* pModelPath,
                            const int nProcType, const int nDeviceType);
    /*
    @param pModelPath:      模型路径
    @param bPath2File       true:通过pModelPath可以定位到具体的模型文件，此时nProcType将失效。
                            false: pModelPath只定位到模型所在文件夹，需通过nProcType指定模型文件
    @param nProcType:       处理类型:                      对应模型：
                            MTLAB_SUB_IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                            MTLAB_SUB_IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                            MTLAB_SUB_IB_BALANCE_SKIN_BEST      ->  snoopy_best
    @param nDeviceType:     设备类型:
                            MTLAB_SUB_IB_DEVICE_CPU(该函数禁用cpu)
                            MTLAB_SUB_IB_DEVICE_CUDA(该函数禁用cuda)
                            MTLAB_SUB_IB_DEVICE_GL
                            MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU
    */
    MTAIENGINE_API bool mtlabai_sub_inception_beauty_GL_load_models_judge(mtlabai_sub_inception_beauty_GL_handle_t *handle, const char* pModelPath,
                            const bool bPath2File, const int nProcType, const int nDeviceType);
    
    
    
    /*
    @param pModelData:      模型数据流
    @param nModelDataSize:  模型数据流尺寸
    @param nProcType:       处理类型:                      对应模型：
                            MTLAB_SUB_IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                            MTLAB_SUB_IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                            MTLAB_SUB_IB_BALANCE_SKIN_BEST      ->  snoopy_best
    @param nDeviceType:     设备类型:
                            MTLAB_SUB_IB_DEVICE_CPU(该函数禁用cpu)
                            MTLAB_SUB_IB_DEVICE_CUDA(该函数禁用cuda)
                            MTLAB_SUB_IB_DEVICE_GL
                            MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU
    */
    MTAIENGINE_API bool mtlabai_sub_inception_beauty_GL_load_models_data(mtlabai_sub_inception_beauty_GL_handle_t *handle, const char* pModelData, const long lModelDataSize,
                                const int nProcType, const int nDeviceType);

    /*
    @param key:             模型key
    @param nProcType:       处理类型:                      对应模型：
                            MTLAB_SUB_IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                            MTLAB_SUB_IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                            MTLAB_SUB_IB_BALANCE_SKIN_BEST      ->  snoopy_best
    @param nDeviceType:     设备类型:
                            MTLAB_SUB_IB_DEVICE_CPU(该函数禁用cpu)
                            MTLAB_SUB_IB_DEVICE_CUDA(该函数禁用cuda)
                            MTLAB_SUB_IB_DEVICE_GL
                            MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU
    */

    MTAIENGINE_API bool mtlabai_sub_inception_beauty_GL_load_models_AiDispatch(mtlabai_sub_inception_beauty_GL_handle_t *handle, const char* key,
                                const int nProcType, const int nDeviceType);
    
    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_set_high_pass_flag(mtlabai_sub_inception_beauty_GL_handle_t *handle, const int nHighPassFlag);
    
    /*
    @brief: 调用函数，模型前向和其他流程都跑cpu
    @param pImage:       输入图(rgba格式）
    @param nWidth:       输入图宽
    @param nHeight:      输入图高
    @param pfFacePoints: 输入图人脸点
    @param nFace:        输入图人脸数
    @param nFacePoints:  输入单个人脸点个数
    */
    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_run(mtlabai_sub_inception_beauty_GL_handle_t *handle, unsigned char* pImage, const int nWidth, const int nHeight,
                        const float* pfFacePoints, const int nFace, const int nFacePoints);


    MTAIENGINE_API const unsigned char* mtlabai_sub_inception_beauty_GL_get_mat_in(mtlabai_sub_inception_beauty_GL_handle_t *handle, int& width, int& height);
    MTAIENGINE_API const unsigned char* mtlabai_sub_inception_beauty_GL_get_mat_out(mtlabai_sub_inception_beauty_GL_handle_t *handle, int& width, int& height);
    
    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_init_GL(mtlabai_sub_inception_beauty_GL_handle_t *handle, char* pShaderFile, const int nTextureFloatBits, const bool bEnableVertexFlag);

    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_exit_GL(mtlabai_sub_inception_beauty_GL_handle_t *handle);
    
    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_set_GL_context(mtlabai_sub_inception_beauty_GL_handle_t *handle, void* pGLContext);
    
    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_clear_net(mtlabai_sub_inception_beauty_GL_handle_t *handle);

    MTAIENGINE_API GLuint mtlabai_sub_inception_beauty_GL_get_fbo(mtlabai_sub_inception_beauty_GL_handle_t *handle);
    MTAIENGINE_API void mtlabai_sub_inception_beauty_GL_read_pixels(mtlabai_sub_inception_beauty_GL_handle_t *handle, const GLuint nTextureID, unsigned char* data, const int nWidth, const int nHeight);

    /*
        @brief: 调用函数，模型前向和其他流程都跑gl3.0
        @param  nInputTextureID    [in]    待处理图像纹理ID
        @param  nOutputTextureID   [out]   处理完毕图像纹理ID
        @param  nWidth             [in]    图像宽
        @param  nHeight            [in]    图像高
        @param  pfFacePoints       [in]    图像非归一化人脸点
        @param  nFace              [in]    人脸数
        @param  nFacePoints        [in]    人脸点个数
        @param  bBindTextureToFbo  [in]    是否将纹理绑定到Fbo
        */
    MTAIENGINE_API int mtlabai_sub_inception_beauty_GL_run_GL(mtlabai_sub_inception_beauty_GL_handle_t *handle, const GLuint nInputTextureID,
                        const GLuint nOutputTextureID,
                        const int nWidth, const int nHeight,
                        const float* pfFacePoints,
                        const int nFace, const int nFacePoints,
                        const bool bBindTextureToFbo);
    
    /*
        @brief: 在纯gl模式下无法跑coreml-gl混合模式，该函数返回错误信息
        */
    MTAIENGINE_API int mtlabai_sub_inception_beauty_GL_run_coreML(mtlabai_sub_inception_beauty_GL_handle_t *handle, const GLuint nInputTextureID,
                            const GLuint nOutputTextureID,
                            void* pInputCVPixelBuffer,
                            void* pOutputCVPixelBuffer,
                            const int nWidth, const int nHeight,
                            const float* pfFacePoints,
                            const int nFace, const int nFacePoints,
                            const bool bBindTextureToFbo);

    /*
        @brief: 调用函数，模型前向和其他流程都跑gl3.0
        @param  nInputTextureID    [in]    待处理图像纹理ID
        @param  nOutputTextureID   [out]   处理完毕图像纹理ID
        @param  nWidth             [in]    图像宽
        @param  nHeight            [in]    图像高
        @param  pfFacePoints       [in]    图像非归一化人脸点
        @param  nFace              [in]    人脸数
        @param  nFacePoints        [in]    人脸点个数
        @param  bBindTextureToFbo  [in]    是否将纹理绑定到Fbo
        @param  bFuse              [in]    是否估算人脸区域并进行alpha融合
        */
    MTAIENGINE_API int mtlabai_sub_inception_beauty_GL_run_GL_fuse(mtlabai_sub_inception_beauty_GL_handle_t *handle, const GLuint nInputTextureID,
                        const GLuint nOutputTextureID,
                        const int nWidth, const int nHeight,
                        const float* pfFacePoints,
                        const int nFace, const int nFacePoints,
                        const bool bBindTextureToFbo,
                        const bool bFuse);

    /*
        @brief: 在纯gl模式下无法跑coreml-gl混合模式，该函数返回错误信息
        */
    MTAIENGINE_API int mtlabai_sub_inception_beauty_GL_run_coreML_fuse(mtlabai_sub_inception_beauty_GL_handle_t *handle, const GLuint nInputTextureID,
                            const GLuint nOutputTextureID,
                            void* pInputCVPixelBuffer,
                            void* pOutputCVPixelBuffer,
                            const int nWidth, const int nHeight,
                            const float* pfFacePoints,
                            const int nFace, const int nFacePoints,
                            const bool bBindTextureToFbo,
                            const bool bFuse);


}


#endif