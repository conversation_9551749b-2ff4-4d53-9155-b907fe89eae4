#ifndef MTAIENGINE_GLINCLUDE_H
#define MTAIENGINE_GLINCLUDE_H

#if defined(MTAIENGINE_PLATFORM_IOS)
#include <OpenGLES/ES3/gl.h>
#include <OpenGLES/gltypes.h>
#elif defined(MTAIENGINE_PLATFORM_OSX)
#include <pygl/pygl.h>
#elif defined(MTAIENGINE_PLATFORM_WINDOWS)
#include <GL/glew.h>
#elif defined(MTAIENGINE_PLATFORM_ANDROID) || defined(MTAIENGINE_PLATFORM_LINUX)
#if DYNAMIC_ES3
#include "mtcvlite/glbase/glUtil/gl3stub.h"
#else
#include <GLES3/gl3.h>
#endif
#else
#error "NO GL SUPPORT"
#endif

#endif //MTAIENGINE_GLINCLUDE_H