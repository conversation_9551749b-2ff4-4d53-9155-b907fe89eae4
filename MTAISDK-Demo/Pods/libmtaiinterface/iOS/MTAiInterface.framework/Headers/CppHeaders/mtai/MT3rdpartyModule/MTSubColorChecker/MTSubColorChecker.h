#ifndef _MTSUB_COLORTONING_H_
#define _MTSUB_COLORTONING_H_

#include <mtai/Common/MTAiEngineMacro.h>

#include <cstddef>
#include <stdint.h>

#ifdef __cplusplus
extern "C"
#endif
{
	typedef struct mtlabai_sub_color_checker_handle* mtlabai_sub_color_checker_handle_t;

	typedef struct mtlabai_sub_color_checker_locate_result* mtlabai_sub_color_checker_result_t;
	/**
	* \brief 创建底层SDK句柄
	* \return handle 算法句柄
	*/
	MTAIENGINE_API mtlabai_sub_color_checker_handle_t mtlabai_sub_color_checker_create_handle();

	/**
	* \brief 销毁底层SDK句柄
	* \param[in] handle 算法句柄
	*/
	MTAIENGINE_API void mtlabai_sub_color_checker_destroy_handle(mtlabai_sub_color_checker_handle_t *handle);

	/**
	* \brief 定位并返回内部创建的色卡22个色块的中心点坐标结果数据
	* \param[in] handle 算法句柄
	* \param[in] rgba_data RGBA数据
	* \param[in] width RGBA数据宽度
	* \param[in] height RGBA数据高度
	* \param[in] gramma_lut gamma表数组, 长度为256
	* \param[in] ref_point_count 参考点个数
	* \param[in] ref_points 参考点数组, 长度为2 * ref_point_count, 不设置参考点则设为空指针
	* \param[out] p_result 色卡22个色块的中心点坐标结果数据, 内部创建内存，外部释放
	* \return[int] 0 成功, 其他 失败
	*/
	MTAIENGINE_API int mtlabai_sub_color_checker_locate(mtlabai_sub_color_checker_handle_t handle, 
														uint8_t *rgba_data, int width, int height, 
														uint8_t *gamma_lut, size_t ref_point_count, int *p_ref_points, 
														mtlabai_sub_color_checker_result_t *p_result);

	/**
	* \brief 销毁色卡定位结果
	* \param[in] result 色卡定位结果具柄
	*/
	MTAIENGINE_API void mtlabai_sub_color_checker_destroy_locate_result(mtlabai_sub_color_checker_result_t *result);

	/**
	* \brief 获取rgb增益结果(顺序:bgr)
	* \param[in] result 色卡定位结果具柄
	* \param[out] size float个数
	* \return[float *] rgb增益, 引用result内部创建内存, 无须释放
	*/
	MTAIENGINE_API const float * mtlabai_sub_color_checker_result_get_bgr_gain(mtlabai_sub_color_checker_result_t result, size_t *size);

	/**
	* \brief 获取颜色校正矩阵(3 x 3)
	* \param[in] result 色卡定位结果具柄
	* \param[out] size float个数
	* \param[in] result 颜色校正矩阵, 引用result内部创建内存, 无须释放
	*/
	MTAIENGINE_API const float * mtlabai_sub_color_checker_result_get_color_correct_matrix(mtlabai_sub_color_checker_result_t result, size_t *size);

	/**
	* \brief 获取色卡22个色块的中心点坐标数组(xyxy...)
	* \param[in] result 色卡定位结果具柄
	* \param[out] size 数组长度
	* \param[in] result 色卡22个色块中心坐标, 引用result内部创建内存, 无须释放
	*/
	MTAIENGINE_API const float * mtlabai_sub_color_checker_result_get_checker_points(mtlabai_sub_color_checker_result_t result, size_t *size);

	/**
	* \brief 获取色卡上2个标识符中心点的坐标数组(xyxy...)
	* \param[in] result 色卡定位结果具柄
	* \param[out] size 数组长度
	* \param[in] result 色卡2个标识符中心点的坐标, 引用result内部创建内存, 无须释放
	*/
	MTAIENGINE_API const float * mtlabai_sub_color_checker_result_get_marker_points(mtlabai_sub_color_checker_result_t result, size_t *size);

}

#endif // _MTSUB_COLORTONING_H_
