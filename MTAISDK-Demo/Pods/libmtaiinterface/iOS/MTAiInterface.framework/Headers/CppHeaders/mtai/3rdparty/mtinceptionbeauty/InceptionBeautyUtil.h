/*****************************************************************
* CPU接口(基类)
* Copyright (c) 2021 MEITU. All rights reserved.
*
* @version: 0.0.3.0
*
* @author:  gary_zou
*
* @email:  <EMAIL>
*
* @date: 2021-08-09
*
* @note: 目前实现功能：匀肤。
*
* @usage: 对外接口。枚举包含模型类型、设备类型；包括CPU加载模型和调用接口。
*
* @change:
*
******************************************************************/
#pragma once
#ifndef InceptionBeautyUtil_h
#define InceptionBeautyUtil_h

#include "InceptionBeautyDefine.h"

namespace mtai {
namespace mtdlbeauty
{
//    typedef struct InceptionBeautyParam
//    {
//        float balance_face_alpha = 1.0f;
//
//        float reju_face_alpha = 0.0f;
//    }InceptionBeautyParam;

    class DL_INCEPTION_BEAUTY_EXPORT InceptionBeautyUtil
    {
    public:
        enum InceptionBeautyProcType
        {
            IB_BALANCE_SKIN = 8,
            IB_BALANCE_SKIN_PHOTO = 9,
            IB_BALANCE_SKIN_REALTIME = 10,
            IB_BALANCE_SKIN_BEST = 11,
            IB_BALANCE_SKIN_FAST = 12,
            IB_RESTORE_FACE = 16,
            IB_REJUVENATION_BEST = 20,
            IB_BEAUTYSHAPE_PH = 21
        };
        
        enum InceptionBeautyDeviceType
        {
            IB_DEVICE_CPU = 0,
            IB_DEVICE_CUDA,
            IB_DEVICE_GL = 8,                      //模型前向和其他流程都跑gl3.0
            IB_DEVICE_GL_MODEL_CPU,                //模型前向跑cpu，其他流程跑gl2.0或gl3.0
            IB_DEVICE_GL_MODEL_NPU,                //模型前向跑npu，其他流程跑gl2.0或gl3.0，只适用于华为npu
            IB_DEVICE_GL_MODEL_CL,                 //模型前向跑cl，其他流程跑gl2.0或gl3.0，只适用于高通芯片安卓机
            IB_DEVICE_GL_MODEL_COREML,             //模型前向跑coreML，其他流程根据不同调用函数选择跑gl2.0或gl3.0，只适用于ios 14.0以上系统
        };
        
    public:
        InceptionBeautyUtil();
        virtual ~InceptionBeautyUtil();
        
        /*获取当前模块版本**/
        static const char* GetVersion();
        
        virtual void Init();
        
        //不允许使用拷贝构造函数和等于操作
        InceptionBeautyUtil(InceptionBeautyUtil& rhs) = delete;
        InceptionBeautyUtil& operator=(InceptionBeautyUtil& rhs) = delete;
        
        
        /*
        @param pModelPath:      模型路径
        @param nProcType:       处理类型:                      对应模型：
                                IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                                IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                                IB_BALANCE_SKIN_BEST      ->  snoopy_best
        @param nDeviceType:     设备类型:
                                IB_DEVICE_CPU
                                IB_DEVICE_CUDA
                                IB_DEVICE_GL(该函数禁用gl)
                                IB_DEVICE_GL_MODEL_CPU(该函数禁用gl)
        */
        virtual bool LoadModels(const char* pModelPath,
                                const int nProcType = IB_BALANCE_SKIN_PHOTO,
                                const int nDeviceType = IB_DEVICE_CPU);
        
        /*
        @param pModelPath:      模型路径
        @param bPath2Fule       true:通过pModelPath可以定位到具体的模型文件，此时nProcType将失效。
                                false: pModelPath只定位到模型所在文件夹，需通过nProcType指定模型文件
        @param nProcType:       处理类型:                      对应模型：
                                IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                                IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                                IB_BALANCE_SKIN_BEST      ->  snoopy_best
        @param nDeviceType:     设备类型:
                                IB_DEVICE_CPU
                                IB_DEVICE_CUDA
                                IB_DEVICE_GL(该函数禁用gl)
                                IB_DEVICE_GL_MODEL_CPU(该函数禁用gl)
        */
        virtual bool LoadModels(const char* pModelPath,
                                const bool bPath2File,
                                const int nProcType = IB_BALANCE_SKIN_PHOTO,
                                const int nDeviceType = IB_DEVICE_CPU);
        /*
        @param pModelData:      模型数据流
        @param nModelDataSize:  模型数据流尺寸
        @param nProcType:       处理类型:                      对应模型：
                                IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                                IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                                IB_BALANCE_SKIN_BEST      ->  snoopy_best
        @param nDeviceType:     设备类型:
                                IB_DEVICE_CPU
                                IB_DEVICE_CUDA
                                IB_DEVICE_GL(该函数禁用gl)
                                IB_DEVICE_GL_MODEL_CPU(该函数禁用gl)
        */
        virtual bool LoadModelsData(const char* pModelData, const long lModelDataSize,
                                    const int nProcType = IB_BALANCE_SKIN_PHOTO,
                                    const int nDeviceType = IB_DEVICE_CPU);
        
        virtual void SetHighPassFlag(const int nHighPassFlag = 100);
        
        
        
        /*
        @brief: 调用函数，模型前向和其他流程都跑cpu
        @param pImage:       输入图(rgba格式）
        @param nWidth:       输入图宽
        @param nHeight:      输入图高
        @param pfFacePoints: 输入图人脸点
        @param nFace:        输入图人脸数
        @param nFacePoints:  输入单个人脸点个数
        */
        virtual void Run(unsigned char* pImage, const int nWidth, const int nHeight,
                         const float* pfFacePoints, const int nFace, const int nFacePoints);
        
        
        
    protected:
        void* m_pInceptionBeautyDL;
        
    };
}// end namespace mtdlbeauty
}// end namespace mtai
#endif /* InceptionBeautyUtil_h */
