//
//  InceptionRejuvenataion.h
//  MTInceptionBeauty
//
//  Created by 周勉 on 2021/3/22.
//  Copyright © 2021 zj-db1192. All rights reserved.
//

#ifndef InceptionBeautyRejuvenataionUtil_h
#define InceptionBeautyRejuvenataionUtil_h


#include "InceptionBeautyUtil.h"

namespace mtai {
namespace mtdlbeauty
{
    class DL_INCEPTION_BEAUTY_EXPORT InceptionBeautyRejuvenationUtil:public InceptionBeautyUtil
    {
    public:
        InceptionBeautyRejuvenationUtil();
        ~InceptionBeautyRejuvenationUtil();
        
        enum resultType{
            IB_REJUVENATE_REJUVENATE = 0,
            IB_REJUVENATE_BEAUTY_SHAPE = 1
        };
        
        virtual void Init();
        
        /*
        @param pModelPath:      模型路径
        @param nProcType:       处理类型:                      对应模型：
                                IB_REJUVENATION_BEST      ->  pikachu_best
        @param nDeviceType:     设备类型:
                                IB_DEVICE_CPU
        */
        virtual bool LoadModels(const char* pModelPath,
                                const int nProcType = IB_REJUVENATION_BEST,
                                const int nDeviceType = IB_DEVICE_CPU);
        
        
        /*
        @param pModelData:      模型数据流
        @param nModelDataSize:  模型数据流尺寸
         @param nProcType:       处理类型:                      对应模型：
                                 IB_REJUVENATION_BEST      ->  pikachu_best
         @param nDeviceType:     设备类型:
                                 IB_DEVICE_CPU
        */
        virtual bool LoadModelsData(const char* pModelData, const long lModelDataSize,
                                    const int nProcType = IB_REJUVENATION_BEST,
                                    const int nDeviceType = IB_DEVICE_CPU);
        
        /*
        @param pImage:       输入图(rgba格式）
        @param nWidth:       输入图宽
        @param nHeight:      输入图高
        @param pfFacePoints: 输入图人脸点
        @param nFace:        输入图人脸数
        @param nFacePoints:  输入单个人脸点个数,
        @param nResultType:  输出结果：0：减龄输出，1：美型输出
        */
        virtual void Run(
                         unsigned char* pImage,
                         const int nWidth,
                         const int nHeight,
                         const float* pfFacePoints,
                         const int nFace,
                         const int nFacePoints,
                         const int nResultType=IB_REJUVENATE_BEAUTY_SHAPE);
        /*
         获取减龄/美型结果, 减龄模型不可调节脸形变大小，嘟唇程度
         @param fpAlphaFAce， 人脸形变系数
         @param fpAlphaFAce， 嘟嘟唇系数
        */
        virtual unsigned char* GetResult(const float* fpAlphaFAce=NULL, const float* fpAlphaMouth=NULL, const float* pOutterAlpha=NULL, unsigned char* pResult=NULL);
        
        virtual void AddOuterMask(unsigned char* pImage, const int nWidth, const int nHeight);
        static const char* GetVersion();
    };
}
}
#endif /* InceptionRejuvenataion_h */
