#pragma once
#include <mtai/Common/MTAiEngineOption.h>
#include <mtai/Common/MTVector.h>

namespace mtai
{
    enum MTDL3DEnableEnum : uint64_t{
         MT_DL3D_ENABLE_NONE                    = 0x0,              // 无检测
         MT_DL3D_ENABLE_NET				        = MT_MASK(0),		// net参数估计
		 MT_DL3D_ENABLE_MESH			        = MT_MASK(1),		// mesh人脸重建
         MT_DL3D_ENABLE_TIME                    = MT_MASK(2),       // 获取运行耗时
         MT_DL3D_ENABLE_DEPEND_OUTSIDE_FACE     = MT_MASK(3),       // 依赖外部人脸, 需要传入人脸点(face_points_list)，人脸id(face_id_list)，图像宽(nImageWidth)，图像高(nImageHeight)，图像方向(nImageOrientation)
         MT_DL3D_ENABLE_RIGGING                 = MT_MASK(4),       // rigging驱动
    };

    enum eDL3DSpeed
    {
        MTDL3D_SUPERULTRAFAST = 0, //超级超级快 打光啥的可以用这个
        MTDL3D_ULTRAFAST = 1, //超级快模式
        MTDL3D_ULTRAFASTV1 = 2, //保证UV效果用这个
        MTDL3D_ULTRAFASTV2 = 3,  
        MTDL3D_ULTRAFASTV3 = 4,
        MTDL3D_FAST = 5, //快速模式
        MTDL3D_NORMAL = 6,   //一般模式
        MTDL3D_ACCURATE =7,  //精准模式 default
    };

    class MTAIENGINE_API MTDL3DModuleOption : public MTAiEngineOption{

    public:

        // 速度模式（运行时使用生效，注册时使用不生效）
        eDL3DSpeed DL3DSpeed = MTDL3D_ACCURATE;

        // 只使用ImageVideo模式（运行时使用生效，注册时使用不生效）
        bool UseImageVideoOnly = false;

        // 启用Rigging（运行时使用生效，注册时使用不生效。依赖：注册与运行需开启MT_DL3D_ENABLE_RIGGING选项）
        bool bRigging = false;

        // 单独设置某个开关
        void SetSigEnaOption(MTDL3DEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MTDL3DEnableEnum flag);//GetSingleEnableOptionStatus
 
        // 模块类型
        MTAiEngineType MuduleType() override;
        
        std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;
    
        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

    };

    inline MTAiEngineType MTDL3DModuleOption::MuduleType() {return MTAiEngineType_DL3DModule;}
    inline void MTDL3DModuleOption::SetSigEnaOption(MTDL3DEnableEnum flag) { enable_option_ |= flag; } //SetSingleEnableOption
    inline bool MTDL3DModuleOption::GetSigEnaOptionStatus(MTDL3DEnableEnum flag){ return ((enable_option_ & flag) == flag); }

}

