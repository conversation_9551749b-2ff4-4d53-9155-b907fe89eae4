/*
 * @Author: your name
 * @Date: 2021-08-10 20:07:11
 * @LastEditTime: 2021-08-12 14:02:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /mtstitch/AIEngine/mtstitch_del_repeat.h
 */

#ifndef _MTSTITCH_DEL_REPEAT_H_
#define _MTSTITCH_DEL_REPEAT_H_

#include "mtstitch_MTAiEngineDefine.h"


extern "C" {

    typedef struct stitch_del_repeat_handle_t stitch_del_repeat_handle_t;
    typedef struct stitch_del_repeat_result_t stitch_del_repeat_result_t;
    typedef struct stitch_del_repeat_image_t stitch_del_repeat_image_t;


    enum stitch_del_repeat_return {
        DEL_REPEAT_RETURN_FAILURE = -1,
        DEL_REPEAT_RETURN_SUCCESS = 0,
    };

    enum stitch_del_repeat_format {
        stitch_del_repeat_format_gray,
        stitch_del_repeat_format_rgba
    };

    /**
     * @description: 创建images句柄，只支持gray、rgba两种格式
     * @param 无
     */
    MTAIENGINE_API stitch_del_repeat_image_t *
    mtlabai_sub_stitch_del_repeat_image_adaptor(unsigned char *image_data, int height, int width, stitch_del_repeat_format format);

    /**
     * @description: 释放images句柄
     * @param {in} images 句柄
     */
    MTAIENGINE_API void
    mtlabai_sub_stitch_del_repeat_image_release(stitch_del_repeat_image_t *image);



    /**
     * @description: 创建图片去重拼接句柄
     * @param 无
     */
    MTAIENGINE_API stitch_del_repeat_handle_t *
    mtlabai_sub_stitch_del_repeat_handle_create();

    /**
     * @description: 释放图片去重拼接句柄
     * @param {in} handel 创建的句柄
     */
    MTAIENGINE_API void
    mtlabai_sub_stitch_del_repeat_handle_release(stitch_del_repeat_handle_t *handle);

    /**
     * @description: 创建result句柄
     * @param 无
     */
    MTAIENGINE_API stitch_del_repeat_result_t *
    mtlabai_sub_stitch_del_repeat_result_create();

    /**
     * @description: 释放result
     * @param {in} result 创建的句柄
     */
    MTAIENGINE_API void
    mtlabai_sub_stitch_del_repeat_result_release(stitch_del_repeat_result_t *result);

    /**
     * @description: 图片去重拼接 run
     * @param {in} handle 
     * @param {in} result 
     * @param {in} images 图片信息数组，image为gray格式
     * @param {in} image_num 传入的图片个数
     */
    MTAIENGINE_API int 
    mtlabai_sub_stitch_del_repeat_run(stitch_del_repeat_handle_t *handle,
                                    stitch_del_repeat_result_t *result,
                                    stitch_del_repeat_image_t **images, int image_num);

    /**
     * @description: 图片去重拼接获取结果
     * @param {in} handle 创建的句柄
     * @param {in} result 结果句柄
     * @param {out} upper 每个图片的上界
     * @param {out} lower 每个图片的下届
     * @param {out} num 个数
     * @param {out} nnrmalize 是否归一化
     * @return -1：失败；0:成功
     */
    MTAIENGINE_API int 
    mtlabai_sub_stitch_del_repeat_result_get_bounds(stitch_del_repeat_result_t *result, float **upper, float **lower, int *num, int *normalize);

}



#endif   /* _MTSTITCH_DEL_REPEAT_H_ */