//
//  FaceOptimizeDefine.h
//  MTFaceOptimize
//
//  Created by meitu on 2020/5/29.
//  Copyright © 2020 meitu. All rights reserved.
//

#ifndef DL_INCEPTION_BEAUTY_DEFINE_H_
#define DL_INCEPTION_BEAUTY_DEFINE_H_

#include <stdio.h>
#include <string>

//plaform
#if defined(_WIN32) || defined(WIN32) || defined(WIN64) || defined(_WIN64)
#define PLATFORM_WINDOWS 1
#elif defined(ANDROID) || defined(_ANDROID_)
#define PLATFORM_ANDROID 1
#include <jni.h>
#elif defined(__APPLE__)
// macro define of TARGET_OS_IPHONE, TARGET_OS_SIMULATOR, TARGET_CPU_ARM, TARGET_CPU_ARM64 etc
#include <TargetConditionals.h>
#if TARGET_OS_IPHONE || TARGET_OS_IOS
#define PLATFORM_IOS 1
#elif TARGET_OS_OSX
#define PLATFORM_MAC_OS 1
#endif
#elif defined(__linux__) || defined(__unix__) || defined(linux) || defined(__linux)
#define PLATFORM_LINUX     1
#else
#define PLATFORM_UNKNOWN 1
#endif

#if defined(_MSC_VER) && (_MSC_VER < 1600) // before Visual Studio 2010 have not <stdint.h>
#define STDINT_MISSING   1
#elif defined(__GNUC__)
#if !defined(GCC_VERSION)
#define GCC_VERSION (__GNUC__ * 10000 + __GNUC_MINOR__ * 100 + __GNUC_PATCHLEVEL__)
#endif
#if (GCC_VERSION < 40500) // test if GCC > 4.5.0 https://gcc.gnu.org/c99status.html
#define STDINT_MISSING   1
#endif
#else
#define STDINT_MISSING   0
#endif

#if !defined(STDINT_MISSING) || (STDINT_MISSING == 0)
#include <stdint.h>
#else
#endif

// EXPORT
#if defined(_WIN32) || defined(WIN32) || defined(_WIN64) || defined(WIN64)
#ifdef DL_INCEPTION_BEAUTY_LIB
#define DL_INCEPTION_BEAUTY_EXPORT __declspec(dllexport)
#define DL_INCEPTION_BEAUTY
#else
#ifdef IMPORT_DL_INCEPTION_BEAUTY_DYLIB
#define DL_INCEPTION_BEAUTY_EXPORT __declspec(dllimport)
#define DL_INCEPTION_BEAUTY extern
#else
#define DL_INCEPTION_BEAUTY_EXPORT
#define DL_INCEPTION_BEAUTY
#endif
#endif
#elif defined(_ADNROID_) || defined(ANDROID) || defined(__APPLE__) || defined(__linux__)
#ifdef DL_INCEPTION_BEAUTY_LIB
#define DL_INCEPTION_BEAUTY_EXPORT __attribute__((visibility("default")))
#define DL_INCEPTION_BEAUTY
#else
#define DL_INCEPTION_BEAUTY_EXPORT
#define DL_INCEPTION_BEAUTY extern
#endif
#else
#define DL_INCEPTION_BEAUTY_EXPORT
#define DL_INCEPTION_BEAUTY
#endif

//DL_INCEPTION_BEAUTY_EXPORT  std::string GetAIInceptionBeautyVersion();

#endif /* DL_FACE_OPTIMIZE_DEFINE_H__ */
