#ifndef MTAIENGINE_MTMODULEMACRO_H
#define MTAIENGINE_MTMODULEMACRO_H

#include <stdint.h>

#include <mtai/Common/MTAiEngineConfig.h>
// 导入导出宏定义 
#if defined(_WIN32) || defined(_WIN32_) || defined(WIN32) || defined(_WIN64_) || defined(WIN64) || defined(_WIN64)
#ifdef MTAIENGINE_DYLIB
#define MTAIENGINE_API __declspec(dllexport)
#else
#ifdef IMPORT_MTAIENGINE_DYLIB
#define MTAIENGINE_API __declspec(dllimport)
#else
#define MTAIENGINE_API
#endif
#endif
#elif defined(_ANDROID_) || defined(ANDROID) || defined(__APPLE__) || defined(__linux__) 
#if defined(MTAIENGINE_DYLIB) || defined(EXPORT_CPP_INTERFACE)
#define MTAIENGINE_API __attribute__((visibility("default")))
#else
#define MTAIENGINE_API
#endif
#else
#define MTAIENGINE_API
#endif

// 安全释放函数
#ifndef SAFE_DELETE
#define SAFE_DELETE(x) { if (x) delete (x); (x) = nullptr; }
#endif
#ifndef SAFE_DELETE_ARRAY
#define SAFE_DELETE_ARRAY(x) { if (x) delete [] (x); (x) = nullptr; }
#endif

//用于引用计数的原子操作
// exchange-add operation for atomic operations on reference counters
#if defined __INTEL_COMPILER && !(defined WIN32 || defined _WIN32)
// atomic increment on the linux version of the Intel(tm) compiler
#  define MTAIENGINE_XADD(addr, delta) (int)_InterlockedExchangeAdd(const_cast<void*>(reinterpret_cast<volatile void*>(addr)), delta)
#elif defined __GNUC__
#  if defined __clang__ && __clang_major__ >= 3 && !defined __ANDROID__ && !defined __EMSCRIPTEN__ && !defined(__CUDACC__)
#    ifdef __ATOMIC_ACQ_REL
#      define MTAIENGINE_XADD(addr, delta) __c11_atomic_fetch_add((_Atomic(int)*)(addr), delta, __ATOMIC_ACQ_REL)
#    else
#      define MTAIENGINE_XADD(addr, delta) __atomic_fetch_add((_Atomic(int)*)(addr), delta, 4)
#    endif
#  else
#    if defined __ATOMIC_ACQ_REL && !defined __clang__
// version for gcc >= 4.7
#      define MTAIENGINE_XADD(addr, delta) (int)__atomic_fetch_add((unsigned*)(addr), (unsigned)(delta), __ATOMIC_ACQ_REL)
#    else
#      define MTAIENGINE_XADD(addr, delta) (int)__sync_fetch_and_add((unsigned*)(addr), (unsigned)(delta))
#    endif
#  endif
#elif defined _MSC_VER && !defined RC_INVOKED
#  include <intrin.h>
#  define MTAIENGINE_XADD(addr, delta) (int)_InterlockedExchangeAdd((long volatile*)addr, delta)
#else
static inline void MTAIENGINE_XADD(int* addr, int delta) { int tmp = *addr; *addr += delta; return tmp; }
#endif

#ifndef NULL
#define NULL 0
#endif

#ifndef IN
#define IN
#endif

#ifndef OUT
#define OUT
#endif

#ifndef INOUT
#define INOUT
#endif

#ifndef NONULL
#define NONULL
#endif

#define MTAIENGINE_KEY(name)                   #name
#define MTAIENGINE(name)                       MTAIENGINE_KEY(MTAIENGINE_ ## name)
#define MTAIENGINE_MODEL(name)                 MTAIENGINE_KEY(MTAIENGINE_MODEL_ ## name)
#define MTAIENGINE_MERAK(name)                 MTAIENGINE_KEY(MTAIENGINE_MERAK_ ## name)
#define MODELKEY_ADD_COREML(name)              MTAIENGINE_KEY(name_ ## COREML)
#define MT_MASK(shift_num)               (uint64_t(0x01) << (shift_num))

#ifndef MTAI_MIN
#  define MTAI_MIN(a,b)  ((a) > (b) ? (b) : (a))
#endif

#ifndef MTAI_MAX
#  define MTAI_MAX(a,b)  ((a) < (b) ? (b) : (a))
#endif


#endif // MTAIENGINE_MTMODULEMACRO_H