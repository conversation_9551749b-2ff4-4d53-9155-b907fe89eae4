//
//  MTAiModelKitReflection.h
//  AiDispatchMoch
//
//  Created by 张凯阳 on 2021/9/5.
//
#pragma once

#include <mtai/Common/MTAiEngineDefine.h>
#include <mtai/Common/MTVector.h>
#include <mtai/Common/MTValueMap.h>
#include <string>

#define MTAIENGINE_ENV_QUALCOMM "qualcomm"

namespace mtai 
{
    //和manis、aidispatch的协议json字段名，务必同步
    const static std::string model_key = "modelPath";
    const static std::string strategy_key = "strategyPath";

    MTAIENGINE_API std::string GetAiDispatchModelPathForKey(const std::string& keyString);

    MTAIENGINE_API MTVector<std::string> GetAiDispatchConfigPaths(const std::string& dirStr);

    MTAIENGINE_API MTValueMapNew<std::string> GetAiDispatchModelInfoForKey(const std::string& keyString);

    MTAIENGINE_API std::string GetAiDispatchXPUPathForKey(const std::string& keyString);

}
