#pragma once

#include <algorithm>
#include<mtai/MeituAiEngine.h>

#include <memory>
namespace mtai 
{

MTAiEngineImage ScaleAiImage(MTAiEngineImage src); 
float RollAngleFromOrientation(int orientation);

MTRect2f DefaultLeftFaceRect(int dst_orientation);
MTRect2f DefaultFrontFaceRect(int dst_orientation);
MTRect2f DefaultRightFaceRect(int dst_orientation);
MTRect2f DefaultLeftChinFaceRect(int dst_orientation);
MTRect2f DefaultRightChinFaceRect(int dst_orientation);

bool CalcFaceRectForFacePoints(MTVector<MTPoint2f> &points, int width, int height, MTRect2f &rect);

}
