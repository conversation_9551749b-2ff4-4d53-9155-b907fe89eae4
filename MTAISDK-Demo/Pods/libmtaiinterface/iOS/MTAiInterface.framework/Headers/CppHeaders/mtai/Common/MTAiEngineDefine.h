//
// Created by IF on 2019/2/12.
//

#ifndef MTAIENGINE_MTMODULEDEFINE_H
#define MTAIENGINE_MTMODULEDEFINE_H

#include "MTAiEngineMacro.h"

namespace mtai
{

    enum MTAiEngineRet {
        MTAiEngineRet_Failure = -1,
        MTAiEngineRet_Success = 0,
    };

    enum MTGesture
    {
        MTGestureNone               = 0,    ///< 未检测
        MTGesturePalm               = 1,    ///< 手掌
        MTGestureThumb              = 2,    ///< 大拇指
        MTGestureHoldFist           = 3,    ///< 抱拳
        MTGestureHoldHand           = 4,    ///< 托手
        MTGestureLove               = 5,    ///< 双手比心
        MTGestureLoveOneHand        = 6,    ///< 单手比心
        MTGestureOk                 = 7,    ///< OK
        MTGestureYeah               = 8,    ///< YEAH
        MTGestureGun                = 9,    ///< 手枪
        MTGestureFinger             = 10,   ///< 手指
        MTGestureFist               = 11,   ///< 拳头
        MTGesture666                = 12,   ///< 666
        MTGesturePrayer             = 13,   ///< 双手合十
        MTGestureILoveU             = 14,   ///< 我爱你(弯曲中指和无名指)
        MTGestureShoot              = 15,   ///< 正面开枪
    };

    enum class MTMakeup
    {
        IsMakeup        = 0,    // 有化妆
        kNoMakeup       = 1,    // 未化妆
        kUnkown         = 2,    // 无法判断
        // mouth color scope
        kMcsRed         = 10,   // 红色系  E60000(230,0,0),FF0000(255,0,0),FF2400(255,36,0),
                                //        8B0000(139,0,0),B22222(178,34,34)
        kMcsOrange      = 11,   // 橘色系  FF4500(255,69,0),FF4D00(255,77,0),FF8033(255,128,51),
                                //        FFA500(255,165,0)
        kMcsBeanpaste   = 12,   // 豆沙系  CD5C5C(205,92,92),F08080(240,128,128),FFA500(255,165,0),
        kMcsPink        = 13,   // 粉色系  FFC0CB(255,192,203),FFB6C1(255,182,193)
        kMcsRaspberry   = 14,   // 梅子色系  CC0080(204,0,128),C71585(199,21,133),DE3163(222,49,99),
                                //          E63995(230,57,149),DDA0DD(221,160,221)
        kMcsBlack       = 15,   // 暗黑色系  00477D(0,71,125),4682B4(70,130,180),CCCCFF(204,204,255),
                                //          B399FF(179,153,255),8B00FF(139,0,255),7400A1(116,0,161),
                                //          800080(128,0,128),E6E6FA(230,230,250)
        kMcsOther       = 16,
        // mouth color scope end
    };

    enum MTChildAge {
        MTChildAgeNone        = -1,   ///< 未检测
        MTChildAgeNo          = 0,    ///< 非儿童
        MTChildAgeYes         = 1,    ///< 儿童
    };

    enum MTTeenagerAge {
        MTTeenagerAgeNone        = -1,   ///< 未检测
        MTTeenagerAgeNo          = 0,    ///< 非青少年
        MTTeenagerAgeYes         = 1,    ///< 青少年
    };

    enum MTGender {
        MTGenderNone        = -1,   ///< 未检测
        MTGenderFemale      = 0,    ///< 女
        MTGenderMale        = 1,    ///< 男
    };

    enum MTRace {
        MTRaceNone          = -1,   ///< 未检测
        MTRaceWhite         = 0,    ///< 白种人
        MTRaceYellow        = 1,    ///< 黄种人
        MTRaceBlack         = 2,    ///< 黑种人
        MTRaceIndiaNorth    = 3,    ///<印度北方人
        MTRaceIndiaSouth    = 4,    ///<印度南方人
        MTRaceSouthestAsia  = 5,    ///<东南亚人
    };

    enum MTEmotion {
        MTEmotionNone       = -1,   ///< 未检测
        MTEmotionSad        = 0,    ///< 伤心
        MTEmotionNeutral    = 1,    ///< 平静
        MTEmotionSmile      = 2,    ///< 微笑
        MTEmotionLaugh      = 3,    ///< 大笑
        MTEmotionSurprise   = 4,    ///< 惊讶
        MTEmotionFear       = 5,    ///< 恐惧
        MTEmotionAngry      = 6,    ///< 愤怒
        MTEmotionDisgust    = 7,    ///< 厌恶
    };

    struct MTGlasses {

        enum Type {
            TypeNone          = -1,   ///< 未检测
            TypeNoGlass       = 0,    ///< 无眼镜
            TypeNormal        = 1,    ///< 普通眼镜
            TypeSunglasses    = 2,    ///< 太阳镜
        };

        enum Shape {
            ShapeNone          = -1,   ///< 未检测
            ShapeNoGlass       = 0,    ///< 无眼镜
            ShapeOtherShapes   = 1,    ///< 其它形状
            ShapeSquare        = 2,    ///< 方形
            ShapeCircle        = 3,    ///< 圆形
        };

        enum Frame {
            FrameNone          = -1,   ///< 未检测
            FrameNoGlass       = 0,    ///< 无眼镜
            FrameNoFrame       = 1,    ///< 无边框
            FrameFullFrame     = 2,    ///< 全框
            FrameHalfFrame     = 3,    ///< 半框
        };

        enum Thickness {
            ThicknessNone       = -1,   ///< 未检测
            ThicknessNoGlass    = 0,    ///< 无眼镜
            ThicknessThin       = 1,    ///< 细
            ThicknessThick      = 2,    ///< 粗
        };

        enum Size {
            SizeNone           = -1,   ///< 未检测
            SizeNoGlass        = 0,    ///< 无眼镜
            SizeLarge          = 1,    ///< 大
            SizeSmall          = 2,    ///< 小
        };

        Type        type       = Type::TypeNone;
        Shape       shape      = Shape::ShapeNone;
        Frame       frame      = Frame::FrameNone;
        Thickness   thickness  = Thickness::ThicknessNone;
        Size        size       = Size::SizeNone;
    };

    struct MTEyelid {

        enum Type {
            MTEyelidNone          = -1,   ///< 未检测
            MTEyelidSingle        = 0,    ///< 单眼皮
            MTEyelidDouble        = 1,    ///< 双眼皮
            MTEyelidDoubleInside  = 2,    ///< 内双眼皮
        };

        Type left    =   MTEyelidNone;
        Type right   =   MTEyelidNone;
    };

    struct MTMustache {

        enum Type {
            TypeNone                = -1,    ///< 未检测
            TypeNoMustache          = 0,    ///< 无胡须
            TypeStubble             = 1,    ///< 胡渣
            TypeHaveMustache        = 2,    ///< 胡须
        };

        enum Length {
            LengthNone              = -1,   ///< 未检测
            LengthNoMustache        = 0,    ///< 无胡须/胡渣
            LengthShort             = 1,    ///< 短胡
            LengthMiddle            = 2,    ///< 中胡
            LengthLong              = 3,    ///< 长胡
        };

        enum Shape {
            ShapeNone               = -1,   ///< 未检测
            ShapeNoMustache         = 0,    ///< 无胡须/胡渣
            ShapeHalfGoatee         = 1,    ///< 半山羊
            ShapeFullGoatee         = 2,    ///< 全山羊
            ShapePencilThin         = 3,    ///< 八字胡
            ShapeFullBeard          = 4,    ///< 全胡须
            ShapeWhisker            = 5,    ///< 络腮胡
        };

        enum Thickness {
            ThicknessNone           = -1,   ///< 未检测
            ThicknessNoMustache     = 0,    ///< 无胡须/胡渣
            ThicknessThin           = 1,    ///< 稀疏
            ThicknessThick          = 2,    ///< 浓密
        };

        Type        type        = Type::TypeNone;
        Length      length      = Length::LengthNone;
        Shape       shape       = Shape::ShapeNone;
        Thickness   thickness   = Thickness::ThicknessNone;
    };

    enum MTCheek {
        MTCheekNone                 = -1,   ///< 未检测
        MTCheekHigh                 = 0,    ///< 高颧骨
        MTCheekFlat                 = 1,    ///< 平颧骨
    };

    enum MTJaw {
        MTJawNone                   = -1,   ///< 未检测
        MTJawSquare                 = 0,    ///< 方下巴
        MTJawSharp                  = 1,    ///< 尖下巴
        MTJawRound                  = 2,    ///< 圆下巴
    };

    struct MTEyebrow {

        enum Type {
            MTEyebrowNone                = -1,   ///< 未检测
            MTEyebrowLineBrow            = 0,   ///< 直眉 aa01
            MTEyebrowNearLineBrowTail    = 1,    ///< 偏直眉-眉尾下垂 aa02
            MTEyebrowNearLineBrowHead    = 2,    ///< 偏直眉-眉头下垂 aa03
            MTEyebrowNearLineBrow        = 3,    ///< 偏直眉 aa04
            MTEyebrowArrowBrow           = 4,    ///< 剑眉 aa05
            MTEyebrowNearArrowBrow       = 5,    ///< 偏剑眉 aa06
            MTEyebrowNearEightBrow       = 6,    ///< 偏八字眉 aa07
            MTEyebrowEightBrow           = 7,    ///< 八字眉 aa08
            MTEyebrowTail                = 8,    ///< 高挑眉 aa09
            MTEyebrowNearTail            = 9,    ///< 偏高挑眉 aa10
            MTEyebrowBended              = 10,    ///< 弯眉 aa11
            MTEyebrowNearBended          = 11,    ///< 偏弯眉 aa12
            MTEyebrowNearBendedHead      = 12,    ///< 偏弯眉-眉头下垂 aa13
            MTEyebrowNearBendedTail      = 13,    ///< 偏直眉-眉尾下垂 aa14
        };

        enum Thickness {
            MTThicknessNone              = -1,   ///< 未检测
            MTThicknessNearLight         = 0,    ///< 浓度-偏淡 ab01
            MTThicknessVeryLight         = 1,    ///< 浓度-非常淡 ab02
            MTThicknessNormal            = 2,    ///< 浓度-浓密正好 ab03
            MTThicknessNearDark          = 3,    ///< 浓度-偏浓 ab04
        };

        enum Distributed {
            MTDistributedNone            = -1,   ///< 未检测
            MTDistributedEvenly          = 0,    ///< 浓密分布-均匀 ac01
            MTDistributedBack            = 1,    ///< 浓密分布-前疏后密 ac02
            MTDistributedFront           = 2,    ///< 浓密分布-前密后疏 ac03
            MTDistributedNearEvenly      = 3,    ///< 浓密分布-偏均匀 ac04
        };
        enum Spacing {
            MTSpacingNone          = -1,   ///< 未检测
            MTSpacingWide          = 0,    ///< 两眉间距-宽 ae01
            MTSpacingNarrow        = 1,    ///< 两眉间距-窄 ae02
            MTSpacingNormal        = 2,    ///< 两眉间距-适当 ae03
        };

        Type type    =   MTEyebrowNone;
        Thickness thick   =   MTThicknessNone;
        Distributed distribute = MTDistributedNone;
        Spacing spacing = MTSpacingNone;

        char typeCode[5] = {'\0'};
        char thickCode[5] = {'\0'};
        char distributeCode[5] = {'\0'};
        char spacingCode[5] = {'\0'};
    };

    struct MTEye {

        enum Spacing {
            MTSpacingNone          = -1,   ///< 未检测
            MTSpacingWide          = 0,    ///< 两眼间距-宽 ad01
            MTSpacingNarrow        = 1,    ///< 两眼间距-窄 ad02
            MTSpacingNormal        = 2,    ///< 两眼间距-适当 ad03
        };
        enum Area {
            MTAreaNone          = -1,   ///< 未检测 
            MTAreaNormal        = 0,    ///< 标准眼 ba01
            MTAreaSmaller       = 1,    ///< 偏小 ba02
            MTAreaTooBig        = 2,    ///< 大 ba03
            MTAreaClose         = 3,    ///< 闭眼 ba04
            MTAreaSmall         = 4,    ///< 小眼 ba05
        };
        
        Spacing spacing    =   MTSpacingNone;
        Area area   =   MTAreaNone;

        char spacingCode[5] = {'\0'};
        char areaCode[5] = {'\0'};
    };

    enum MTNoseWing {
        MTNoseNone          = -1,   ///< 未检测
        MTNoseNarrow        = 0,    ///< 鼻子-窄 ca01
        MTNoseNormal        = 1,    ///< 鼻子-标准 ca02
        MTNoseWideSide      = 2,    ///< 鼻子-偏宽 ca03
        MTNoseWide          = 3,    ///< 鼻子-宽 ca04
    };

    struct MTLip {

        enum Thickness {
            MTThicknessNone                = -1,   ///< 未检测
            MTThicknessNormal              = 0,    ///< 唇形-标准 da01
            MTThicknessPartialThickness    = 1,    ///< 唇形-偏厚 da02
            MTThicknessthickness           = 2,    ///< 唇形-厚 da03
            MTThicknessNearThin            = 3,    ///< 唇形-偏薄 da04
            MTThicknessThin                = 4,    ///< 唇形-薄 da05
        };

        enum Peak {
            MTPeakNone          = -1,   ///< 未检测
            MTPeakHave          = 0,    ///< 唇峰-有 db01
            MTPeakNo            = 1,    ///< 唇峰-无 db02
            MTPeakNear          = 2,    ///< 唇峰-有唇峰，不明显 db03
        };
        
        Thickness thick   =   MTThicknessNone;
        Peak peak    =   MTPeakNone;

        char thickCode[5] = {'\0'};
        char peakCode[5] = {'\0'};
    };

    enum MTFaceType {
        MTFaceTypeNone                = -1,   ///< 未检测
        MTFaceTypeNearTriangle        = 0,    ///< 偏倒三角 ga01
        MTFaceTypeTriangle            = 1,    ///< 倒三角 ga02
        MTFaceTypeNearEllipse         = 2,    ///< 偏椭圆 ga03
        MTFaceTypeEllipse             = 3,    ///< 椭圆 ga04
        MTFaceTypeNearSquare          = 4,    ///< 偏方 ga05
        MTFaceTypeSquare              = 5,    ///< 方 ga06
        MTFaceTypeRound               = 6,    ///< 圆脸 ga07
        MTFaceTypeNearRound           = 7,    ///< 偏圆脸 ga08
        MTFaceTypeLong                = 8,    ///< 长脸 ga09
        MTFaceTypeNearLong            = 9,    ///< 偏长脸 ga10
        MTFaceTypePrism               = 10,    ///< 棱形脸 ga11
    };

    enum MTFaceEyeBag {
        MTFaceEyeBagNone               = -1,   ///< 未检测
        MTFaceEyeBagNo                 = 0,    ///< 无眼袋
        MTFaceEyeBagHave               = 1,    ///< 有眼袋
    };

    enum MTFaceTemple {
        MTFaceTempleNone                = -1,   ///< 未检测
        MTFaceTempleSeg                 = 0,    ///< 凹太阳穴
        MTFaceTempleFull                = 1,    ///< 太阳穴饱满
    };

    enum MTFaceRisorius {
        MTFaceRisoriusNone                = -1,   ///< 未检测
        MTFaceRisoriusNo                  = 0,    ///< 无苹果肌
        MTFaceRisoriusHave                = 1,    ///< 有苹果肌
    };

    enum MTAnimalLabel {
        MTAnimalLabelNone           = 0,    ///< 未检测
        MTAnimalLabelCat            = 1,    ///< 猫
        MTAnimalLabelDog            = 2,    ///< 狗
    };

    enum MTInputDataFormat {
        MTInputDataFormat_RGBA = 0,
        MTInputDataFormat_RGBA_O1, //orientation方向参数必须为1
        MTInputDataFormat_GRAY,
        MTInputDataFormat_GRAY_O1, //orientation方向参数必须为1
        MTInputDataFormat_MAX,
    };
    
    enum MTPandaEyeType {
        MTEyeBloodVessel            = 0,
        MTEyeShadow                 = 1,
        MTEyePigment                = 2,
        MTEyeNoneShadow             = 3,
    };

    enum MTPandaEyeLevel
    {
        MTPandaEyeLevelNone = 0,
        MTPandaEyeLevelMild = 1,
        MTPandaEyeLevelModerate = 2,
        MTPandaEyeSerious = 3,
    };

    enum MTFlawType
    {
        MTFlawHealth = 0,
        MTFlawSplash = 1,
        MTFlawNevus = 2,
        MTFlawAcne = 3,
        MTFlawAcneMark = 4,
    };

    enum MTFoodType
    {
        MTFoodNone = 0,
        MTFoodOther,
        MTFoodBottle, 
        MTFoodCan, 
        MTFoodCoffee, 
        MTFoodGlass, 
        MTFoodGoblet, 
        MTFoodBread,
        MTFoodEggTart, 
        MTFoodCake, 
        MTFoodMacarons, 
        MTFoodIceCream, 
        MTFoodIcePop, 
        MTFoodAnanas,
        MTFoodApple, 
        MTFoodBanana, 
        MTFoodDurian, 
        MTFoodMango, 
        MTFoodOrange, 
        MTFoodPear,
        MTFoodWatermelon, 
        MTFoodGrape, 
        MTFoodTomato, 
        MTFoodBroccoli, 
        MTFoodCarrot, 
        MTFoodCucumber,
        MTFoodCabbage, 
        MTFoodRawMeat, 
        MTFoodChip, 
        MTFoodHamburger, 
        MTFoodPizza, 
        MTFoodChips,
        MTFoodPopcorn, 
        MTFoodCookie, 
        MTFoodLightHomeDish, 
        MTFoodLightFish, 
        MTFoodJapaneseFood, 
        MTFoodRice,
        MTFoodFriedRice, 
        MTFoodHotpot, 
        MTFoodBarbecue, 
        MTFoodRoastDuck, 
        MTFoodRoastFish, 
        MTFoodHotHomeDish,
        MTFoodHotFish, 
        MTFoodKoreanFood, 
        MTFoodShellfish, 
        MTFoodShrimp, 
        MTFoodSteak, 
        MTFoodSnail,
        MTFoodWesternSoup, 
        MTFoodBakedRice, 
        MTFoodSalad, 
        MTFoodCheeze, 
        MTFoodNotSure,
    };

    enum MTOrnamentType {
        MTOrnamentNone        = 0, ///< 标签：未检测                                 
        MTOrnamentEar         = 1, ///< 标签：耳部
        MTOrnamentHead        = 2, ///< 标签：头部
        MTOrnamentNeck        = 3, ///< 标签：脖子
        MTOrnamentWrist       = 4, ///< 标签：腕部
        MTOrnamentNail        = 5, ///< 标签：美甲
        MTOrnamentFingerring  = 6, ///< 标签：戒指
    };

    enum MTColorType {
        MTColorTypeNone = 0,       //无颜色上报
        MTColorTypeBlack = 1,      //黑色
        MTColorTypeAsk = 2,        //灰色
        MTColorTypeWhite = 3,      //白色
        MTColorTypeDarkRed = 4,    //暗红色
        MTColorTypePink = 5,       //粉色
        MTColorTypeBrightRed = 6,  //鲜红色
        MTColorTypeBrown = 7,      //棕色
        MTColorTypeOrange = 8,     //橙色
        MTColorTypeYellow = 9,     //黄色
        MTColorTypeGreen = 10,     //绿色
        MTColorTypeCyan = 11,      //青色
        MTColorTypeBlue = 12,      //蓝色
        MTColorTypePurle = 13,     //紫色
        MTColorTypeOther = 14,     //其他/未知
    };

    template <typename Dtype>
    struct MTAIENGINE_API MTRect_
    {
            MTRect_()
            {
            }

            MTRect_(Dtype xx, Dtype yy, Dtype ww, Dtype hh) : x(xx), y(yy), width(ww), height(hh)
            {

            }

            Dtype area()
            {
                return width * height;
            }

            Dtype x;
            Dtype y;
            Dtype width;
            Dtype height;
    };

    typedef MTRect_<float> MTRect2f;
    typedef	MTRect_<int> MTRect2i;

    template <typename Dtype>
    struct MTAIENGINE_API MTSize_
    {
        MTSize_() { }

        MTSize_(Dtype w, Dtype h) : width(w), height(h) { }

        Dtype width;
        Dtype height;
    };

    typedef MTSize_<float> MTSize2f;
    typedef MTSize_<int> MTSize2i;

    template <typename Dtype>
    struct MTAIENGINE_API MTPoint_
    {
        MTPoint_() {}
        MTPoint_(Dtype xx, Dtype yy) : x(xx), y(yy) {}

        Dtype x;
        Dtype y;
    };

    typedef MTPoint_<float> MTPoint2f;
    typedef MTPoint_<int>	MTPoint2i;

    template <typename Dtype>
    struct MTAIENGINE_API MTPoint3_
    {
        MTPoint3_() {}
        MTPoint3_(Dtype xx, Dtype yy, Dtype zz) : x(xx), y(yy) , z(zz){}

        Dtype x;
        Dtype y;
        Dtype z;
    };

    typedef MTPoint3_<float> MTPoint3f;
    typedef MTPoint3_<int>	MTPoint3i;


}

#endif //MTAIENGINE_MTMODULEDEFINE_H
