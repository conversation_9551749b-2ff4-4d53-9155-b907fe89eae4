#ifndef _MT_SUB_INCEPTION_BEAUTY_UTIl_H_
#define _MT_SUB_INCEPTION_BEAUTY_UTIl_H_

#ifndef GLuint
typedef unsigned int GLuint;
#endif

#ifndef GLint
typedef int GLint;
#endif

#ifndef GLenum
typedef unsigned int GLenum;
#endif

#include <mtai/Common/MTAiEngineDefine.h>

extern "C" {

    typedef struct mtlabai_sub_inception_beauty_handle_t mtlabai_sub_inception_beauty_handle_t;

    enum mtlabai_sub_inception_beauty_proc_type
    {
        MTLAB_SUB_IB_BALANCE_SKIN = 8,
        MTLAB_SUB_IB_BALANCE_SKIN_PHOTO = 9,
        MTLAB_SUB_IB_BALANCE_SKIN_REALTIME = 10,
        MTLAB_SUB_IB_BALANCE_SKIN_BEST = 11,
        MTLAB_SUB_IB_BALANCE_SKIN_FAST = 12,
        MTLAB_SUB_IB_RESTORE_FACE = 16
    };
    
    enum mtlabai_sub_inception_beauty_device_type
    {
        MTLAB_SUB_IB_DEVICE_CPU = 0,
        MTLAB_SUB_IB_DEVICE_CUDA,
        MTLAB_SUB_IB_DEVICE_GL = 8,                      //模型前向和其他流程都跑gl3.0
        MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU,                //模型前向跑cpu，其他流程跑gl2.0或gl3.0
        MTLAB_SUB_IB_DEVICE_GL_MODEL_NPU,                //模型前向跑npu，其他流程跑gl2.0或gl3.0，只适用于华为npu
        MTLAB_SUB_IB_DEVICE_GL_MODEL_CL,                 //模型前向跑cl，其他流程跑gl2.0或gl3.0，只适用于高通芯片安卓机
        MTLAB_SUB_IB_DEVICE_GL_MODEL_COREML,             //模型前向跑coreML，其他流程根据不同调用函数选择跑gl2.0或gl3.0，只适用于ios 14.0以上系统
    };

    MTAIENGINE_API mtlabai_sub_inception_beauty_handle_t *mtlabai_sub_inception_beauty_create_handle();
    MTAIENGINE_API void mtlabai_sub_inception_beauty_release(mtlabai_sub_inception_beauty_handle_t **handle);
    
    MTAIENGINE_API void mtlabai_sub_inception_beauty_init(mtlabai_sub_inception_beauty_handle_t *handle);
            
    /*
    @param pModelPath:      模型路径
    @param nProcType:       处理类型:                      对应模型：
                            MTLAB_SUB_IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                            MTLAB_SUB_IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                            MTLAB_SUB_IB_BALANCE_SKIN_BEST      ->  snoopy_best
    @param nDeviceType:     设备类型:
                            MTLAB_SUB_IB_DEVICE_CPU
                            MTLAB_SUB_IB_DEVICE_CUDA
                            MTLAB_SUB_IB_DEVICE_GL(该函数禁用gl)
                            MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU(该函数禁用gl)
    */
    MTAIENGINE_API bool mtlabai_sub_inception_beauty_load_models(mtlabai_sub_inception_beauty_handle_t *handle, const char* pModelPath,
                            const int nProcType,
                            const int nDeviceType);
    /*
    @param pModelPath:      模型路径
    @param bPath2File       true:通过pModelPath可以定位到具体的模型文件，此时nProcType将失效。
                            false: pModelPath只定位到模型所在文件夹，需通过nProcType指定模型文件
    @param nProcType:       处理类型:                      对应模型：
                            MTLAB_SUB_IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                            MTLAB_SUB_IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                            MTLAB_SUB_IB_BALANCE_SKIN_BEST      ->  snoopy_best
    @param nDeviceType:     设备类型:
                            MTLAB_SUB_IB_DEVICE_CPU
                            MTLAB_SUB_IB_DEVICE_CUDA
                            MTLAB_SUB_IB_DEVICE_GL(该函数禁用gl)
                            MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU(该函数禁用gl)
    */
    MTAIENGINE_API bool mtlabai_sub_inception_beauty_load_models_judge(mtlabai_sub_inception_beauty_handle_t *handle, const char* pModelPath,
                            const bool bPath2File,
                            const int nProcType,
                            const int nDeviceType);
    
    
    /*
    @param pModelData:      模型数据流
    @param nModelDataSize:  模型数据流尺寸
    @param nProcType:       处理类型:                      对应模型：
                            MTLAB_SUB_IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                            MTLAB_SUB_IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                            MTLAB_SUB_IB_BALANCE_SKIN_BEST      ->  snoopy_best
    @param nDeviceType:     设备类型:
                            MTLAB_SUB_IB_DEVICE_CPU
                            MTLAB_SUB_IB_DEVICE_CUDA
                            MTLAB_SUB_IB_DEVICE_GL(该函数禁用gl)
                            MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU(该函数禁用gl)
    */
    MTAIENGINE_API bool mtlabai_sub_inception_beauty_load_models_data(mtlabai_sub_inception_beauty_handle_t *handle, const char* pModelData, const long lModelDataSize,
                                const int nProcType, const int nDeviceType);

    /*
    @param key:             模型key
    @param nProcType:       处理类型:                      对应模型：
                            MTLAB_SUB_IB_BALANCE_SKIN_PHOTO     ->  snoopy_ph
                            MTLAB_SUB_IB_BALANCE_SKIN_REALTIME  ->  snoopy_rt
                            MTLAB_SUB_IB_BALANCE_SKIN_BEST      ->  snoopy_best
    @param nDeviceType:     设备类型:
                            MTLAB_SUB_IB_DEVICE_CPU
                            MTLAB_SUB_IB_DEVICE_CUDA
                            MTLAB_SUB_IB_DEVICE_GL(该函数禁用gl)
                            MTLAB_SUB_IB_DEVICE_GL_MODEL_CPU(该函数禁用gl)
    */

    MTAIENGINE_API bool mtlabai_sub_inception_beauty_load_models_AiDispatch(mtlabai_sub_inception_beauty_handle_t *handle, const char* key,
                                const int nProcType, const int nDeviceType);
    
    MTAIENGINE_API void mtlabai_sub_inception_beauty_set_high_pass_flag(mtlabai_sub_inception_beauty_handle_t *handle, const int nHighPassFlag);
    
    /*
    @brief: 调用函数，模型前向和其他流程都跑cpu
    @param pImage:       输入图(rgba格式）
    @param nWidth:       输入图宽
    @param nHeight:      输入图高
    @param pfFacePoints: 输入图人脸点
    @param nFace:        输入图人脸数
    @param nFacePoints:  输入单个人脸点个数
    */
    MTAIENGINE_API void mtlabai_sub_inception_beauty_run(mtlabai_sub_inception_beauty_handle_t *handle, unsigned char* pImage, const int nWidth, const int nHeight,
                        const float* pfFacePoints, const int nFace, const int nFacePoints);

}

#endif