#ifndef _MT_SUB_FACE_FOR_PATH_
#define _MT_SUB_FACE_FOR_PATH_



#include <mtai/Common/MTAiEngineDefine.h>
#include <mtai/MTFaceModule/MTFaceResult.h>
#include <mtai/Common/MTAiEngineLog.h>
#include <mtai/MTFaceModule/MTFaceModuleOption.h>

extern "C" {
    typedef struct mtlabai_sub_face_analysis_handle_t mtlabai_sub_face_analysis_handle_t;
    typedef struct mtlabai_sub_face_analysis_result_t mtlabai_sub_face_analysis_result_t;

    MTAIENGINE_API typedef void (*mtlabai_sub_face_analysis_analysis_cb)(const mtai::MTAiEngineResult *result, mtai::MTAiEngineEnableOption *option, void *cb);

    enum mtlabai_sub_face_analysis_type {
        MTLABAI_SUB_FACE_TYPE_VIDEO = 0,
        MTLABAI_SUB_FACE_TYPE_IMAGE = 1,
    };

    /**
     * @description: 创建 handle
     * @param {in} path：路径
     * @param {in} type：导入的类型：视频：0， 图片：1
     * @return handle
     */
    MTAIENGINE_API mtlabai_sub_face_analysis_handle_t *
    mtlabai_sub_face_analysis_handle_create(const char *path, int type, bool enable_GL);

    /**
     * @description: 销毁 handle
     * @param {in} handle
     * @return void
     */
    MTAIENGINE_API void
    mtlabai_sub_face_analysis_handle_release(mtlabai_sub_face_analysis_handle_t **handle);


    /**
     * @description: 创建ai引擎
     * @param {in} handle
     * @param {in} model_path：模型路径
     * @param {in} assetManager：安卓平台的资源管理器
     * @param {in} mode: 注册时选择加载模型模式
     * @param {in} device_type: 注册时选择设备级别类型
     * @return 检测结果
     */
    MTAIENGINE_API const mtai::MTAiEngineResult *
    mtlabai_sub_face_analysis_run(mtlabai_sub_face_analysis_handle_t *handle, mtai::MTFaceModuleOption *faceOption, const char *model_path, void *context, mtlabai_sub_face_analysis_analysis_cb fn, void *cb);
    
    /**
     * @description: 获取人脸option
     * @param {in} handle
     * @return face option
     */
    MTAIENGINE_API mtai::MTFaceModuleOption *
    mtlabai_sub_face_analysis_get_option(mtlabai_sub_face_analysis_handle_t *handle);

    /**
    * @description: 获取结果
    * @param {in} handle
    * @return 
    */
    MTAIENGINE_API const mtai::MTAiEngineResult *
    mtlabai_sub_face_analysis_get_result(mtlabai_sub_face_analysis_handle_t *handle);

    /**
    * @description: 获取结果
    * @param {in} handle
    * @return 
    */
    MTAIENGINE_API void
    mtlabai_sub_face_analysis_set_log_level(mtai::MTAI_LOG_LEVEL level);

    /**
    * @description: 设置起始位置
    * @param {in} handle
    * @param {in} time 开始时间, 毫秒:ms
    * @return bool
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_set_start_time(mtlabai_sub_face_analysis_handle_t *handle, int64_t time);

    /**
    * @description: 设置持续时间
    * @param {in} handle
    * @param {in} time 时长, 毫秒:ms
    * @return bool
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_set_duration_time(mtlabai_sub_face_analysis_handle_t *handle, int64_t time);

    /**
    * @description: 设置跳帧数
    * @param {in} handle
    * @param {in} skipFrame 跳帧数
    * @return bool
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_set_skip_frame(mtlabai_sub_face_analysis_handle_t *handle, int skipFrame);

    /**
    * @description: 设置gl
    * @param {in} handle
    * @param {in} flag: 0 -> 不使用GL，1 -> 使用GL；默认0
    * @return bool
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_set_GL(mtlabai_sub_face_analysis_handle_t *handle, int flag);

    /**
    * @description: 设置时间
    * @param {in} handle
    * @param {in} flag: 0 -> 不开启，1 -> 开启
    * @return bool
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_open_runtime(mtlabai_sub_face_analysis_handle_t *handle, int flag);

    /**
    * @description: 输出为像素图像相对于媒体文件尺寸的缩放比例
    * @param {in} handle
    * @param scale 范围(0,1]
    * @return bool
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_scale(mtlabai_sub_face_analysis_handle_t *handle, float scale);

    /**
    * @description: 只输出关键帧
    * @param {in} handle
    * @param {in} flag，0:关闭，1:开启 
    * @return bool
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_set_enable_decode_key_frame_only(mtlabai_sub_face_analysis_handle_t *handle, int flag);

    /**
    * @description: 获取关键帧总数
    * @param {in} handle
    * @return 总数
    */
    MTAIENGINE_API int
    mtlabai_sub_face_analysis_get_video_key_frame_number(mtlabai_sub_face_analysis_handle_t *handle);

    /**
    * @description: stop
    * @param {in} handle
    * @return 
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_stop(mtlabai_sub_face_analysis_handle_t *handle);

    /**
    * @description: 开启人脸统计
    * @param {in} handle
    * @return 
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_enable_face_count(mtlabai_sub_face_analysis_handle_t *handle, bool flag);


    /**
    * @description: 获取不同人脸个数
    * @param {in} handle
    * @return 
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_get_face_count(mtlabai_sub_face_analysis_handle_t *handle, int &count);

    /**
    * @description: 设置人脸相同匹配相识阈值
    * @param {in} handle
    * @return 
    */
    MTAIENGINE_API bool
    mtlabai_sub_face_analysis_set_similarity_threshold(mtlabai_sub_face_analysis_handle_t *handle, float similarity_threshold);


}


#endif