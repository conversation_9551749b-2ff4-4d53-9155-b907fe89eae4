//
//Created by Xiez<PERSON><PERSON> on 2021/07/29.
//
#pragma once
#include <string>
#include <mtai/Common/MTAiEngineOption.h>

namespace mtai
{

    enum MTAIKitEnableEnum : uint64_t{
        MT_AIKIT_ENABLE_NONE     = 0X0,           //无
        MT_AIKIT_ENABLE_AIKIT    = MT_MASK(0),    //额头
        MT_AIKIT_ENABLE_TIME     = MT_MASK(1),    //获取运行耗时
        MT_AIKIT_ENABLE_DEPEND_OUTSIDE_FACE  = MT_MASK(2),    //依赖外部传入的人脸点数据，要求归一化
        //other depend outside data
        //MT_AIKIT_ENABLE_DEPEND_OUTSIDE_BODY = MT_MASK(3),
    };

    class MTAIENGINE_API MTAIKitModuleOption : public MTAiEngineOption{

    public:
        MTAIKitModuleOption();
        MTAIKitModuleOption(const MTAIKitModuleOption &opt);

        // 单独设置某个开关
        void SetSigEnaOption(MTAIKitEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MTAIKitEnableEnum flag);//GetSingleEnableOptionStatus

        // 模块类型
        MTAiEngineType MuduleType() override;

        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

        int restore(cJSON *dict, const char *cfgPath, void *assertManager) override;

        MTAiEngineOption *Clone() const override;

        // std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;

        void SetProtoBinaryDirectory(const char* dir);

        void SetProtoBinary(const char* file);

        void SetModelPath(const char* file);

        inline const std::string GetProtoBinaryDirectory(){ return m_protoDir; };

        inline const std::string GetProtoBinary(){ return m_protoBin; };

        inline const std::string GetModelPath(){ return m_modelPath; };

        inline const bool GetPatchProtoFlag(){ return m_b_patch_proto; };

        std::string m_protoDir = "";
        
        std::string m_protoBin = "";

        std::string m_modelPath = "";

        std::string m_protoVersion = "0.0.0.1";

        bool m_b_patch_proto = false;
    
    private:
        
    };

    inline MTAiEngineType MTAIKitModuleOption::MuduleType() {return MTAiEngineType_AIKitModule;}
    inline void MTAIKitModuleOption::SetSigEnaOption(MTAIKitEnableEnum flag) { enable_option_ |= flag; };    //SetSingleEnableOption
    inline bool MTAIKitModuleOption::GetSigEnaOptionStatus(MTAIKitEnableEnum flag){ return ((enable_option_ & flag) == flag); };

}
