#ifndef _MTLIBAI_SUB_DL_ALGORITHM_UTIL_H_
#define _MTLIBAI_SUB_DL_ALGORITHM_UTIL_H_

#include <mtai/Common/MTAiEngineDefine.h>
#include "MTSubDLAlgorithmDefined.h"


extern "C" {

    typedef struct mtlabai_sub_DL_algorithm_handle_t mtlabai_sub_DL_algorithm_handle_t;
    
    MTAIENGINE_API mtlabai_sub_DL_algorithm_handle_t *mtlabai_sub_DL_algorithm_create_handle();
    
    MTAIENGINE_API void mtlabai_sub_DL_algorithm_release(mtlabai_sub_DL_algorithm_handle_t **handle);
    
    /*@brief 初始化*/
    MTAIENGINE_API void mtlabai_sub_DL_algorithm_init(mtlabai_sub_DL_algorithm_handle_t *handle);
    
    /*
     @brief加载模型
     @deviceType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_CPU
     @dataType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_STRING_DATA_TYPE_FLOAT
     @nModelProType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_RM_BEST
     */
    
    MTAIENGINE_API bool mtlabai_sub_DL_algorithm_load_model(mtlabai_sub_DL_algorithm_handle_t *handle, const char* pModelPath,
                           const mtlabai_sub_DL_algorithm_device_type deviceType,
                           const mtlabai_sub_DL_algorithm_data_type dataType,
                           const mtlabai_sub_DL_algorithm_proc_type nModelProType
                           );
   /*
    @brief 从内存加载模型
    @deviceType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_STRING_DEVICE_TYPE_CPU
    @dataType：如果使用默认，设置成 MTLABAI_SUB_DL_ALGORITHM_STRING_DATA_TYPE_FLOAT
    */
    MTAIENGINE_API bool mtlabai_sub_DL_algorithm_load_data_model(mtlabai_sub_DL_algorithm_handle_t *handle, const char* pModelData,
                               const long nModelSize,
                               const mtlabai_sub_DL_algorithm_device_type deviceType,
                               const mtlabai_sub_DL_algorithm_data_type dataType
                      );
   
   /*
    @brief  运行算法
    @param  pImage              [in/out]        输入图像数据
    @param  nWidth              [in]            图像宽
    @param  nHeight             [in]            图像高
    @param  pfFacePoints        [in]            人脸点，130个点，非归一化, x,y,...
    @param  nNumFacedth         [in]            人脸个数
    @param  nNumFacePoints      [in]            人脸点个数
    */
    MTAIENGINE_API bool mtlabai_sub_DL_algorithm_run(mtlabai_sub_DL_algorithm_handle_t *handle, unsigned char* pImage,
                     const int nWidth,
                     const int nHeight,
                     const float* pfFacePoints,
                     const int nNumFace,
                     const int nNumFacePoints);
}

#endif