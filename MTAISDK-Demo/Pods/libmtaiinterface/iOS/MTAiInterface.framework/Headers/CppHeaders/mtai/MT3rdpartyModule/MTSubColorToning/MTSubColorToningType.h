#ifndef _MTSUB_COLORTONING_TYPE_H_
#define _MTSUB_COLORTONING_TYPE_H_

#pragma once

#include <mtai/Common/MTAiEngineMacro.h>

#ifdef __cplusplus
extern "C"
#endif
{
    #ifndef GLuint
    typedef unsigned int GLuint;
    #endif

    #ifndef GLint
    typedef int GLint;
    #endif

    #ifndef GLenum
    typedef unsigned int GLenum;
    #endif


    enum MTSubColorToningEWStatus
    {
        CORLORTONING_STATUS_ERR = -1,   // 出错了
        CORLORTONING_STATUS_OK = 0,     // 成功
    };

    // 加载模型跑的计算数据类型精度
    enum MTSubColorToningDataType
    {
        CORLORTONING_DATA_TYPE_FLOAT = 0,
        CORLORTONING_DATA_TYPE_FLOAT16 = 1,
        CORLORTONING_DATA_TYPE_BFLOAT16 = 2,
        CORLORTONING_DATA_TYPE_INT8 = 3,
        CORLORTONING_DATA_TYPE_UINT8 = 4,
    };

    // 加载模型跑的计算模式
    enum MTSubColorToningDeviceType
    {
        CORLORTONING_DEVICE_CPU = 0,
        CORLORTONING_DEVICE_CPU_C4 = 1,
        CORLORTONING_DEVICE_OPENGL = 2,
        CORLORTONING_DEVICE_OPENCL = 3,
        CORLORTONING_DEVICE_CUDA = 4,
        CORLORTONING_DEVICE_HEXAGON = 5,
        CORLORTONING_DEVICE_METAL = 6,
        CORLORTONING_DEVICE_WEBGL = 7,
        CORLORTONING_DEVICE_GLCS = 8,
        CORLORTONING_DEVICE_HIAI_NPU = 9,
        CORLORTONING_DEVICE_COREML = 10,
        CORLORTONING_DEVICE_OPENVINO = 11,
        CORLORTONING_DEVICE_AUTO = 12,
    };

    // 智能一键同款
    enum MTSubColorToningEWOptionType
    {
        CORLORTONING_OVER_EXPOSURE_OPTION = 1,             // 过曝矫正  --> ultraman_p.bin
        CORLORTONING_UNDER_EXPOSURE_OPTION = 2,            // 欠曝矫正  --> ultraman_n.bin
        CORLORTONING_EXPOSURE_CLASSIFIER_OPTION = 4,       // 曝光分类  --> test.bin
        CORLORTONING_WHITE_BALANCE_OPTION = 8,             // 白平衡矫正 --> eva_p_cc.bin
        CORLORTONING_DL_CT_DEHAZE_OPTION = 16,             //  去雾     --> unicorn.bin，该模型当前只支持CORLORTONING_DEVICE_CPU_C4模式
        CORLORTONING_DL_CT_DEHAZE_V2_OPTION = 32,          //  去雾V2   --> ponyta.bin
    };

    /*@brief 智能一键同 程度参数*/
    typedef struct MTSubColorEWParam
    {
        int ai_exposure = 0;               // 智能曝光，数值范围[0, 100]， 默认100
        int ai_wb = 0;                     // 智能白平衡，数值范围[0, 100]， 默认100
        int ai_dehaze = 0;                 //智能去雾， 数值范围[0, 100]， 默认100
        int ai_dehaze_v2 = 0;              //智能去雾V2， 数值范围[0, 100]， 默认100
    }MTSubColorEWParam;

    typedef struct MTSubColorCR2Param{
        //曝光
        int exposure = 0; // -150-150
        //对比度
        int constrast = 0;
        //白色
        int whiteness = 0;
        //黑色
        int blackness = 0;
        //饱和度
        int saturability = 0;
        //自然饱和度
        int vibrance = 0;
        //高光
        int highlight = 0;
        //阴影
        int shadow = 0;
        // 清晰度
        int sharpness = 0; // 0-100
    }MTSubColorCR2Param;


    // AI调色专项参数
    typedef struct MTSubColorACParam
    {
        int exposure = 0;               // 曝光，数值范围[-100, 100]
        int natrue_saturation = 0;      // 自然饱和度，数值范围[-100, 100]
        int brilliance = 0;             // 鲜明度，数值范围[-100, 100]
        int hue = 0;                    // 色调，数值范围[-100, 100]
        int dehaze = 0;                 // 去朦胧，数值范围[-100, 100]
        MTSubColorCR2Param cr2Param;
    }MTSubColorACParam;

    // AI调色专项功能
    enum MTSubColorACOptionType
    {
        CORLORTONING_EXPOSURE_OPTION = 1,              // 曝光调整         -> psyduck.bin
        CORLORTONING_NATRUE_SATURATION_OPTION = 2,     // 自然饱和度调整    -> nidoqueen.bin
        CORLORTONING_BRILLIANCE_OPTION = 4,            // 鲜明度调整       -> golduck.bin
        CORLORTONING_HUE_OPTION_OPTION = 8,            // 色调调整         -> pngn.bin
        CORLORTONING_DEHAZE_OPTION = 16,               // 去朦胧调整       -> nidoking
    };


    MTAIENGINE_API const char* GetDeviceName(const MTSubColorToningDeviceType deviceType);
    MTAIENGINE_API const char* GetDataName(const MTSubColorToningDataType dataType);
}

#endif