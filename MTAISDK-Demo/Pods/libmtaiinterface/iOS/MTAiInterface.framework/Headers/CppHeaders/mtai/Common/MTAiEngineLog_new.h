#ifndef _MT_AIENGINE_LOG_NEW_H
#define _MT_AIENGINE_LOG_NEW_H
#ifndef __STDC_FORMAT_MACROS
#define __STDC_FORMAT_MACROS
#endif

#include <string>
#include <sstream>
#include "MTAiEngineType.h"
#include "MTAiEngineMacro.h"
#if defined(NEW_LOG_MESSAGE)

namespace mtai 
{
    void mtai_set_ai_log_level(MTAI_LOG_LEVEL log_level);
    void mtai_show_log_level(MTAI_LOG_LEVEL log_levelconst, char* host_name, void* mtai_ptr, const char* tag, const char* fmt, ...);
}

extern MTAIENGINE_API void (*g_log_call_back)(int level, const char *, const char *);
// 等级由低到高
#define LOGV(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_TRACE, "", nullptr, "", __VA_ARGS__);
#define LOGD(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, "", nullptr, "", __VA_ARGS__);
#define LOGI(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_INFO, "", nullptr, "", __VA_ARGS__);
#define LOGW(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_WARN, "", nullptr, "", __VA_ARGS__);
#define LOGE(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_ERROR, "", nullptr, "", __VA_ARGS__);
#define LOGF(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, "", nullptr, "", __VA_ARGS__);

// 等级由低到高，且有tag用于回调，需设置mtai_set_log_callback
#define N_LOG_T_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_TRACE, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_D_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_I_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_INFO, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_W_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_WARN, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_E_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_ERROR, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_C_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, "", nullptr, tag, __VA_ARGS__);

// 等级由低到高
#define  MTAI_SUB_LOGV(...)  mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_TRACE, "UnknownHost", nullptr, "mtai", __VA_ARGS__);
#define  MTAI_SUB_LOGD(...)  mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, "UnknownHost", nullptr, "mtai", __VA_ARGS__);
#define  MTAI_SUB_LOGI(...)  mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_INFO, "UnknownHost", nullptr, "mtai", __VA_ARGS__);
#define  MTAI_SUB_LOGW(...)  mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_WARN, "UnknownHost", nullptr, "mtai", __VA_ARGS__);
#define  MTAI_SUB_LOGE(...)  mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_ERROR, "UnknownHost", nullptr, "mtai", __VA_ARGS__);
#define  MTAI_SUB_LOGF(...)  mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, "UnknownHost", nullptr, "mtai", __VA_ARGS__);


// 等级由低到高
#define MTAI_LOGV(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_TRACE, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define MTAI_LOGD(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define MTAI_LOGI(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_INFO, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define MTAI_LOGW(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_WARN, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define MTAI_LOGE(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_ERROR, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define MTAI_LOGF(...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);

// 等级由低到高，且有tag用于回调，需设置mtai_set_log_callback
#define LOG_T_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_TRACE, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_D_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_I_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_INFO, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_W_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_WARN, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_E_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_ERROR, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_C_TAG(tag, ...) mtai::mtai_show_log_level(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);



///////////////////////////////////////////// 以下内容需要打印，请通过设置Pattern进行///////////////////////////////////////////////
// 等级由低到高，包含文件名等信息
#define N_LOG_MORE_T(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_TRACE, __FILE__, __LINE__, __FUNCTION__, "", nullptr, "", __VA_ARGS__);
#define N_LOG_MORE_D(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, __FILE__, __LINE__, __FUNCTION__, "", nullptr, "", __VA_ARGS__);
#define N_LOG_MORE_I(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_INFO, __FILE__, __LINE__, __FUNCTION__, "", nullptr, "", __VA_ARGS__);
#define N_LOG_MORE_W(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_WARN, __FILE__, __LINE__, __FUNCTION__, "", nullptr, "", __VA_ARGS__);
#define N_LOG_MORE_E(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_ERROR, __FILE__, __LINE__, __FUNCTION__, "", nullptr, "", __VA_ARGS__);
#define N_LOG_MORE_C(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, __FILE__, __LINE__, __FUNCTION__, "", nullptr, "", __VA_ARGS__);

// 等级由低到高，，包含文件名等信息，且有tag用于回调，需设置mtai_set_log_callback
#define N_LOG_MORE_T_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_TRACE, __FILE__, __LINE__, __FUNCTION__, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_MORE_D_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, __FILE__, __LINE__, __FUNCTION__, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_MORE_I_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_INFO, __FILE__, __LINE__, __FUNCTION__, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_MORE_W_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_WARN, __FILE__, __LINE__, __FUNCTION__, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_MORE_E_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_ERROR, __FILE__, __LINE__, __FUNCTION__, "", nullptr, tag, __VA_ARGS__);
#define N_LOG_MORE_C_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, __FILE__, __LINE__, __FUNCTION__, "", nullptr, tag, __VA_ARGS__);


// 等级由低到高，包含文件名等信息
#define LOG_MORE_T(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_TRACE, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define LOG_MORE_D(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define LOG_MORE_I(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_INFO, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define LOG_MORE_W(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_WARN, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define LOG_MORE_E(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_ERROR, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);
#define LOG_MORE_C(...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, "", __VA_ARGS__);

// 等级由低到高，，包含文件名等信息，且有tag用于回调，需设置mtai_set_log_callback
#define LOG_MORE_T_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_TRACE, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_MORE_D_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_DEBUG, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_MORE_I_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_INFO, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_MORE_W_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_WARN, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_MORE_E_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_ERROR, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);
#define LOG_MORE_C_TAG(tag, ...) mtai::mtai_show_log_levelMore(mtai::MTAI_NEW_LOG_LEVEL_CRITICAL, __FILE__, __LINE__, __FUNCTION__, m_pAM->host_name, m_pAM->mtai_ptr, tag, __VA_ARGS__);

#endif

#endif