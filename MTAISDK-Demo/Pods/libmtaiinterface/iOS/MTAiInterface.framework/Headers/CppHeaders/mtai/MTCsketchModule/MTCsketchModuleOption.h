#ifndef MTAIENGINE_MTCSKETCH_MODULE_OPTION_H
#define MTAIENGINE_MTCSKETCH_MODULE_OPTION_H

#include <mtai/Common/MTAiEngineOption.h>

namespace mtai
{
    enum MTCsketchEnableEnum : uint64_t{
        MTCSKETCH_ENABLE_NONE                                          = 0x0,        // 无
        MTCSKETCH_ENABLE_CSKETCH                                       = MT_MASK(0), // 开启线稿
        MT_CSKETCH_ENABLE_DEPEND_OUTSIDE_FACE                          = MT_MASK(1), // 设置人脸点由外部输入
        MT_CSKETCH_ENABLE_DEPEND_OUTSIDE_HAIR_MASK                     = MT_MASK(2), // 设置头发mask由外部输入
        MT_CSKETCH_ENABLE_DEPEND_OUTSIDE_HALFBODY_MASK                 = MT_MASK(3), // 设置半身mask由外部输入
        MT_CSKETCH_ENABLE_TIME                                          = MT_MASK(4), // 获取运行耗时
    };
    
    enum MTCsketchMode {
        MTCsketchMode_CPU    = 0,
        MTCsketchMode_METAL  = 1,
        MTCsketchMode_OPENCL = 2,
        MTCsketchMode_OPENGL = 3,
        MTCsketchMode_VULKAN = 4
    };

    class MTAIENGINE_API MTCsketchModuleOption : public MTAiEngineOption{

    public:
        
        // 单独设置某个开关
        void SetSigEnaOption(MTCsketchEnableEnum flag);//SetSingleEnableOption

        // 返回某个开关的状态，true--》开启状态   false--》关闭状态
        bool GetSigEnaOptionStatus(MTCsketchEnableEnum flag);//GetSingleEnableOptionStatus

        std::map<const char*, const char*> GetCurrentModelsName(MTAiEngineMode mtai_mode) override;
        
        /*
            不同任务对应编号。
            0 - 跑完整效果,保留五官
            1 - 跑完整效果，但扣去五官
        */
        void SetDoPicNoFace(int doPicNoFace);
        int GetDoPicNoFace();

        /* 
            不同任务对应编号。
            0 - 不提取路径
            1 - 提取路径
        */
        void SetDoContour(int doContour);
        int GetDoContour();
        
        /*
            头发路径稀疏系数 （值为0~1, 值越大代表最后保留的路径越多）
        */
        void SetHairSparseCoeffi(float hairSparseCoeffi);
        float GetHairSparseCoeffi();

        /*
            身体路径稀疏系数 （值为0~1, 值越大代表最后保留的路径越多）
        */
        void  SetBodySparseCoeffi(float bodySparseCoeffi);
        float GetBodySparseCoeffi();

        // 模块类型
        MTAiEngineType MuduleType() override;

        // 获取参数捕获并打包成json
        cJSON* GetParamsCapture() override;

        int mode = 0;
        int m_doPicNoFace = 0 ;
        int m_doContour = 0 ;
        float m_HairSparseCoeffi = 1.0f ;
        float m_BodySparseCoeffi = 1.0f ;

    };

    inline MTAiEngineType MTCsketchModuleOption::MuduleType() {return MTAiEngineType_CsketchModule;}
    inline void MTCsketchModuleOption::SetSigEnaOption(MTCsketchEnableEnum flag) { enable_option_ |= flag; };//SetSingleEnableOption
    inline bool MTCsketchModuleOption::GetSigEnaOptionStatus(MTCsketchEnableEnum flag){ return ((enable_option_ & flag) == flag); };
    // inline void MTCsketchModuleOption::SetMultiThread(bool isMultiThread) {isMultiThread = isMultiThread;}
    // inline bool MTCsketchModuleOption::GetMultiThread() {return isMultiThread_;}
    // inline void MTCsketchModuleOption::SetStyleType(int styleType) { m_styleType = styleType ; };
    // inline int MTCsketchModuleOption::GetStyleType(){ return m_styleType; };

    inline void MTCsketchModuleOption::SetDoPicNoFace(int doPicNoFace) { m_doPicNoFace = doPicNoFace ; };
    inline int  MTCsketchModuleOption::GetDoPicNoFace(){ return m_doPicNoFace; };

    inline void MTCsketchModuleOption::SetDoContour(int doContour) { m_doContour = doContour ; };
    inline int  MTCsketchModuleOption::GetDoContour(){ return m_doContour; };

    inline void  MTCsketchModuleOption::SetHairSparseCoeffi(float hairSparseCoeffi){m_HairSparseCoeffi = hairSparseCoeffi;};
    inline float MTCsketchModuleOption::GetHairSparseCoeffi(){ return m_HairSparseCoeffi; };

    inline void  MTCsketchModuleOption::SetBodySparseCoeffi(float bodySparseCoeffi){m_BodySparseCoeffi = bodySparseCoeffi;};
    inline float MTCsketchModuleOption::GetBodySparseCoeffi(){ return m_BodySparseCoeffi; };
   
}

#endif //MTAIENGINE_MTCsketch_MODULE_OPTION_H
