#ifndef _MTSUB_COLORACUTIL_GL_H_
#define _MTSUB_COLORACUTIL_GL_H_

#include <mtai/Common/MTAiEngineMacro.h>
#include <mtai/MT3rdpartyModule/MTSubColorToning/MTSubColorToningType.h>

#ifdef __cplusplus
extern "C"
#endif
{

// 色调专项
typedef struct mtlabai_sub_color_ac_gl_handle* mtlabai_sub_color_ac_gl_handle_t;

/**
 * \brief 第0步:创建底层SDK句柄
 * \return handle 算法句柄
 */
MTAIENGINE_API mtlabai_sub_color_ac_gl_handle_t mtlabai_sub_color_ac_gl_create_handle();

/**
 * \brief 第0步:底层对象初始化
 */
MTAIENGINE_API void mtlabai_sub_color_ac_gl_init(
    mtlabai_sub_color_ac_gl_handle_t handle);

/**
 * \brief 销毁底层SDK句柄,最后调用
 * \param[in] handle 算法句柄
 */
MTAIENGINE_API void mtlabai_sub_color_ac_gl_release_handle(
    mtlabai_sub_color_ac_gl_handle_t* handle);

/**
 * \brief 第一步:从路径加载模型
 * \param[in] handle 算法句柄
 * \param[in] modelPath 模型文件夹路径
 * \param[in] deviceType
 *设备类型，当前支持CORLORTONING_DEVICE_CPU、CORLORTONING_DEVICE_CPU_C4、CORLORTONING_DEVICE_COREML
 *CORLORTONING_DEVICE_AUTO:ios能跑CoreML优先跑CoreML；其他机型设置CORLORTONING_DEVICE_CPU_C4
 * \param[in] dataType 数据类型，当前仅支持CORLORTONING_DATA_TYPE_FLOAT
 * \param[in] optionType 模型类型 MTSubColorACOptionType，一次只能加载一个模型
 * \param[in] assetManager assetManager
 */
MTAIENGINE_API MTSubColorToningEWStatus mtlabai_sub_color_ac_gl_load_model(
    mtlabai_sub_color_ac_gl_handle_t handle, const char* modelPath,
    MTSubColorToningDeviceType deviceType, MTSubColorToningDataType dataType,
    MTSubColorACOptionType optionType, void* assetManager);

/**
 * \brief 第一步:从内存加载模型(注：CoreML模型无法通过内存加载)
 * \param[in] handle 算法句柄
 * \param[in] modelData 模型数据
 * \param[in] modelSize 模型大小
 * \param[in] deviceType
 *设备类型，当前支持CORLORTONING_DEVICE_CPU、CORLORTONING_DEVICE_CPU_C4、CORLORTONING_DEVICE_COREML
 *CORLORTONING_DEVICE_AUTO:ios能跑CoreML优先跑CoreML；其他机型设置CORLORTONING_DEVICE_CPU_C4
 * \param[in] dataType 数据类型，当前仅支持CORLORTONING_DATA_TYPE_FLOAT
 * \param[in] optionType 模型类型 MTSubColorACOptionType，一次只能加载一个模型
 */
MTAIENGINE_API MTSubColorToningEWStatus mtlabai_sub_color_ac_gl_load_data_model(
    mtlabai_sub_color_ac_gl_handle_t handle, const char* modelData, long modelSize,
    MTSubColorToningDeviceType deviceType, MTSubColorToningDataType dataType,
    MTSubColorACOptionType optionType);

/**
 * \brief 第一步:从天枢加载模型
 * \param[in] handle 算法句柄
 * \param[in] deviceType 设备类型，当前仅支持CORLORTONING_DEVICE_CPU
 * \param[in] deviceType
 *设备类型，当前支持CORLORTONING_DEVICE_CPU、CORLORTONING_DEVICE_CPU_C4、CORLORTONING_DEVICE_COREML
 *CORLORTONING_DEVICE_AUTO:ios能跑CoreML优先跑CoreML；其他机型设置CORLORTONING_DEVICE_CPU_C4
 * \param[in] optionType 模型类型 MTSubColorACOptionType，一次只能加载一个模型
 * \param[in] assetManager assetManager
 */
MTAIENGINE_API MTSubColorToningEWStatus mtlabai_sub_color_ac_gl_load_model_Aidispatch(
    mtlabai_sub_color_ac_gl_handle_t handle, MTSubColorToningDeviceType deviceType,
    MTSubColorToningDataType dataType, MTSubColorACOptionType optionType,
    void* assetManager);

/**
 * \brief 第二步:初始化算法(需要在GL环境下)
 * \param[in] handle 算法句柄
 */
MTAIENGINE_API void mtlabai_sub_color_ac_gl_initGL(
    mtlabai_sub_color_ac_gl_handle_t handle);

/**
 * \brief 第三步:运行(需要在GL环境下)
 * \param[in] handle 算法句柄
 * \param[in] inputTexture 输入纹理id
 * \param[in out] outputTexture 输出纹理id
 * \param[in] outputWidth 输出纹理的宽
 * \param[in] outputHeight 输出纹理的高
 * \param[in] cParam AI调色专项参数 MTSubColorACParam
 * \param[in] optionType 模型类型 MTSubColorACOptionType，可进行|操作，同时运行多个模型
 */
MTAIENGINE_API MTSubColorToningEWStatus mtlabai_sub_color_ac_gl_runGL(
    mtlabai_sub_color_ac_gl_handle_t handle, GLuint inputTexture, GLuint outputTexture,
    int outputWidth, int outputHeight, const MTSubColorACParam* cParam, int optionType);

/**
 * \brief 第四步:释放内部GL环境(需要在GL环境下)
 * \param[in] handle 算法句柄
 */
MTAIENGINE_API void mtlabai_sub_color_ac_gl_exitGL(
    mtlabai_sub_color_ac_gl_handle_t handle);
}

#endif  // _MTSUB_COLORACUTIL_GL_H_